# 标准库模块
import traceback
from functools import wraps
from typing import Optional, Dict, Any

# 第三方库模块
from fastapi import Depends, HTTPException, status, Cookie
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# 自定义模块
import 状态
# 用户相关函数从统一的用户模块导入
from 数据.用户 import 异步用户是否存在_id, 异步验证管理员权限
# 导入统一日志系统
from 日志 import 错误日志器, 安全日志器
from 服务.异步用户服务 import 异步认证服务


# -----------------------------
# 权限相关类和常量
# -----------------------------
class 权限错误(Exception):
    """权限验证错误"""
    def __init__(self, 消息: str, 错误码: int = 403):
        self.消息 = 消息
        self.错误码 = 错误码
        super().__init__(self.消息)

class 用户角色:
    """用户角色常量"""
    管理员 = "管理员"
    普通用户 = "普通用户"
    团队负责人 = "团队负责人"
    公司管理员 = "公司管理员"

class 权限代码:
    """权限代码常量"""
    公司管理 = "company_management"
    团队管理 = "team_management"
    用户管理 = "user_management"
    系统设置 = "system_settings"
    数据查看 = "data_view"
    数据编辑 = "data_edit"

# -----------------------------
# 安全配置
# -----------------------------
安全方案 = HTTPBearer(auto_error=False)

# -----------------------------
# 权限检查函数（统一使用is_admin字段）
# -----------------------------
async def 检查管理员权限_统一(当前用户: Dict[str, Any]) -> bool:
    """
    统一检查用户是否具有管理员权限（使用用户表的is_admin字段）
    
    Args:
        当前用户: 当前用户信息字典
        
    Returns:
        bool: 是否为管理员
    """
    # 直接使用已有的异步检查管理员权限函数，避免代码重复
    return await 异步检查管理员权限(当前用户)

def 检查管理员权限_同步(当前用户: Dict[str, Any]) -> bool:
    """
    同步检查用户是否具有管理员权限（装饰器专用，简化版本）
    注意：这个函数只做基本检查，真正的权限验证应该使用异步版本
    
    Args:
        当前用户: 当前用户信息字典
        
    Returns:
        bool: 是否为管理员（基于用户信息中的标识）
    """
    try:
        用户ID = 当前用户.get("id", 0)
        
        # 如果用户信息中已经包含管理员标识，直接使用
        # 注意：这个假设用户信息已经包含了is_admin字段
        if "is_admin" in 当前用户:
            is_admin = 当前用户.get("is_admin", False)
            if is_admin:
                安全日志器.info(f"用户 {用户ID} 管理员权限验证通过（基于用户信息）")
                return True
            else:
                安全日志器.warning(f"用户 {用户ID} 不具备管理员权限（is_admin=False）")
                return False
        
        # 如果用户信息中没有is_admin字段，需要数据库查询
        # 但在同步函数中无法进行异步查询，所以这里返回False并记录警告
        安全日志器.warning(f"用户 {用户ID} 权限信息不完整，缺少is_admin字段，拒绝访问")
        return False
        
    except Exception as e:
        安全日志器.error(f"检查管理员权限时发生错误: {str(e)}")
        return False

def 检查用户权限_同步(当前用户: Dict[str, Any], 需要权限: str, 团队ID: Optional[int] = None) -> bool:
    """
    同步检查用户是否具有指定权限（装饰器专用）
    
    Args:
        当前用户: 当前用户信息字典
        需要权限: 需要的权限代码
        团队ID: 团队ID（可选，用于团队级权限检查）
        
    Returns:
        bool: 是否具有权限
    """
    try:
        用户ID = 当前用户.get("id", 0)
        
        # 管理员拥有所有权限（统一使用is_admin字段）
        if 当前用户.get("is_admin", False):
            安全日志器.info(f"用户 {用户ID} 以管理员身份获得权限 {需要权限}")
            return True
        
        # 这里可以扩展更复杂的权限检查逻辑
        # 例如：从数据库查询用户权限、团队权限等
        
        安全日志器.info(f"用户 {用户ID} 权限检查完成，权限 {需要权限}: 待实现具体逻辑")
        return False
        
    except Exception as e:
        安全日志器.error(f"检查用户权限时发生错误: {str(e)}")
        return False

# -----------------------------
# 权限装饰器
# -----------------------------
def 管理员权限验证(函数=None):
    """
    管理员权限验证装饰器
    
    用法:
        @管理员权限验证
        async def 管理员接口(当前用户: dict = Depends(获取当前用户)):
            pass
    """
    def 装饰器(函数):
        @wraps(函数)
        async def 包装器(*args, **kwargs):
            try:
                # 从参数中获取当前用户
                当前用户 = None
                if "当前用户" in kwargs:
                    当前用户 = kwargs["当前用户"]
                else:
                    # 尝试从args中查找
                    for arg in args:
                        if isinstance(arg, dict) and "id" in arg:
                            当前用户 = arg
                            break
                
                if not 当前用户:
                    安全日志器.error("权限验证装饰器：未找到当前用户信息")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "status": 状态.通用.令牌缺失,
                            "message": "权限验证失败：用户信息缺失"
                        }
                    )
                
                # 使用统一的管理员权限检查
                if not await 检查管理员权限_统一(当前用户):
                    用户ID = 当前用户.get("id", "未知")
                    安全日志器.warning(f"用户 {用户ID} 尝试访问管理员接口但权限不足")
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail={
                            "status": 状态.用户.权限不足,
                            "message": "访问被拒绝：需要管理员权限"
                        }
                    )
                
                # 权限验证通过，执行原函数
                return await 函数(*args, **kwargs)
                
            except HTTPException:
                raise
            except Exception as e:
                安全日志器.error(f"权限验证装饰器执行异常: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "status": 状态.通用.服务器错误,
                        "message": "权限验证过程中发生错误"
                    }
                )
        
        return 包装器
    
    if 函数 is None:
        return 装饰器
    else:
        return 装饰器(函数)

def 权限验证(需要权限: str, 团队ID字段: Optional[str] = None):
    """
    通用权限验证装饰器
    
    Args:
        需要权限: 需要的权限代码
        团队ID字段: 从请求参数中获取团队ID的字段名（可选）
    
    用法:
        @权限验证(权限代码.团队管理, "团队ID")
        async def 团队管理接口(请求数据, 当前用户: dict = Depends(获取当前用户)):
            pass
    """
    def 装饰器(函数):
        @wraps(函数)
        async def 包装器(*args, **kwargs):
            try:
                # 获取当前用户
                当前用户 = kwargs.get("当前用户")
                if not 当前用户:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "status": 状态.通用.令牌缺失,
                            "message": "权限验证失败：用户信息缺失"
                        }
                    )
                
                # 获取团队ID（如果需要）
                团队ID = None
                if 团队ID字段:
                    请求数据 = kwargs.get("请求数据")
                    if 请求数据 and hasattr(请求数据, 团队ID字段):
                        团队ID = getattr(请求数据, 团队ID字段)
                
                # 检查权限
                if not 检查用户权限_同步(当前用户, 需要权限, 团队ID):
                    用户ID = 当前用户.get("id", "未知")
                    安全日志器.warning(f"用户 {用户ID} 尝试访问需要权限 {需要权限} 的接口但权限不足")
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail={
                            "status": 状态.用户.权限不足,
                            "message": f"访问被拒绝：需要权限 {需要权限}"
                        }
                    )
                
                # 权限验证通过，执行原函数
                return await 函数(*args, **kwargs)
                
            except HTTPException:
                raise
            except Exception as e:
                安全日志器.error(f"权限验证装饰器执行异常: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "status": 状态.通用.服务器错误,
                        "message": "权限验证过程中发生错误"
                    }
                )
        
        return 包装器
    return 装饰器

# -----------------------------
# 认证依赖项
# -----------------------------
async def 获取当前用户(
    凭证: Optional[HTTPAuthorizationCredentials] = Depends(安全方案),
    token_from_cookie: Optional[str] = Cookie(None, alias="token")
) -> dict:
    """
    认证依赖项：从Header或Cookie获取并验证Token
    重构版本：消除嵌套try-except，简化异常处理逻辑
    :param 凭证: 包含Bearer Token的认证凭证 (来自Header)
    :param token_from_cookie: 来自名为 'token' 的 Cookie 的令牌字符串
    :return: 用户数据字典
    :raises HTTPException: 401未认证/403无效令牌/404用户不存在/503服务不可用
    """
    token: Optional[str] = None

    try:
        # 1. 获取令牌
        if 凭证 and 凭证.credentials:
            token = 凭证.credentials
            安全日志器.debug("成功从 Authorization 头部获取到令牌。")
        elif token_from_cookie:
            token = token_from_cookie
            安全日志器.debug("成功从名为 'token' 的 Cookie 获取到令牌。")

        if not token:
            安全日志器.warning("认证失败：请求中既未找到 Authorization 头部令牌，也未找到名为 'token' 的 Cookie。")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "status": 状态.通用.令牌缺失,
                    "message": "请求中未提供认证令牌 (Header 或 Cookie)"
                },
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 2. 验证令牌
        认证服务实例 = 异步认证服务()
        解密的数据 = await 认证服务实例.验证令牌(token)

        if 解密的数据.get("status") != 状态.通用.成功_旧:
            安全日志器.warning(f"令牌验证失败: {解密的数据.get('message')}")
            http_status_code = status.HTTP_401_UNAUTHORIZED
            if 解密的数据.get("status") == 状态.通用.令牌已过期:
                pass
            elif 解密的数据.get("status") == 状态.通用.令牌无效:
                pass

            raise HTTPException(
                status_code=http_status_code,
                detail=解密的数据
            )

        # 3. 提取用户信息
        用户数据 = 解密的数据.get("data", {}).get("用户信息")
        if not 用户数据 or "id" not in 用户数据:
            安全日志器.error("令牌解码成功，但用户信息结构不正确或缺少用户ID。")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "status": 状态.通用.令牌内容无效,
                    "message": "令牌中用户信息无效或不完整"
                }
            )

        # 4. 验证用户是否存在于数据库中
        用户存在 = await 异步用户是否存在_id(用户数据["id"])
        if not 用户存在:
            安全日志器.warning(f"令牌有效，但用户ID {用户数据['id']} 在数据库中不存在。")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "status": 状态.用户.用户不存在,
                    "message": "令牌关联的用户不存在"
                }
            )

        安全日志器.debug(f"用户 {用户数据['id']} 认证成功。")
        return 用户数据

    except ConnectionError as conn_error:
        # 数据库连接问题，返回服务不可用状态
        安全日志器.error(f"用户认证时数据库连接异常: {str(conn_error)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "服务暂时不可用，请稍后重试"
            }
        )
    except HTTPException as he:
        # HTTP异常直接重新抛出
        raise he
    except Exception as e:
        # 其他未预期的异常
        错误详情 = traceback.format_exc()
        错误日志器.error(f"认证依赖项 '获取当前用户' 发生意外错误: {str(e)}\n{错误详情}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "认证过程中发生内部服务器错误"
            }
        )

async def 获取当前用户_可选(凭证: Optional[HTTPAuthorizationCredentials] = Depends(安全方案)) -> Optional[dict]:
    """
    尝试获取当前认证的用户，如果Token无效或不存在，则返回None。
    重构版本：简化异常处理逻辑，避免嵌套try-except
    """
    if not 凭证 or not 凭证.credentials:
        return None

    try:
        # 1. 验证令牌
        认证服务实例 = 异步认证服务()
        解密的数据 = await 认证服务实例.验证令牌(凭证.credentials)

        # 检查验证结果
        if 解密的数据.get("status") != 状态.通用.成功_旧:
            安全日志器.debug(f"可选用户认证失败: {解密的数据.get('message')}")
            return None

        # 2. 提取用户信息
        用户数据 = 解密的数据.get("data", {}).get("用户信息")
        if not 用户数据 or "id" not in 用户数据:
            安全日志器.debug("可选用户认证：令牌解码成功，但用户信息结构不正确或缺少用户ID")
            return None

        # 3. 验证用户是否存在于数据库中
        用户存在 = await 异步用户是否存在_id(用户数据["id"])
        if not 用户存在:
            安全日志器.debug(f"可选用户认证：用户ID {用户数据['id']} 在数据库中不存在")
            return None

        安全日志器.debug(f"可选用户认证成功: 用户ID {用户数据['id']}")
        return 用户数据

    except ConnectionError as conn_error:
        # 数据库连接问题时，记录日志但返回None（可选认证失败不应阻断请求）
        安全日志器.debug(f"可选用户认证时数据库连接异常: {str(conn_error)}")
        return None
    except HTTPException as e:
        # 如果是认证错误（例如401, 403），则视为未认证或无权限，返回None
        if e.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]:
            安全日志器.debug(f"可选用户认证：HTTP认证错误 {e.status_code}")
            return None
        raise  # 其他HTTP异常继续抛出
    except Exception as e:
        # 其他异常也视为获取用户失败，但记录警告日志
        错误日志器.warning(f"获取可选当前用户时发生未知错误: {str(e)}")
        return None

async def 获取当前管理员用户(用户: Dict[str, Any] = Depends(获取当前用户)) -> Dict[str, Any]:
    """
    认证依赖项：获取当前用户并验证是否为管理员。
    首先通过JWT获取用户，然后检查其管理员权限。
    """
    # `异步验证管理员权限(用户ID: int)` 接收用户ID
    是管理员 = await 异步验证管理员权限(用户.get('id', 0))

    if not 是管理员:
        安全日志器.warning(f"用户 {用户.get('phone')} (ID: {用户.get('id')}) 尝试访问管理员接口，权限不足。")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "status": 状态.用户.权限不足,
                "message": "权限不足，无法执行此操作",
                "data": None
            }
        )
    安全日志器.info(f"管理员用户 {用户.get('phone')} (ID: {用户.get('id')}) 通过认证。")
    return 用户 