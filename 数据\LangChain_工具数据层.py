"""
LangChain工具数据层
负责处理工具配置、智能体工具关联等数据库操作
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例, Postgre_异步数据库连接池

# 配置日志
工具数据层日志器 = logging.getLogger("LangChain工具数据层")


class LangChain工具数据层:
    """LangChain工具数据层 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于工具数据操作
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据库操作: Optional[Postgre_异步数据库连接池] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据库操作: PostgreSQL连接池实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据库操作永远不为None
        self.数据库操作: Postgre_异步数据库连接池 = 数据库操作 or 异步连接池实例
        self.已初始化 = True  # 简化初始化逻辑

        工具数据层日志器.info("LangChain工具数据层创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain工具数据层":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 创建数据层实例
        return cls(异步连接池实例)
    
    # ==================== 工具配置管理 ====================
    
    async def 获取工具配置列表(self, 工具类型: Optional[str] = None, 启用状态: Optional[bool] = None) -> List[Dict[str, Any]]:
        """获取工具配置列表"""
        try:
            条件列表 = []
            参数列表 = []
            
            参数索引 = 1
            if 工具类型:
                条件列表.append(f"工具类型 = ${参数索引}")
                参数列表.append(工具类型)
                参数索引 += 1

            if 启用状态 is not None:
                条件列表.append(f"启用状态 = ${参数索引}")
                参数列表.append(1 if 启用状态 else 0)
                参数索引 += 1
            
            WHERE子句 = f"WHERE {' AND '.join(条件列表)}" if 条件列表 else ""
            
            查询SQL = f"""
            SELECT
                id, 工具名称, 工具描述, 工具参数, 权限要求,
                安全级别, 启用状态, 超时时间, 重试次数, 使用次数,
                成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_工具配置表
            {WHERE子句}
            ORDER BY 工具名称
            """
            
            结果 = await self.数据库操作.执行查询(查询SQL, tuple(参数列表) if 参数列表 else None)
            
            # 处理数据字段
            工具列表 = []
            for 行 in 结果:
                工具配置 = dict(行)

                # 直接使用字段值，不进行JSON解析
                工具配置['工具参数'] = 工具配置.get('工具参数') or ''
                工具配置['权限要求'] = 工具配置.get('权限要求') or ''

                # 转换布尔值
                工具配置['启用状态'] = bool(工具配置.get('启用状态', 0))

                工具列表.append(工具配置)
            
            工具数据层日志器.debug(f"获取工具配置列表成功，共 {len(工具列表)} 个工具")
            return 工具列表
            
        except Exception as e:
            工具数据层日志器.error(f"获取工具配置列表失败: {str(e)}")
            return []
    
    async def 获取工具配置(self, 工具名称: str) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具名称）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 权限要求,
                安全级别, 启用状态, 超时时间, 重试次数, 使用次数,
                成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE 工具名称 = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            # 直接使用字段值，不进行JSON解析
            工具配置['工具参数'] = 工具配置.get('工具参数') or ''
            工具配置['权限要求'] = 工具配置.get('权限要求') or ''

            # 转换布尔值
            工具配置['启用状态'] = bool(工具配置.get('启用状态', 0))

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置失败 ({工具名称}): {str(e)}")
            return None

    async def 根据ID获取工具配置(self, 工具ID: int) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具ID）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 权限要求,
                安全级别, 启用状态, 超时时间, 重试次数, 使用次数,
                成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE id = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具ID,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            # 直接使用字段值，不进行JSON解析
            工具配置['工具参数'] = 工具配置.get('工具参数') or ''
            工具配置['权限要求'] = 工具配置.get('权限要求') or ''

            # 转换布尔值
            工具配置['启用状态'] = bool(工具配置.get('启用状态', 0))

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(f"根据ID获取工具配置失败 (ID: {工具ID}): {str(e)}")
            return None

    async def 根据名称获取工具配置(self, 工具名称: str) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具名称）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 权限要求,
                安全级别, 启用状态, 超时时间, 重试次数,
                使用次数, 成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE 工具名称 = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            # 转换布尔值
            工具配置['启用状态'] = bool(工具配置.get('启用状态', 0))

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(f"根据名称获取工具配置失败 (名称: {工具名称}): {str(e)}")
            return None

    async def 同步内部函数工具到数据库(self) -> Dict[str, Any]:
        """
        同步内部函数工具到数据库
        在系统启动时调用，确保所有内部函数工具都在数据库中有对应记录
        """
        try:
            工具数据层日志器.info("🔄 开始同步内部函数工具到数据库...")

            # 导入内部函数包装器
            from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

            # 初始化内部函数包装器（如果尚未初始化）
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            # 获取所有内部函数工具
            内部工具字典 = await 内部函数包装器实例.获取可用工具列表()
            工具元数据字典 = await 内部函数包装器实例.获取工具元数据()

            if not 内部工具字典:
                工具数据层日志器.warning("⚠️ 未发现任何内部函数工具")
                return {"success": True, "message": "未发现内部函数工具", "新增": 0, "更新": 0}

            # 获取数据库中已存在的工具（所有工具）
            现有工具查询SQL = "SELECT 工具名称, id FROM langchain_工具配置表"
            现有工具结果 = await self.数据库操作.执行查询(现有工具查询SQL)
            现有工具映射 = {row['工具名称']: row['id'] for row in 现有工具结果} if 现有工具结果 else {}

            新增计数 = 0
            更新计数 = 0

            # 遍历所有内部函数工具
            for 工具名称, 工具实例 in 内部工具字典.items():
                元数据 = 工具元数据字典.get(工具名称, {})

                # 处理工具参数
                工具参数 = None
                if hasattr(工具实例, "args_schema") and 工具实例.args_schema:
                    try:
                        if hasattr(工具实例.args_schema, "model_json_schema"):
                            工具参数 = 工具实例.args_schema.model_json_schema()
                        else:
                            工具参数 = str(工具实例.args_schema)
                    except Exception:
                        工具参数 = None

                工具描述 = getattr(工具实例, "description", "") or 元数据.get("描述", "")

                if 工具名称 in 现有工具映射:
                    # 工具已存在，更新描述和参数（可选）
                    更新SQL = """
                    UPDATE langchain_工具配置表
                    SET 工具描述 = $1, 工具参数 = $2, 更新时间 = CURRENT_TIMESTAMP
                    WHERE 工具名称 = $3
                    """
                    await self.数据库操作.执行更新(
                        更新SQL,
                        (工具描述, json.dumps(工具参数, ensure_ascii=False) if 工具参数 else None, 工具名称)
                    )
                    更新计数 += 1
                    工具数据层日志器.debug(f"🔄 更新工具: {工具名称}")
                else:
                    # 新工具，插入到数据库
                    插入SQL = """
                    INSERT INTO langchain_工具配置表
                    (工具名称, 工具描述, 工具参数, 权限要求, 安全级别, 启用状态, 超时时间, 重试次数, 创建时间, 更新时间)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """
                    # 处理安全级别：将字符串转换为数字
                    安全级别数值 = {"low": 1, "medium": 2, "high": 3}.get(元数据.get("安全级别", "medium"), 2)

                    await self.数据库操作.执行插入(
                        插入SQL,
                        (
                            工具名称,
                            工具描述,
                            json.dumps(工具参数, ensure_ascii=False) if 工具参数 else None,
                            元数据.get("权限要求", ""),
                            安全级别数值,
                            True,  # 默认启用
                            30,    # 默认超时30秒
                            3      # 默认重试3次
                        )
                    )
                    新增计数 += 1
                    工具数据层日志器.info(f"✅ 新增工具: {工具名称}")

            结果消息 = f"同步完成: 新增 {新增计数} 个工具, 更新 {更新计数} 个工具"
            工具数据层日志器.info(f"🎉 {结果消息}")

            return {
                "success": True,
                "message": 结果消息,
                "新增": 新增计数,
                "更新": 更新计数,
                "总计": len(内部工具字典)
            }

        except Exception as e:
            错误消息 = f"同步内部函数工具到数据库失败: {str(e)}"
            工具数据层日志器.error(错误消息, exc_info=True)
            return {"success": False, "message": 错误消息, "新增": 0, "更新": 0}

    async def 创建工具配置(self, 工具配置: Dict[str, Any]) -> Optional[int]:
        """创建工具配置"""
        try:
            工具名称 = 工具配置.get('工具名称')
            if not 工具名称:
                工具数据层日志器.error("工具名称不能为空")
                return None

            # 先检查工具是否已存在
            现有工具 = await self.获取工具配置(工具名称)
            if 现有工具:
                工具数据层日志器.info(f"工具配置已存在: {工具名称} (ID: {现有工具.get('id')})")
                return 现有工具.get('id')

            插入SQL = """
            INSERT INTO langchain_工具配置表
            (工具名称, 工具描述, 工具参数, 权限要求, 安全级别, 启用状态, 超时时间, 重试次数)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
            """

            # 直接使用字符串格式存储
            工具参数 = 工具配置.get('工具参数', '')
            if isinstance(工具参数, dict):
                工具参数 = str(工具参数)

            权限要求 = 工具配置.get('权限要求', '')
            if isinstance(权限要求, list):
                权限要求 = ','.join(权限要求) if 权限要求 else ''
            elif isinstance(权限要求, dict):
                权限要求 = str(权限要求)

            参数 = [
                工具名称,
                工具配置.get('工具描述', ''),
                工具参数,
                权限要求,
                工具配置.get('安全级别', 1),
                1 if 工具配置.get('启用状态', True) else 0,
                工具配置.get('超时时间', 30),
                工具配置.get('重试次数', 3)
            ]

            结果 = await self.数据库操作.执行查询(插入SQL, tuple(参数))
            工具ID = 结果[0]['id'] if 结果 else None

            if 工具ID:
                工具数据层日志器.info(f"✅ 创建工具配置成功: {工具名称} (ID: {工具ID})")
                return 工具ID
            else:
                工具数据层日志器.error(f"❌ 创建工具配置失败: {工具名称}")
                return None

        except Exception as e:
            # 如果是重复键错误，尝试获取现有工具的ID
            if "Duplicate entry" in str(e) and "工具名称" in str(e):
                工具名称 = 工具配置.get('工具名称')
                if 工具名称:
                    工具数据层日志器.info(f"工具配置已存在，获取现有ID: {工具名称}")
                    现有工具 = await self.获取工具配置(工具名称)
                    if 现有工具:
                        return 现有工具.get('id')

            工具数据层日志器.error(f"创建工具配置异常 ({工具配置.get('工具名称')}): {str(e)}")
            return None
    
    async def 更新工具配置(self, 工具名称: str, 更新数据: Dict[str, Any]) -> bool:
        """更新工具配置"""
        try:
            设置子句 = []
            参数列表 = []
            
            允许更新字段 = [
                '工具描述', '工具参数', '权限要求', '安全级别', 
                '启用状态', '超时时间', '重试次数'
            ]
            
            参数索引 = 1
            for 字段 in 允许更新字段:
                if 字段 in 更新数据:
                    设置子句.append(f"{字段} = ${参数索引}")
                    参数索引 += 1

                    值 = 更新数据[字段]
                    if 字段 in ['工具参数', '权限要求']:
                        # 直接存储为字符串
                        if isinstance(值, dict):
                            值 = str(值)
                        elif isinstance(值, list):
                            值 = ','.join(值) if 值 else ''
                        else:
                            值 = str(值) if 值 else ''
                    elif 字段 == '启用状态':
                        值 = 1 if 值 else 0

                    参数列表.append(值)

            if not 设置子句:
                return True

            参数列表.append(工具名称)

            更新SQL = f"""
            UPDATE langchain_工具配置表
            SET {', '.join(设置子句)}, 更新时间 = CURRENT_TIMESTAMP
            WHERE 工具名称 = ${参数索引}
            """

            await self.数据库操作.执行更新(更新SQL, tuple(参数列表))
            工具数据层日志器.info(f"✅ 更新工具配置成功: {工具名称}")
            return True

        except Exception as e:
            工具数据层日志器.error(f"更新工具配置失败 ({工具名称}): {str(e)}")
            return False

    async def 删除工具配置(self, 工具名称: str) -> bool:
        """删除工具配置"""
        try:
            删除SQL = "DELETE FROM langchain_工具配置表 WHERE 工具名称 = $1"
            await self.数据库操作.执行更新(删除SQL, (工具名称,))
            工具数据层日志器.info(f"✅ 删除工具配置成功: {工具名称}")
            return True

        except Exception as e:
            工具数据层日志器.error(f"删除工具配置失败 ({工具名称}): {str(e)}")
            return False
    
    # ==================== 智能体工具关联管理 ====================
    
    async def 获取智能体工具关联(self, 智能体ID: int) -> List[Dict[str, Any]]:
        """获取智能体的工具关联 - 使用工具配置表ID"""
        try:
            查询SQL = """
            SELECT
                a.id,
                a.langchain_智能体配置表id,
                a.langchain_工具配置表id,
                a.工具名称,
                a.工具配置,
                a.启用状态,
                a.创建时间,
                t.工具描述,
                t.工具参数,
                t.权限要求,
                t.安全级别,
                t.超时时间,
                t.重试次数
            FROM langchain_智能体工具关联表 a
            LEFT JOIN langchain_工具配置表 t ON a.langchain_工具配置表id = t.id
            WHERE a.langchain_智能体配置表id = $1 AND t.启用状态 = 1
            ORDER BY a.创建时间
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (智能体ID,))

            关联列表 = []
            for 行 in 结果:
                关联数据 = dict(行)

                # 直接使用字段值，不进行JSON解析
                关联数据['工具配置'] = 关联数据.get('工具配置') or ''
                关联数据['工具参数'] = 关联数据.get('工具参数') or ''
                关联数据['权限要求'] = 关联数据.get('权限要求') or ''

                # 转换布尔值
                关联数据['启用状态'] = bool(关联数据.get('启用状态', 0))

                关联列表.append(关联数据)

            工具数据层日志器.debug(f"获取智能体工具关联成功，智能体 {智能体ID} 有 {len(关联列表)} 个工具")
            return 关联列表

        except Exception as e:
            工具数据层日志器.error(f"获取智能体工具关联失败 (智能体ID: {智能体ID}): {str(e)}")
            return []

    async def 获取单个智能体工具关联(self, 智能体ID: int, 工具名称: str) -> Optional[Dict[str, Any]]:
        """获取智能体的单个工具关联"""
        try:
            查询SQL = """
            SELECT
                a.id,
                a.langchain_智能体配置表id,
                a.langchain_工具配置表id,
                a.工具名称,
                a.工具配置,
                a.启用状态,
                a.创建时间,
                t.工具描述,
                t.工具参数,
                t.权限要求,
                t.安全级别,
                t.超时时间,
                t.重试次数
            FROM langchain_智能体工具关联表 a
            LEFT JOIN langchain_工具配置表 t ON a.langchain_工具配置表id = t.id
            WHERE a.langchain_智能体配置表id = $1 AND a.工具名称 = $2
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (智能体ID, 工具名称))

            if 结果:
                关联数据 = dict(结果[0])

                # 解析JSON字段
                json_字段 = ['工具配置', '工具参数', '权限要求']
                for 字段 in json_字段:
                    if 关联数据.get(字段):
                        try:
                            关联数据[字段] = json.loads(关联数据[字段])
                        except (json.JSONDecodeError, TypeError):
                            if 字段 == '权限要求':
                                关联数据[字段] = []
                            else:
                                关联数据[字段] = {}

                # 转换布尔值
                关联数据['启用状态'] = bool(关联数据.get('启用状态', 0))

                工具数据层日志器.debug(f"获取单个工具关联成功: 智能体{智能体ID} -> {工具名称}")
                return 关联数据
            else:
                工具数据层日志器.debug(f"工具关联不存在: 智能体{智能体ID} -> {工具名称}")
                return None

        except Exception as e:
            工具数据层日志器.error(f"获取单个工具关联失败 (智能体{智能体ID} -> {工具名称}): {str(e)}")
            return None

    async def 根据ID获取工具配置详情(self, 工具配置ID: int) -> Dict[str, Any]:
        """根据工具配置表ID获取工具配置"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数,
                权限要求, 安全级别, 启用状态, 超时时间, 重试次数,
                使用次数, 成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE id = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具配置ID,))

            if 结果:
                工具配置 = dict(结果[0])

                # 直接使用字段值，不进行JSON解析
                工具配置['工具参数'] = 工具配置.get('工具参数') or ''
                工具配置['权限要求'] = 工具配置.get('权限要求') or ''

                # 转换布尔值
                工具配置['启用状态'] = bool(工具配置.get('启用状态', 0))

                工具数据层日志器.debug(f"获取工具配置成功: ID={工具配置ID}, 名称={工具配置.get('工具名称')}")
                return 工具配置
            else:
                工具数据层日志器.warning(f"工具配置不存在: ID={工具配置ID}")
                return {}

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置失败 (ID: {工具配置ID}): {str(e)}")
            return {}

    async def 创建智能体工具关联(self, 智能体ID: int, 工具名称: str, 工具配置: Optional[Dict[str, Any]] = None, 启用状态: bool = True) -> Optional[int]:
        """创建智能体工具关联"""
        try:
            插入SQL = """
            INSERT INTO langchain_智能体工具关联表
            (langchain_智能体配置表id, 工具名称, 工具配置, 启用状态)
            VALUES ($1, $2, $3, $4)
            RETURNING id
            """

            # 直接存储工具配置为字符串
            工具配置_str = str(工具配置) if 工具配置 else ''

            参数 = [
                智能体ID,
                工具名称,
                工具配置_str,
                1 if 启用状态 else 0
            ]

            结果 = await self.数据库操作.执行查询(插入SQL, tuple(参数))
            关联ID = 结果[0]['id'] if 结果 else None

            if 关联ID:
                工具数据层日志器.info(f"✅ 创建智能体工具关联成功: 智能体{智能体ID} -> {工具名称} (ID: {关联ID})")
                return 关联ID
            else:
                工具数据层日志器.error(f"❌ 创建智能体工具关联失败: 智能体{智能体ID} -> {工具名称}")
                return None

        except Exception as e:
            工具数据层日志器.error(f"创建智能体工具关联异常 (智能体{智能体ID} -> {工具名称}): {str(e)}")
            return None

    async def 更新智能体工具关联(self, 智能体ID: int, 工具名称: str, 更新数据: Dict[str, Any]) -> bool:
        """更新智能体工具关联"""
        try:
            设置子句 = []
            参数列表 = []

            参数索引 = 1
            if '工具配置' in 更新数据:
                设置子句.append(f"工具配置 = ${参数索引}")
                参数索引 += 1
                工具配置 = 更新数据['工具配置']
                if isinstance(工具配置, dict):
                    工具配置 = str(工具配置)
                参数列表.append(工具配置)

            if '启用状态' in 更新数据:
                设置子句.append(f"启用状态 = ${参数索引}")
                参数索引 += 1
                参数列表.append(1 if 更新数据['启用状态'] else 0)

            if not 设置子句:
                return True

            参数列表.extend([智能体ID, 工具名称])

            更新SQL = f"""
            UPDATE langchain_智能体工具关联表
            SET {', '.join(设置子句)}
            WHERE langchain_智能体配置表id = ${参数索引} AND 工具名称 = ${参数索引 + 1}
            """

            影响行数 = await self.数据库操作.执行更新(更新SQL, tuple(参数列表))

            if 影响行数 > 0:
                工具数据层日志器.info(f"✅ 更新智能体工具关联成功: 智能体{智能体ID} -> {工具名称}")
                return True
            else:
                工具数据层日志器.warning(f"⚠️ 更新智能体工具关联无影响: 智能体{智能体ID} -> {工具名称}")
                return False

        except Exception as e:
            工具数据层日志器.error(f"更新智能体工具关联失败 (智能体{智能体ID} -> {工具名称}): {str(e)}")
            return False

    async def 删除智能体工具关联(self, 智能体ID: int, 工具名称: Optional[str] = None) -> bool:
        """删除智能体工具关联"""
        try:
            if 工具名称:
                # 删除特定工具关联
                删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1 AND 工具名称 = $2"
                参数 = [智能体ID, 工具名称]
                描述 = f"智能体{智能体ID} -> {工具名称}"
            else:
                # 删除智能体的所有工具关联
                删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1"
                参数 = [智能体ID]
                描述 = f"智能体{智能体ID}的所有工具关联"

            await self.数据库操作.执行更新(删除SQL, tuple(参数))
            工具数据层日志器.info(f"✅ 删除智能体工具关联成功: {描述}")
            return True

        except Exception as e:
            工具数据层日志器.error(f"删除智能体工具关联失败: {str(e)}")
            return False

    async def 批量更新智能体工具关联(self, 智能体ID: int, 工具列表: List[Dict[str, Any]]) -> bool:
        """批量更新智能体工具关联 - 使用工具配置表ID"""
        try:
            # 先删除现有关联
            await self.删除智能体工具关联(智能体ID)

            # 批量插入新关联
            if not 工具列表:
                工具数据层日志器.info(f"✅ 智能体{智能体ID}工具关联已清空")
                return True

            插入SQL = """
            INSERT INTO langchain_智能体工具关联表
            (langchain_智能体配置表id, langchain_工具配置表id, 工具名称, 工具配置, 启用状态)
            VALUES ($1, $2, $3, $4, $5)
            """

            批量参数 = []
            for 工具配置 in 工具列表:
                工具名称 = 工具配置.get('name') or 工具配置.get('工具名称')
                if not 工具名称:
                    continue

                # 检查工具是否启用，默认为True
                启用状态 = 工具配置.get('enabled', True)
                # 如果enabled明确设置为False，则跳过该工具（不创建关联）
                if 启用状态 is False:
                    工具数据层日志器.debug(f"跳过禁用的工具: {工具名称}")
                    continue

                # 根据工具名称获取工具配置表ID
                工具配置ID = await self._获取工具配置ID(工具名称)
                if not 工具配置ID:
                    工具数据层日志器.warning(f"工具配置不存在，跳过: {工具名称}")
                    continue

                参数 = [
                    智能体ID,
                    工具配置ID,
                    工具名称,
                    json.dumps(工具配置, ensure_ascii=False),
                    1  # 只有启用的工具才会被添加到关联表
                ]
                批量参数.append(参数)

            if 批量参数:
                # 转换为元组列表
                批量元组参数 = [tuple(参数) for 参数 in 批量参数]
                # 逐个插入，因为PostgreSQL连接池没有批量插入方法
                成功数量 = 0
                for 参数 in 批量元组参数:
                    try:
                        await self.数据库操作.执行更新(插入SQL, 参数)
                        成功数量 += 1
                    except Exception as e:
                        工具数据层日志器.error(f"插入工具关联失败: {str(e)}")
                工具数据层日志器.info(f"✅ 批量更新智能体工具关联成功: 智能体{智能体ID} 关联{成功数量}个工具")
                return 成功数量 == len(批量参数)
            else:
                工具数据层日志器.info(f"✅ 智能体{智能体ID}无有效工具配置")
                return True

        except Exception as e:
            工具数据层日志器.error(f"批量更新智能体工具关联失败 (智能体{智能体ID}): {str(e)}")
            return False

    async def _获取工具配置ID(self, 工具名称: str) -> Optional[int]:
        """根据工具名称获取工具配置表ID"""
        try:
            查询SQL = "SELECT id FROM langchain_工具配置表 WHERE 工具名称 = $1 AND 启用状态 = 1"
            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if 结果:
                return 结果[0]['id']
            else:
                return None

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置ID失败 ({工具名称}): {str(e)}")
            return None

    # ==================== 工具调用统计 ====================

    async def 记录工具调用(self, 工具名称: str, 成功: bool = True) -> bool:
        """记录工具调用统计"""
        try:
            if 成功:
                更新SQL = """
                UPDATE langchain_工具配置表
                SET 使用次数 = 使用次数 + 1, 成功次数 = 成功次数 + 1, 更新时间 = CURRENT_TIMESTAMP
                WHERE 工具名称 = $1
                """
            else:
                更新SQL = """
                UPDATE langchain_工具配置表
                SET 使用次数 = 使用次数 + 1, 失败次数 = 失败次数 + 1, 更新时间 = CURRENT_TIMESTAMP
                WHERE 工具名称 = $1
                """

            await self.数据库操作.执行更新(更新SQL, (工具名称,))
            工具数据层日志器.debug(f"记录工具调用统计成功: {工具名称} ({'成功' if 成功 else '失败'})")
            return True

        except Exception as e:
            工具数据层日志器.error(f"记录工具调用统计失败 ({工具名称}): {str(e)}")
            return False

    async def 获取工具统计信息(self, 工具名称: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取工具统计信息"""
        try:
            if 工具名称:
                查询SQL = """
                SELECT 工具名称, 使用次数, 成功次数, 失败次数,
                       CASE WHEN 使用次数 > 0 THEN ROUND(成功次数 * 100.0 / 使用次数, 2) ELSE 0 END as 成功率
                FROM langchain_工具配置表
                WHERE 工具名称 = $1
                """
                参数 = [工具名称]
            else:
                查询SQL = """
                SELECT 工具名称, 使用次数, 成功次数, 失败次数,
                       CASE WHEN 使用次数 > 0 THEN ROUND(成功次数 * 100.0 / 使用次数, 2) ELSE 0 END as 成功率
                FROM langchain_工具配置表
                WHERE 启用状态 = 1
                ORDER BY 使用次数 DESC
                """
                参数 = []

            结果 = await self.数据库操作.执行查询(查询SQL, tuple(参数) if 参数 else None)

            统计列表 = []
            for 行 in 结果:
                统计数据 = dict(行)
                统计列表.append(统计数据)

            return 统计列表

        except Exception as e:
            工具数据层日志器.error(f"获取工具统计信息失败: {str(e)}")
            return []

    async def 批量保存智能体工具关联(self, 智能体ID: int, 工具列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量保存智能体工具关联 - 事务安全的工具关联保存接口"""
        try:
            工具数据层日志器.info(f"开始批量保存智能体工具关联: 智能体{智能体ID}, {len(工具列表)}个工具")

            # 准备批量插入数据
            批量插入数据 = []
            成功数量 = 0
            失败数量 = 0
            失败详情 = []

            # 预处理工具列表
            for 工具配置 in 工具列表:
                工具名称 = 工具配置.get('工具名称') or 工具配置.get('name')
                启用状态 = 工具配置.get('enabled', True)
                工具参数 = 工具配置.get('工具配置', {})

                if not 工具名称:
                    失败数量 += 1
                    失败详情.append({"工具": "未知", "原因": "工具名称为空"})
                    continue

                # 只保存启用的工具
                if not 启用状态:
                    工具数据层日志器.debug(f"跳过未启用工具: {工具名称}")
                    continue

                # 准备插入数据
                批量插入数据.append({
                    "智能体ID": 智能体ID,
                    "工具名称": 工具名称,
                    "工具配置": 工具参数,
                    "启用状态": 启用状态
                })

            # 使用现有的事务方法进行批量操作
            事务SQL列表 = []
            事务参数列表 = []

            # 添加删除现有关联的SQL
            删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1"
            事务SQL列表.append(删除SQL)
            事务参数列表.append((智能体ID,))

            # 添加批量插入的SQL
            if 批量插入数据:
                插入SQL = """
                INSERT INTO langchain_智能体工具关联表
                (langchain_智能体配置表id, 工具名称, 工具配置, 启用状态)
                VALUES ($1, $2, $3, $4)
                """

                for 数据 in 批量插入数据:
                    事务SQL列表.append(插入SQL)
                    事务参数列表.append((
                        数据["智能体ID"],
                        数据["工具名称"],
                        json.dumps(数据["工具配置"], ensure_ascii=False) if 数据["工具配置"] else None,
                        1 if 数据["启用状态"] else 0
                    ))

            # 先删除现有关联
            await self.数据库操作.执行更新(事务SQL列表[0], 事务参数列表[0])

            # 然后批量插入新关联
            成功数量 = 0
            失败数量 = 0
            失败详情 = []

            for i, sql in enumerate(事务SQL列表[1:], 1):
                try:
                    await self.数据库操作.执行更新(sql, 事务参数列表[i])
                    成功数量 += 1
                except Exception as e:
                    失败数量 += 1
                    失败详情.append({"工具": 批量插入数据[i-1]["工具名称"], "原因": str(e)})

            工具数据层日志器.info(f"批量操作完成: 删除现有关联，插入{成功数量}个新关联")

            工具数据层日志器.info(f"✅ 批量保存完成: 成功{成功数量}个, 失败{失败数量}个")

            return {
                "success": True,
                "data": {
                    "智能体ID": 智能体ID,
                    "总数量": len(工具列表),
                    "成功数量": 成功数量,
                    "失败数量": 失败数量,
                    "失败详情": 失败详情,
                    "保存时间": datetime.now().isoformat()
                }
            }

        except Exception as e:
            工具数据层日志器.error(f"批量保存智能体工具关联异常 (智能体{智能体ID}): {str(e)}")
            return {
                "success": False,
                "error": f"批量保存失败: {str(e)}"
            }


# 创建全局实例
LangChain工具数据层实例 = LangChain工具数据层()
