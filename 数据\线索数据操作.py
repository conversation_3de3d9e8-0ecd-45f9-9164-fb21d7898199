"""
线索数据操作模块 - PostgreSQL版本
基于PostgreSQL实现的线索相关数据库操作

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的线索查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的关联查询和数据处理
5. 完整的错误处理和日志记录
6. 支持联系方式和线索的关联操作
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


# ==================== 联系方式相关操作 ====================

async def 获取或创建联系方式并返回完整数据(
    内容: str,
    类型: str,
    记录来源: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    获取或创建联系方式记录，返回完整数据

    参数:
        内容: 联系方式内容
        类型: 联系方式类型
        记录来源: 记录来源

    返回:
        联系方式完整数据或None
    """
    try:
        # 首先尝试查找现有记录
        查询SQL = """
        SELECT id, 联系方式, 类型, 来源, 创建时间, 更新时间
        FROM kol.联系方式表
        WHERE 联系方式 = $1 AND 类型 = $2
        LIMIT 1
        """

        现有记录 = await 异步连接池实例.执行查询(查询SQL, (内容, 类型))

        if 现有记录:
            数据库日志器.info(f"找到现有联系方式: ID={现有记录[0]['id']}, 内容={内容}")
            return 现有记录[0]

        # 如果不存在，创建新记录
        插入SQL = """
        INSERT INTO kol.联系方式表 (联系方式, 类型, 来源, 创建时间, 更新时间)
        VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id, 联系方式, 类型, 来源, 创建时间, 更新时间
        """

        新记录 = await 异步连接池实例.执行查询(插入SQL, (内容, 类型, 记录来源))

        if 新记录:
            数据库日志器.info(f"创建新联系方式: ID={新记录[0]['id']}, 内容={内容}")
            return 新记录[0]
        else:
            错误日志器.error(f"创建联系方式失败: 内容={内容}, 类型={类型}")
            return None

    except Exception as e:
        错误日志器.error(f"获取或创建联系方式失败: 内容={内容}, 类型={类型}, 错误={str(e)}")
        return None


async def 根据ID查询联系方式(联系方式ID: int) -> Optional[Dict[str, Any]]:
    """
    根据ID查询联系方式

    参数:
        联系方式ID: 联系方式ID

    返回:
        联系方式信息或None
    """
    try:
        查询SQL = """
        SELECT id, 联系方式, 类型, 来源, 创建时间, 更新时间
        FROM kol.联系方式表
        WHERE id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (联系方式ID,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"查询联系方式失败: ID={联系方式ID}, 错误={str(e)}")
        return None


# ==================== 线索相关操作 ====================

async def 检查线索是否存在_条件化(
    联系方式id: int,
    额外信息: Dict[str, Any],
    检查名称: bool = False
) -> Optional[int]:
    """
    条件化检查线索是否存在

    参数:
        联系方式id: 联系方式ID
        额外信息: 额外信息字典
        检查名称: 是否按名称检查

    返回:
        存在的线索ID或None
    """
    try:
        if 检查名称 and "名称" in 额外信息:
            # 按名称检查
            查询SQL = """
            SELECT id FROM kol.线索表
            WHERE 联系方式id = $1 
            AND JSON_EXTRACT_PATH_TEXT(信息::json, '名称') = $2
            LIMIT 1
            """
            参数 = (联系方式id, 额外信息["名称"])
        else:
            # 按完整信息检查
            额外信息_json = json.dumps(额外信息, ensure_ascii=False, separators=(',', ':'))
            查询SQL = """
            SELECT id FROM kol.线索表
            WHERE 联系方式id = $1 AND 信息 = $2
            LIMIT 1
            """
            参数 = (联系方式id, 额外信息_json)

        结果 = await 异步连接池实例.执行查询(查询SQL, 参数)
        
        if 结果:
            线索ID = 结果[0]["id"]
            数据库日志器.info(f"找到现有线索: ID={线索ID}, 联系方式ID={联系方式id}")
            return 线索ID
        else:
            数据库日志器.debug(f"未找到匹配线索: 联系方式ID={联系方式id}")
            return None

    except Exception as e:
        错误日志器.error(f"检查线索是否存在失败: 联系方式ID={联系方式id}, 错误={str(e)}")
        return None


async def 创建线索(
    联系方式id: int,
    信息_json: str,
    线索来源_param: Optional[str] = None,
    更新用户_param: Optional[int] = None,
    创建时间_param: Optional[datetime] = None,
    更新时间_param: Optional[datetime] = None
) -> Optional[int]:
    """
    创建新线索记录

    参数:
        联系方式id: 联系方式ID
        信息_json: 信息JSON字符串
        线索来源_param: 线索来源
        更新用户_param: 更新用户ID
        创建时间_param: 创建时间
        更新时间_param: 更新时间

    返回:
        新创建的线索ID或None
    """
    try:
        创建时间 = 创建时间_param or datetime.now()
        更新时间 = 更新时间_param or datetime.now()

        插入SQL = """
        INSERT INTO kol.线索表 (
            联系方式id, 信息, 线索来源, 更新用户, 创建时间, 更新时间
        )
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
        """

        结果 = await 异步连接池实例.执行查询(
            插入SQL, 
            (联系方式id, 信息_json, 线索来源_param, 更新用户_param, 创建时间, 更新时间)
        )

        if 结果:
            线索ID = 结果[0]["id"]
            数据库日志器.info(f"创建线索成功: ID={线索ID}, 联系方式ID={联系方式id}")
            return 线索ID
        else:
            错误日志器.error(f"创建线索失败: 联系方式ID={联系方式id}")
            return None

    except Exception as e:
        错误日志器.error(f"创建线索失败: 联系方式ID={联系方式id}, 错误={str(e)}")
        return None


async def 根据id获取线索(线索ID: int) -> Optional[Dict[str, Any]]:
    """
    根据ID获取线索信息

    参数:
        线索ID: 线索ID

    返回:
        线索信息或None
    """
    try:
        查询SQL = """
        SELECT 
            id, 联系方式id, 信息, 线索来源, 更新用户, 创建时间, 更新时间
        FROM kol.线索表
        WHERE id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (线索ID,))
        return 结果[0] if 结果 else None

    except Exception as e:
        错误日志器.error(f"查询线索失败: ID={线索ID}, 错误={str(e)}")
        return None


async def 更新线索信息(
    线索ID: int,
    更新字段: Dict[str, Any],
    更新时间: Optional[datetime] = None
) -> bool:
    """
    更新线索信息

    参数:
        线索ID: 线索ID
        更新字段: 要更新的字段字典
        更新时间: 更新时间

    返回:
        是否更新成功
    """
    try:
        if not 更新字段:
            return True

        更新时间 = 更新时间 or datetime.now()
        更新字段["更新时间"] = 更新时间

        # 构建更新SQL
        字段列表 = []
        参数列表 = []
        参数索引 = 1

        for 字段名, 值 in 更新字段.items():
            字段列表.append(f'"{字段名}" = ${参数索引}')
            参数列表.append(值)
            参数索引 += 1

        更新SQL = f"""
        UPDATE kol.线索表
        SET {', '.join(字段列表)}
        WHERE id = ${参数索引}
        """
        参数列表.append(线索ID)

        影响行数 = await 异步连接池实例.执行更新(更新SQL, 参数列表)

        if 影响行数 > 0:
            数据库日志器.info(f"更新线索成功: ID={线索ID}")
            return True
        else:
            数据库日志器.warning(f"更新线索未影响任何行: ID={线索ID}")
            return False

    except Exception as e:
        错误日志器.error(f"更新线索失败: ID={线索ID}, 错误={str(e)}")
        return False


async def 获取线索详情_含联系方式(线索ID: int) -> Optional[Dict[str, Any]]:
    """
    获取线索详情，包含关联的联系方式信息

    参数:
        线索ID: 线索ID

    返回:
        线索详情（包含联系方式）或None
    """
    try:
        查询SQL = """
        SELECT
            l.id, l.联系方式id, l.信息, l.线索来源, l.更新用户, l.创建时间, l.更新时间,
            c.联系方式, c.类型 as 联系方式类型, c.来源 as 联系方式来源,
            c.创建时间 as 联系方式创建时间, c.更新时间 as 联系方式更新时间
        FROM kol.线索表 l
        LEFT JOIN kol.联系方式表 c ON l.联系方式id = c.id
        WHERE l.id = $1
        """

        结果 = await 异步连接池实例.执行查询(查询SQL, (线索ID,))

        if 结果:
            线索数据 = 结果[0]

            # 构建返回数据
            线索详情 = {
                "id": 线索数据["id"],
                "联系方式id": 线索数据["联系方式id"],
                "信息": 线索数据["信息"],
                "线索来源": 线索数据["线索来源"],
                "更新用户": 线索数据["更新用户"],
                "创建时间": 线索数据["创建时间"],
                "更新时间": 线索数据["更新时间"],
                "关联联系方式": None
            }

            # 如果有联系方式信息，添加到结果中
            if 线索数据["联系方式"]:
                线索详情["关联联系方式"] = {
                    "id": 线索数据["联系方式id"],
                    "内容": 线索数据["联系方式"],
                    "类型": 线索数据["联系方式类型"],
                    "来源": 线索数据["联系方式来源"],
                    "创建时间": 线索数据["联系方式创建时间"],
                    "更新时间": 线索数据["联系方式更新时间"]
                }

            数据库日志器.info(f"获取线索详情成功: ID={线索ID}")
            return 线索详情
        else:
            数据库日志器.warning(f"线索不存在: ID={线索ID}")
            return None

    except Exception as e:
        错误日志器.error(f"获取线索详情失败: ID={线索ID}, 错误={str(e)}")
        return None


async def 分页查询线索列表(
    页码: int = 1,
    每页数量: int = 20,
    筛选_联系方式: Optional[str] = None,
    筛选_线索来源: Optional[str] = None,
    起始id: Optional[int] = None,
    筛选_信息值: Optional[str] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    分页查询线索列表

    参数:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        筛选_联系方式: 按联系方式内容筛选
        筛选_线索来源: 按线索来源筛选
        起始id: 起始ID筛选
        筛选_信息值: 在信息JSON中搜索

    返回:
        (线索列表, 总记录数)
    """
    try:
        # 构建查询条件
        where_条件 = []
        参数列表 = []
        参数索引 = 1

        if 起始id is not None and 起始id > 0:
            where_条件.append(f"l.id > ${参数索引}")
            参数列表.append(起始id)
            参数索引 += 1

        if 筛选_联系方式:
            where_条件.append(f"c.联系方式 ILIKE ${参数索引}")
            参数列表.append(f"%{筛选_联系方式}%")
            参数索引 += 1

        if 筛选_线索来源:
            where_条件.append(f"l.线索来源 = ${参数索引}")
            参数列表.append(筛选_线索来源)
            参数索引 += 1

        if 筛选_信息值:
            where_条件.append(f"l.信息::text ILIKE ${参数索引}")
            参数列表.append(f"%{筛选_信息值}%")
            参数索引 += 1

        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM kol.线索表 l
        LEFT JOIN kol.联系方式表 c ON l.联系方式id = c.id
        {where_clause}
        """

        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT
            l.id, l.联系方式id, l.信息, l.线索来源, l.更新用户, l.创建时间, l.更新时间,
            c.联系方式 as 关联_联系方式内容
        FROM kol.线索表 l
        LEFT JOIN kol.联系方式表 c ON l.联系方式id = c.id
        {where_clause}
        ORDER BY l.创建时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])

        线索列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表)

        数据库日志器.info(f"分页查询线索列表成功，页码: {页码}, 每页: {每页数量}, 总数: {总数}")
        return 线索列表 or [], 总数

    except Exception as e:
        错误日志器.error(f"分页查询线索列表失败，页码: {页码}, 每页: {每页数量}, 错误: {str(e)}")
        return [], 0


async def 删除线索(线索ID: int) -> bool:
    """
    删除线索记录

    参数:
        线索ID: 线索ID

    返回:
        是否删除成功
    """
    try:
        删除SQL = "DELETE FROM kol.线索表 WHERE id = $1"

        影响行数 = await 异步连接池实例.执行更新(删除SQL, (线索ID,))

        if 影响行数 > 0:
            数据库日志器.info(f"删除线索成功: ID={线索ID}")
            return True
        else:
            数据库日志器.warning(f"删除线索未影响任何行: ID={线索ID}")
            return False

    except Exception as e:
        错误日志器.error(f"删除线索失败: ID={线索ID}, 错误={str(e)}")
        return False


async def 批量删除线索(线索ID列表: List[int]) -> Tuple[int, int]:
    """
    批量删除线索记录

    参数:
        线索ID列表: 要删除的线索ID列表

    返回:
        (成功删除数量, 失败数量)
    """
    try:
        if not 线索ID列表:
            return 0, 0

        成功数量 = 0
        失败数量 = 0

        # 使用事务进行批量删除
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.transaction():
                for 线索ID in 线索ID列表:
                    try:
                        删除SQL = "DELETE FROM kol.线索表 WHERE id = $1"
                        影响行数 = await 连接.execute(删除SQL, 线索ID)

                        if 影响行数 > 0:
                            成功数量 += 1
                        else:
                            失败数量 += 1

                    except Exception as e:
                        错误日志器.error(f"删除单个线索失败: ID={线索ID}, 错误={str(e)}")
                        失败数量 += 1

        数据库日志器.info(f"批量删除线索完成: 成功={成功数量}, 失败={失败数量}")
        return 成功数量, 失败数量

    except Exception as e:
        错误日志器.error(f"批量删除线索失败: 错误={str(e)}")
        return 0, len(线索ID列表) if 线索ID列表 else 0


async def 异步提交联系方式(
    联系方式: str,
    类型: str = "微信",
    来源: Optional[str] = None,
    来源用户: Optional[int] = None
) -> Optional[int]:
    """
    提交联系方式（兼容旧接口）

    参数:
        联系方式: 联系方式内容
        类型: 联系方式类型
        来源: 记录来源
        来源用户: 来源用户ID

    返回:
        联系方式ID，失败返回None
    """
    try:
        # 构建记录来源信息
        记录来源 = 来源 or "用户提交"
        if 来源用户:
            记录来源 += f"(用户ID:{来源用户})"

        # 调用获取或创建联系方式函数
        联系方式数据 = await 获取或创建联系方式并返回完整数据(
            内容=联系方式,
            类型=类型,
            记录来源=记录来源
        )

        if 联系方式数据:
            数据库日志器.info(f"提交联系方式成功: {联系方式} ({类型})")
            return 联系方式数据["id"]
        else:
            错误日志器.error(f"提交联系方式失败: {联系方式} ({类型})")
            return None

    except Exception as e:
        错误日志器.error(f"提交联系方式异常: 联系方式={联系方式}, 类型={类型}, 错误={str(e)}")
        return None
