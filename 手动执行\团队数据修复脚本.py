#!/usr/bin/env python3
"""
团队数据修复脚本
用于修复团队成员数不一致和权限缺失问题

运行方式:
python 团队数据修复脚本.py

功能:
1. 修复团队表中当前成员数与实际成员数不一致的问题
2. 为缺少权限的团队成员初始化基础权限
3. 修复成员关联表中加入时间为null的问题
4. 检查和修复团队状态异常的问题

作者: AI助手
创建时间: 2025-06-21
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.团队权限数据 import 初始化负责人权限, 初始化成员权限
from 日志 import 应用日志器, 错误日志器

class 团队数据修复器:
    """团队数据修复器类"""
    
    def __init__(self):
        self.修复统计 = {
            "团队成员数修复": 0,
            "权限初始化": 0,
            "加入时间修复": 0,
            "错误数量": 0
        }
    
    async def 修复团队成员数不一致(self) -> int:
        """修复团队表中当前成员数与实际成员数不一致的问题"""
        try:
            应用日志器.info("开始修复团队成员数不一致问题...")
            
            # 查询所有正常状态的团队
            团队查询SQL = """
            SELECT 
                t.id as 团队ID,
                t.团队名称,
                t.当前成员数 as 记录成员数,
                COUNT(ut.用户ID) as 实际成员数
            FROM 团队表 t
            LEFT JOIN 用户团队关联表 ut ON t.id = ut.团队ID AND ut.状态 = '正常'
            WHERE t.团队状态 = '正常'
            GROUP BY t.id, t.团队名称, t.当前成员数
            HAVING t.当前成员数 != COUNT(ut.用户ID)
            """
            
            不一致团队 = await 异步连接池实例.执行查询(团队查询SQL)
            
            修复数量 = 0
            for 团队 in 不一致团队:
                团队ID = 团队["团队ID"]
                团队名称 = 团队["团队名称"]
                记录成员数 = 团队["记录成员数"]
                实际成员数 = 团队["实际成员数"]
                
                应用日志器.warning(
                    f"发现成员数不一致: 团队《{团队名称}》(ID:{团队ID}) "
                    f"记录={记录成员数}, 实际={实际成员数}"
                )
                
                # 更新团队表中的成员数
                更新SQL = """
                UPDATE 团队表 
                SET 当前成员数 = %s, 更新时间 = %s 
                WHERE id = %s
                """
                
                更新结果 = await 异步连接池实例.执行更新(
                    更新SQL, (实际成员数, datetime.now(), 团队ID)
                )
                
                if 更新结果:
                    应用日志器.info(f"团队成员数修复成功: {团队名称} -> {实际成员数}")
                    修复数量 += 1
                else:
                    错误日志器.error(f"团队成员数修复失败: {团队名称}")
                    self.修复统计["错误数量"] += 1
            
            self.修复统计["团队成员数修复"] = 修复数量
            应用日志器.info(f"团队成员数修复完成: 共修复 {修复数量} 个团队")
            return 修复数量
            
        except Exception as e:
            错误日志器.error(f"修复团队成员数失败: {e}", exc_info=True)
            self.修复统计["错误数量"] += 1
            return 0
    
    async def 修复缺失权限(self) -> int:
        """为缺少权限的团队成员初始化权限"""
        try:
            应用日志器.info("开始修复缺失权限问题...")
            
            # 查询所有正常团队成员，但没有权限记录的用户
            缺失权限查询SQL = """
            SELECT DISTINCT
                ut.用户ID,
                ut.团队ID,
                ut.职位,
                t.团队名称,
                t.创建人ID,
                COALESCE(u.nickname, u.phone, '') as 用户名
            FROM 用户团队关联表 ut
            JOIN 团队表 t ON ut.团队ID = t.id
            LEFT JOIN 用户表 u ON ut.用户ID = u.id
            LEFT JOIN 用户团队权限表 utp ON ut.用户ID = utp.用户ID AND ut.团队ID = utp.团队ID
            WHERE ut.状态 = '正常' 
            AND t.团队状态 = '正常'
            AND utp.用户ID IS NULL
            ORDER BY ut.团队ID, ut.用户ID
            """
            
            缺失权限成员 = await 异步连接池实例.执行查询(缺失权限查询SQL)
            
            修复数量 = 0
            for 成员 in 缺失权限成员:
                用户ID = 成员["用户ID"]
                团队ID = 成员["团队ID"]
                职位 = 成员["职位"]
                团队名称 = 成员["团队名称"]
                用户名 = 成员["用户名"]
                创建人ID = 成员["创建人ID"]
                
                应用日志器.info(
                    f"为用户初始化权限: {用户名}({用户ID}) "
                    f"-> 团队《{团队名称}》({团队ID}) -> 职位:{职位}"
                )
                
                # 根据职位初始化不同的权限
                初始化成功 = False
                if 职位 in ["创建者", "团队负责人"]:
                    初始化成功 = await 初始化负责人权限(团队ID, 用户ID, 创建人ID)
                else:
                    初始化成功 = await 初始化成员权限(团队ID, 用户ID, 创建人ID)
                
                if 初始化成功:
                    应用日志器.info(f"权限初始化成功: {用户名} -> {职位}权限")
                    修复数量 += 1
                else:
                    错误日志器.error(f"权限初始化失败: {用户名}")
                    self.修复统计["错误数量"] += 1
            
            self.修复统计["权限初始化"] = 修复数量
            应用日志器.info(f"权限修复完成: 共为 {修复数量} 个成员初始化权限")
            return 修复数量
            
        except Exception as e:
            错误日志器.error(f"修复缺失权限失败: {e}", exc_info=True)
            self.修复统计["错误数量"] += 1
            return 0
    
    async def 修复加入时间为空(self) -> int:
        """修复成员关联表中加入时间为null的问题"""
        try:
            应用日志器.info("开始修复加入时间为空的问题...")
            
            # 查询加入时间为null的正常成员
            查询SQL = """
            SELECT 
                ut.id,
                ut.用户ID,
                ut.团队ID,
                ut.创建时间,
                ut.处理邀请时间,
                COALESCE(u.昵称, u.phone, '') as 用户名,
                t.团队名称
            FROM 用户团队关联表 ut
            JOIN 团队表 t ON ut.团队ID = t.id
            LEFT JOIN 用户表 u ON ut.用户ID = u.id
            WHERE ut.状态 = '正常' 
            AND ut.加入时间 IS NULL
            ORDER BY ut.团队ID, ut.用户ID
            """
            
            空时间记录 = await 异步连接池实例.执行查询(查询SQL)
            
            修复数量 = 0
            for 记录 in 空时间记录:
                记录ID = 记录["id"]
                用户名 = 记录["用户名"]
                团队名称 = 记录["团队名称"]
                创建时间 = 记录["创建时间"]
                处理邀请时间 = 记录["处理邀请时间"]
                
                # 优先使用处理邀请时间，其次是创建时间，最后是当前时间
                加入时间 = 处理邀请时间 or 创建时间 or datetime.now()
                
                应用日志器.info(
                    f"修复加入时间: {用户名} -> 团队《{团队名称}》 -> {加入时间}"
                )
                
                更新SQL = "UPDATE 用户团队关联表 SET 加入时间 = %s WHERE id = %s"
                更新结果 = await 异步连接池实例.执行更新(更新SQL, (加入时间, 记录ID))
                
                if 更新结果:
                    修复数量 += 1
                else:
                    错误日志器.error(f"修复加入时间失败: {用户名}")
                    self.修复统计["错误数量"] += 1
            
            self.修复统计["加入时间修复"] = 修复数量
            应用日志器.info(f"加入时间修复完成: 共修复 {修复数量} 条记录")
            return 修复数量
            
        except Exception as e:
            错误日志器.error(f"修复加入时间失败: {e}", exc_info=True)
            self.修复统计["错误数量"] += 1
            return 0
    
    async def 检查团队数据完整性(self) -> Dict[str, Any]:
        """检查团队数据完整性"""
        try:
            应用日志器.info("开始检查团队数据完整性...")
            
            检查结果 = {}
            
            # 1. 检查团队状态
            团队状态SQL = """
            SELECT 团队状态, COUNT(*) as 数量
            FROM 团队表
            GROUP BY 团队状态
            """
            团队状态统计 = await 异步连接池实例.执行查询(团队状态SQL)
            检查结果["团队状态分布"] = {行["团队状态"]: 行["数量"] for 行 in 团队状态统计}
            
            # 2. 检查成员状态
            成员状态SQL = """
            SELECT 状态, COUNT(*) as 数量
            FROM 用户团队关联表
            GROUP BY 状态
            """
            成员状态统计 = await 异步连接池实例.执行查询(成员状态SQL)
            检查结果["成员状态分布"] = {行["状态"]: 行["数量"] for 行 in 成员状态统计}
            
            # 3. 检查权限记录
            权限统计SQL = """
            SELECT 
                COUNT(DISTINCT 用户ID) as 有权限用户数,
                COUNT(*) as 权限记录总数
            FROM 用户团队权限表
            WHERE 状态 = '生效'
            """
            权限统计 = await 异步连接池实例.执行查询(权限统计SQL)
            检查结果["权限统计"] = 权限统计[0] if 权限统计 else {}
            
            # 4. 检查数据一致性
            一致性检查SQL = """
            SELECT 
                COUNT(*) as 成员数不一致团队数
            FROM (
                SELECT 
                    t.id,
                    t.当前成员数,
                    COUNT(ut.用户ID) as 实际成员数
                FROM 团队表 t
                LEFT JOIN 用户团队关联表 ut ON t.id = ut.团队ID AND ut.状态 = '正常'
                WHERE t.团队状态 = '正常'
                GROUP BY t.id, t.当前成员数
                HAVING t.当前成员数 != COUNT(ut.用户ID)
            ) as 不一致数据
            """
            一致性结果 = await 异步连接池实例.执行查询(一致性检查SQL)
            检查结果["数据一致性"] = 一致性结果[0] if 一致性结果 else {}
            
            应用日志器.info(f"团队数据完整性检查完成: {检查结果}")
            return 检查结果
            
        except Exception as e:
            错误日志器.error(f"检查团队数据完整性失败: {e}", exc_info=True)
            return {}
    
    async def 执行全面修复(self) -> Dict[str, Any]:
        """执行全面的团队数据修复"""
        应用日志器.info("=" * 50)
        应用日志器.info("开始执行团队数据全面修复")
        应用日志器.info("=" * 50)
        
        开始时间 = datetime.now()
        
        try:
            # 1. 检查修复前的数据状态
            应用日志器.info("步骤1: 检查修复前的数据状态")
            修复前状态 = await self.检查团队数据完整性()
            
            # 2. 修复团队成员数不一致
            应用日志器.info("步骤2: 修复团队成员数不一致")
            await self.修复团队成员数不一致()
            
            # 3. 修复加入时间为空
            应用日志器.info("步骤3: 修复加入时间为空")
            await self.修复加入时间为空()
            
            # 4. 修复缺失权限
            应用日志器.info("步骤4: 修复缺失权限")
            await self.修复缺失权限()
            
            # 5. 检查修复后的数据状态
            应用日志器.info("步骤5: 检查修复后的数据状态")
            修复后状态 = await self.检查团队数据完整性()
            
            结束时间 = datetime.now()
            耗时 = (结束时间 - 开始时间).total_seconds()
            
            # 生成修复报告
            修复报告 = {
                "修复统计": self.修复统计,
                "修复前状态": 修复前状态,
                "修复后状态": 修复后状态,
                "开始时间": 开始时间.isoformat(),
                "结束时间": 结束时间.isoformat(),
                "耗时秒数": 耗时
            }
            
            应用日志器.info("=" * 50)
            应用日志器.info("团队数据修复完成")
            应用日志器.info(f"修复统计: {self.修复统计}")
            应用日志器.info(f"总耗时: {耗时:.2f}秒")
            应用日志器.info("=" * 50)
            
            return 修复报告
            
        except Exception as e:
            错误日志器.error(f"执行团队数据修复失败: {e}", exc_info=True)
            return {"error": str(e), "修复统计": self.修复统计}

async def main():
    """主函数"""
    try:
        应用日志器.info("团队数据修复脚本启动")
        
        修复器 = 团队数据修复器()
        修复报告 = await 修复器.执行全面修复()
        
        # 打印修复报告
        print("\n" + "=" * 60)
        print("团队数据修复报告")
        print("=" * 60)
        print(f"团队成员数修复: {修复报告['修复统计']['团队成员数修复']} 个")
        print(f"权限初始化: {修复报告['修复统计']['权限初始化']} 个")
        print(f"加入时间修复: {修复报告['修复统计']['加入时间修复']} 个")
        print(f"错误数量: {修复报告['修复统计']['错误数量']} 个")
        print(f"总耗时: {修复报告.get('耗时秒数', 0):.2f} 秒")
        print("=" * 60)
        
        if 修复报告['修复统计']['错误数量'] == 0:
            print("✅ 所有修复操作都成功完成！")
        else:
            print("⚠️  部分修复操作出现错误，请查看日志获取详细信息。")
        
        应用日志器.info("团队数据修复脚本完成")
        
    except Exception as e:
        错误日志器.error(f"脚本执行失败: {e}", exc_info=True)
        print(f"❌ 脚本执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 