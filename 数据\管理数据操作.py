"""
管理数据操作模块 - PostgreSQL版本
基于asyncpg实现的系统管理相关数据库操作

功能：
1. 系统配置管理
2. 操作日志管理
3. 权限管理
4. 数据统计和监控
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


# ==================== 系统配置管理 ====================

async def 获取系统配置(配置键: str) -> Optional[str]:
    """
    获取系统配置值
    
    Args:
        配置键: 配置键名
        
    Returns:
        配置值，不存在返回None
    """
    try:
        查询SQL = """
        SELECT 配置值 
        FROM 系统配置表 
        WHERE 配置键 = $1 AND 状态 = '启用'
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, (配置键,))
        
        if 结果:
            配置值 = 结果[0]['配置值']
            数据库日志器.debug(f"获取系统配置成功: {配置键}")
            return 配置值
        else:
            数据库日志器.debug(f"系统配置不存在: {配置键}")
            return None
            
    except Exception as e:
        错误日志器.error(f"获取系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return None


async def 设置系统配置(
    配置键: str, 
    配置值: str, 
    配置描述: Optional[str] = None,
    操作人ID: Optional[int] = None
) -> bool:
    """
    设置系统配置值
    
    Args:
        配置键: 配置键名
        配置值: 配置值
        配置描述: 配置描述
        操作人ID: 操作人ID
        
    Returns:
        是否设置成功
    """
    try:
        # 使用PostgreSQL的UPSERT语法
        upsert_sql = """
        INSERT INTO 系统配置表 (配置键, 配置值, 配置描述, 操作人ID, 创建时间, 更新时间, 状态)
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '启用')
        ON CONFLICT (配置键) 
        DO UPDATE SET
            配置值 = EXCLUDED.配置值,
            配置描述 = COALESCE(EXCLUDED.配置描述, 系统配置表.配置描述),
            操作人ID = EXCLUDED.操作人ID,
            更新时间 = CURRENT_TIMESTAMP,
            状态 = '启用'
        """
        
        await 异步连接池实例.执行插入(upsert_sql, (配置键, 配置值, 配置描述, 操作人ID))
        
        数据库日志器.info(f"设置系统配置成功: {配置键}={配置值}")
        return True
        
    except Exception as e:
        错误日志器.error(f"设置系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return False


async def 获取所有系统配置(
    页码: int = 1,
    每页数量: int = 50
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取所有系统配置
    
    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        
    Returns:
        (配置列表, 总记录数)
    """
    try:
        # 查询总数
        计数SQL = """
        SELECT COUNT(*) as total
        FROM 系统配置表
        WHERE 状态 = '启用'
        """
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL)
        总数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = """
        SELECT 
            配置键, 配置值, 配置描述, 操作人ID, 创建时间, 更新时间,
            u.用户名 as 操作人姓名
        FROM 系统配置表 sc
        LEFT JOIN 用户表 u ON sc.操作人ID = u.id
        WHERE sc.状态 = '启用'
        ORDER BY sc.更新时间 DESC
        LIMIT $1 OFFSET $2
        """
        
        配置列表 = await 异步连接池实例.执行查询(查询SQL, (每页数量, 偏移量))
        
        数据库日志器.debug(f"获取所有系统配置成功，总数: {总数}")
        return 配置列表 or [], 总数
        
    except Exception as e:
        错误日志器.error(f"获取所有系统配置异常: {str(e)}")
        return [], 0


async def 删除系统配置(配置键: str, 操作人ID: int) -> bool:
    """
    删除系统配置（软删除）
    
    Args:
        配置键: 配置键名
        操作人ID: 操作人ID
        
    Returns:
        是否删除成功
    """
    try:
        更新SQL = """
        UPDATE 系统配置表 
        SET 状态 = '禁用', 操作人ID = $1, 更新时间 = CURRENT_TIMESTAMP
        WHERE 配置键 = $2
        """
        
        影响行数 = await 异步连接池实例.执行更新(更新SQL, (操作人ID, 配置键))
        
        if 影响行数 > 0:
            数据库日志器.info(f"删除系统配置成功: {配置键}")
            return True
        else:
            数据库日志器.warning(f"删除系统配置未影响任何行: {配置键}")
            return False
            
    except Exception as e:
        错误日志器.error(f"删除系统配置异常: 配置键={配置键}, 错误={str(e)}")
        return False


# ==================== 操作日志管理 ====================

async def 记录操作日志(
    操作人ID: int,
    操作类型: str,
    操作对象: str,
    操作描述: str,
    操作结果: str = "成功",
    详细信息: Optional[Dict[str, Any]] = None,
    IP地址: Optional[str] = None
) -> bool:
    """
    记录操作日志
    
    Args:
        操作人ID: 操作人ID
        操作类型: 操作类型（增删改查等）
        操作对象: 操作对象（表名、模块名等）
        操作描述: 操作描述
        操作结果: 操作结果（成功/失败）
        详细信息: 详细信息字典
        IP地址: 操作IP地址
        
    Returns:
        是否记录成功
    """
    try:
        详细信息_json = json.dumps(详细信息, ensure_ascii=False) if 详细信息 else None
        
        插入SQL = """
        INSERT INTO 操作日志表 (
            操作人ID, 操作类型, 操作对象, 操作描述, 操作结果,
            详细信息, IP地址, 操作时间
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP
        )
        """
        
        await 异步连接池实例.执行插入(
            插入SQL, 
            (操作人ID, 操作类型, 操作对象, 操作描述, 操作结果, 详细信息_json, IP地址)
        )
        
        数据库日志器.debug(f"记录操作日志成功: {操作类型}-{操作对象}")
        return True
        
    except Exception as e:
        错误日志器.error(f"记录操作日志异常: {操作类型}-{操作对象}, 错误={str(e)}")
        return False


async def 查询操作日志(
    页码: int = 1,
    每页数量: int = 50,
    操作人ID筛选: Optional[int] = None,
    操作类型筛选: Optional[str] = None,
    操作对象筛选: Optional[str] = None,
    开始时间: Optional[datetime] = None,
    结束时间: Optional[datetime] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    查询操作日志
    
    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        操作人ID筛选: 按操作人筛选
        操作类型筛选: 按操作类型筛选
        操作对象筛选: 按操作对象筛选
        开始时间: 开始时间
        结束时间: 结束时间
        
    Returns:
        (日志列表, 总记录数)
    """
    try:
        where_条件 = []
        参数列表 = []
        参数索引 = 1
        
        if 操作人ID筛选:
            where_条件.append(f"ol.操作人ID = ${参数索引}")
            参数列表.append(操作人ID筛选)
            参数索引 += 1
            
        if 操作类型筛选:
            where_条件.append(f"ol.操作类型 = ${参数索引}")
            参数列表.append(操作类型筛选)
            参数索引 += 1
            
        if 操作对象筛选:
            where_条件.append(f"ol.操作对象 ILIKE ${参数索引}")
            参数列表.append(f"%{操作对象筛选}%")
            参数索引 += 1
            
        if 开始时间:
            where_条件.append(f"ol.操作时间 >= ${参数索引}")
            参数列表.append(开始时间)
            参数索引 += 1
            
        if 结束时间:
            where_条件.append(f"ol.操作时间 <= ${参数索引}")
            参数列表.append(结束时间)
            参数索引 += 1
        
        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""
        
        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 操作日志表 ol
        {where_clause}
        """
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL, 参数列表)
        总数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT 
            ol.id, ol.操作人ID, ol.操作类型, ol.操作对象, ol.操作描述,
            ol.操作结果, ol.详细信息, ol.IP地址, ol.操作时间,
            u.用户名 as 操作人姓名
        FROM 操作日志表 ol
        LEFT JOIN 用户表 u ON ol.操作人ID = u.id
        {where_clause}
        ORDER BY ol.操作时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])
        
        日志列表 = await 异步连接池实例.执行查询(查询SQL, 参数列表)
        
        数据库日志器.debug(f"查询操作日志成功，总数: {总数}")
        return 日志列表 or [], 总数
        
    except Exception as e:
        错误日志器.error(f"查询操作日志异常: {str(e)}")
        return [], 0


# ==================== 权限管理 ====================

async def 检查用户权限(
    用户ID: int,
    权限代码: str,
    资源ID: Optional[int] = None
) -> bool:
    """
    检查用户是否具有指定权限

    Args:
        用户ID: 用户ID
        权限代码: 权限代码
        资源ID: 资源ID（可选）

    Returns:
        是否具有权限
    """
    try:
        # 检查用户角色权限
        角色权限SQL = """
        SELECT COUNT(*) as count
        FROM 用户角色表 ur
        INNER JOIN 角色权限表 rp ON ur.角色ID = rp.角色ID
        INNER JOIN 权限表 p ON rp.权限ID = p.id
        WHERE ur.用户ID = $1 AND p.权限代码 = $2
        AND ur.状态 = '正常' AND rp.状态 = '正常' AND p.状态 = '启用'
        """

        角色权限结果 = await 异步连接池实例.执行查询(角色权限SQL, (用户ID, 权限代码))
        角色权限数 = 角色权限结果[0]["count"] if 角色权限结果 else 0

        if 角色权限数 > 0:
            数据库日志器.debug(f"用户{用户ID}通过角色具有权限{权限代码}")
            return True

        # 检查用户直接权限
        直接权限SQL = """
        SELECT COUNT(*) as count
        FROM 用户权限表 up
        INNER JOIN 权限表 p ON up.权限ID = p.id
        WHERE up.用户ID = $1 AND p.权限代码 = $2
        AND up.状态 = '正常' AND p.状态 = '启用'
        """

        if 资源ID:
            直接权限SQL += " AND (up.资源ID IS NULL OR up.资源ID = $3)"
            参数 = (用户ID, 权限代码, 资源ID)
        else:
            参数 = (用户ID, 权限代码)

        直接权限结果 = await 异步连接池实例.执行查询(直接权限SQL, 参数)
        直接权限数 = 直接权限结果[0]["count"] if 直接权限结果 else 0

        有权限 = 直接权限数 > 0

        if 有权限:
            数据库日志器.debug(f"用户{用户ID}直接具有权限{权限代码}")
        else:
            数据库日志器.debug(f"用户{用户ID}不具有权限{权限代码}")

        return 有权限

    except Exception as e:
        错误日志器.error(f"检查用户权限异常: 用户ID={用户ID}, 权限={权限代码}, 错误={str(e)}")
        return False


async def 获取用户权限列表(用户ID: int) -> List[Dict[str, Any]]:
    """
    获取用户所有权限列表

    Args:
        用户ID: 用户ID

    Returns:
        权限列表
    """
    try:
        # 获取角色权限
        角色权限SQL = """
        SELECT DISTINCT p.id, p.权限代码, p.权限名称, p.权限描述, '角色权限' as 权限来源,
               r.角色名称 as 来源详情
        FROM 用户角色表 ur
        INNER JOIN 角色权限表 rp ON ur.角色ID = rp.角色ID
        INNER JOIN 权限表 p ON rp.权限ID = p.id
        INNER JOIN 角色表 r ON ur.角色ID = r.id
        WHERE ur.用户ID = $1
        AND ur.状态 = '正常' AND rp.状态 = '正常' AND p.状态 = '启用'
        """

        角色权限 = await 异步连接池实例.执行查询(角色权限SQL, (用户ID,))

        # 获取直接权限
        直接权限SQL = """
        SELECT p.id, p.权限代码, p.权限名称, p.权限描述, '直接权限' as 权限来源,
               CASE WHEN up.资源ID IS NOT NULL THEN CONCAT('资源ID:', up.资源ID) ELSE '全局' END as 来源详情
        FROM 用户权限表 up
        INNER JOIN 权限表 p ON up.权限ID = p.id
        WHERE up.用户ID = $1
        AND up.状态 = '正常' AND p.状态 = '启用'
        """

        直接权限 = await 异步连接池实例.执行查询(直接权限SQL, (用户ID,))

        # 合并权限列表
        所有权限 = (角色权限 or []) + (直接权限 or [])

        数据库日志器.debug(f"获取用户权限列表成功: 用户ID={用户ID}, 权限数={len(所有权限)}")
        return 所有权限

    except Exception as e:
        错误日志器.error(f"获取用户权限列表异常: 用户ID={用户ID}, 错误={str(e)}")
        return []


# ==================== 数据统计和监控 ====================

async def 获取系统统计信息() -> Dict[str, Any]:
    """
    获取系统统计信息

    Returns:
        统计信息字典
    """
    try:
        统计信息 = {}

        # 用户统计
        用户统计SQL = """
        SELECT
            COUNT(*) as 总用户数,
            COUNT(CASE WHEN 状态 = '正常' THEN 1 END) as 正常用户数,
            COUNT(CASE WHEN 状态 = '禁用' THEN 1 END) as 禁用用户数,
            COUNT(CASE WHEN DATE(创建时间) = CURRENT_DATE THEN 1 END) as 今日新增用户
        FROM 用户表
        """

        用户统计 = await 异步连接池实例.执行查询(用户统计SQL)
        统计信息["用户统计"] = 用户统计[0] if 用户统计 else {}

        # 团队统计
        团队统计SQL = """
        SELECT
            COUNT(*) as 总团队数,
            COUNT(CASE WHEN 团队状态 = '正常' THEN 1 END) as 正常团队数,
            SUM(当前成员数) as 总成员数,
            AVG(当前成员数) as 平均成员数
        FROM 团队表
        WHERE 团队状态 != '已删除'
        """

        团队统计 = await 异步连接池实例.执行查询(团队统计SQL)
        统计信息["团队统计"] = 团队统计[0] if 团队统计 else {}

        # 操作日志统计
        日志统计SQL = """
        SELECT
            COUNT(*) as 总操作数,
            COUNT(CASE WHEN DATE(操作时间) = CURRENT_DATE THEN 1 END) as 今日操作数,
            COUNT(CASE WHEN 操作时间 >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as 近7日操作数,
            COUNT(DISTINCT 操作人ID) as 活跃用户数
        FROM 操作日志表
        WHERE 操作时间 >= CURRENT_DATE - INTERVAL '30 days'
        """

        日志统计 = await 异步连接池实例.执行查询(日志统计SQL)
        统计信息["操作统计"] = 日志统计[0] if 日志统计 else {}

        # 系统配置统计
        配置统计SQL = """
        SELECT
            COUNT(*) as 总配置数,
            COUNT(CASE WHEN 状态 = '启用' THEN 1 END) as 启用配置数,
            COUNT(CASE WHEN 状态 = '禁用' THEN 1 END) as 禁用配置数
        FROM 系统配置表
        """

        配置统计 = await 异步连接池实例.执行查询(配置统计SQL)
        统计信息["配置统计"] = 配置统计[0] if 配置统计 else {}

        数据库日志器.debug("获取系统统计信息成功")
        return 统计信息

    except Exception as e:
        错误日志器.error(f"获取系统统计信息异常: {str(e)}")
        return {}


async def 获取数据库性能指标() -> Dict[str, Any]:
    """
    获取数据库性能指标

    Returns:
        性能指标字典
    """
    try:
        性能指标 = {}

        # 连接池状态
        连接池状态 = {
            "最大连接数": 异步连接池实例._pool.get_max_size() if hasattr(异步连接池实例, '_pool') else 0,
            "当前连接数": 异步连接池实例._pool.get_size() if hasattr(异步连接池实例, '_pool') else 0,
            "空闲连接数": 异步连接池实例._pool.get_idle_size() if hasattr(异步连接池实例, '_pool') else 0
        }
        性能指标["连接池状态"] = 连接池状态

        # 数据库大小统计
        数据库大小SQL = """
        SELECT
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as 表大小,
            pg_total_relation_size(schemaname||'.'||tablename) as 表大小字节
        FROM pg_tables
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
        """

        表大小统计 = await 异步连接池实例.执行查询(数据库大小SQL)
        性能指标["表大小统计"] = 表大小统计 or []

        数据库日志器.debug("获取数据库性能指标成功")
        return 性能指标

    except Exception as e:
        错误日志器.error(f"获取数据库性能指标异常: {str(e)}")
        return {}
