"""
LangChain知识库服务
负责知识库的CRUD操作、文档管理、向量化、检索等功能
"""

import json
import os
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

# 数据层
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据.LangChain_数据层 import LangChain数据层实例, LangChain数据层

# 服务层
from 服务.LangChain_文档处理器 import LangChain文档处理器实例
from 服务.LangChain_RAG引擎 import RAG引擎实例

# 日志系统
from 日志 import 应用日志器

知识库服务日志器 = 应用日志器


class LangChain知识库服务:
    """LangChain知识库统一服务 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于知识库业务逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据层: Optional[LangChain数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据层: LangChain数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据层永远不为None
        self.数据层: LangChain数据层 = 数据层 or LangChain数据层实例
        self.已初始化 = True  # 简化初始化逻辑
        self.初始化时间 = datetime.now()

        知识库服务日志器.info("LangChain知识库服务创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain知识库服务":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 确保数据层已初始化
        if not LangChain数据层实例.已初始化:
            await LangChain数据层实例.初始化()

        # 创建服务实例
        return cls(LangChain数据层实例)

    # ==================== 知识库管理相关方法 ====================

    async def 获取知识库列表(self, 查询参数: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取知识库列表"""
        try:
            if 查询参数 is None:
                查询参数 = {}

            # 构建筛选条件
            筛选条件 = {}
            if 查询参数.get("搜索关键词"):
                筛选条件["搜索关键词"] = 查询参数["搜索关键词"]
            if 查询参数.get("知识库类型"):
                筛选条件["知识库类型"] = 查询参数["知识库类型"]
            if 查询参数.get("创建者id"):
                筛选条件["创建者id"] = 查询参数["创建者id"]

            # 构建分页参数
            分页参数 = {
                "页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

            # 调用数据层获取知识库列表
            知识库列表, 总数量 = await self.数据层.获取知识库列表(筛选条件, 分页参数)

            return {
                "success": True,
                "知识库列表": 知识库列表,
                "总数量": 总数量,
                "当前页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

        except Exception as e:
            知识库服务日志器.error(f"获取知识库列表失败: {str(e)}")
            return {"success": False, "error": f"获取知识库列表失败: {str(e)}"}

    async def 获取知识库详情(self, 知识库ID: int) -> Dict[str, Any]:
        """获取知识库详情（包含统计信息）"""
        try:
            知识库详情 = await self.数据层.获取知识库详情(知识库ID)

            if 知识库详情:
                # 获取统计信息
                try:
                    统计信息 = await self.数据层.获取知识库统计信息(知识库ID)
                    # 合并统计信息到知识库详情
                    知识库详情.update(
                        {
                            "total_chunks": 统计信息.get("总分块数量", 0),
                            "total_vectors": 统计信息.get(
                                "总分块数量", 0
                            ),  # 向量数等于分块数
                            "processed_docs": 统计信息.get("已处理文档数", 0),
                            "processing_docs": 统计信息.get("处理中文档数", 0),
                            "failed_docs": 统计信息.get("失败文档数", 0),
                            "total_file_size": 统计信息.get("总文件大小", 0),
                            "avg_file_size": 统计信息.get("平均文件大小", 0),
                        }
                    )
                except Exception as stats_error:
                    知识库服务日志器.warning(f"获取统计信息失败: {str(stats_error)}")
                    # 如果统计信息获取失败，设置默认值
                    知识库详情.update(
                        {
                            "total_chunks": 0,
                            "total_vectors": 0,
                            "processed_docs": 0,
                            "processing_docs": 0,
                            "failed_docs": 0,
                            "total_file_size": 0,
                            "avg_file_size": 0,
                        }
                    )

                return {"success": True, "data": 知识库详情}
            else:
                return {"success": False, "error": "知识库不存在"}

        except Exception as e:
            知识库服务日志器.error(f"获取知识库详情失败: {str(e)}")
            return {"success": False, "error": f"获取知识库详情失败: {str(e)}"}

    async def 创建知识库(self, 知识库数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建知识库 - 专注于文档存储和向量检索"""
        try:
            # 设置默认嵌入模型（如果未指定）
            if not 知识库数据.get("嵌入模型ID"):
                默认模型结果 = await self.获取默认嵌入模型()
                if 默认模型结果["success"]:
                    知识库数据["嵌入模型ID"] = 默认模型结果["data"]["id"]
                    知识库服务日志器.info(
                        f"使用默认嵌入模型: {默认模型结果['data']['显示名称']}"
                    )
                else:
                    return {"success": False, "error": "没有可用的嵌入模型"}

            # 移除不属于知识库的字段（这些应该在智能体配置中）
            知识库数据.pop("AI模型ID", None)
            知识库数据.pop("系统提示词", None)
            知识库数据.pop("用户提示词模板", None)
            知识库数据.pop("自动编译", None)
            知识库数据.pop("版本控制", None)

            # 创建知识库
            知识库ID = await self.数据层.创建知识库(知识库数据)

            if 知识库ID:
                return {"success": True, "data": {"知识库ID": 知识库ID}}
            else:
                return {"success": False, "error": "知识库创建失败"}

        except Exception as e:
            知识库服务日志器.error(f"创建知识库失败: {str(e)}")
            return {"success": False, "error": f"创建知识库失败: {str(e)}"}

    async def 更新知识库(
        self, 知识库ID: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新知识库"""
        try:
            更新成功 = await self.数据层.更新知识库(知识库ID, 更新数据)

            if 更新成功:
                return {"success": True, "message": "知识库更新成功"}
            else:
                return {"success": False, "error": "知识库更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"更新知识库失败: {str(e)}")
            return {"success": False, "error": f"更新知识库失败: {str(e)}"}

    async def 删除知识库(self, 知识库ID: int) -> Dict[str, Any]:
        """删除知识库"""
        try:
            删除成功 = await self.数据层.删除知识库(知识库ID)

            if 删除成功:
                return {"success": True, "message": "知识库删除成功"}
            else:
                return {"success": False, "error": "知识库删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"删除知识库失败: {str(e)}")
            return {"success": False, "error": f"删除知识库失败: {str(e)}"}

    async def 克隆知识库(
        self, 源知识库ID: int, 新知识库名称: str, 创建者ID: int
    ) -> Dict[str, Any]:
        """克隆知识库"""
        try:
            # 获取源知识库详情
            源知识库 = await self.数据层.获取知识库详情(源知识库ID)
            if not 源知识库:
                return {"success": False, "error": "源知识库不存在"}

            # 构建新知识库数据
            新知识库数据 = {
                "知识库名称": 新知识库名称,
                "知识库描述": f"克隆自: {源知识库['知识库名称']}",
                "创建者id": 创建者ID,
                "组织id": 源知识库.get("组织id", 1),
                "向量维度": 源知识库.get("向量维度", 1536),
                "嵌入模型": 源知识库.get("嵌入模型", "text-embedding-ada-002"),
                "配置信息": 源知识库.get("配置信息", {}),
            }

            # 创建新知识库
            新知识库ID = await self.数据层.创建知识库(新知识库数据)

            if 新知识库ID:
                # TODO: 复制文档数据（如果需要）
                知识库服务日志器.info(f"知识库克隆成功: {源知识库ID} -> {新知识库ID}")
                return {
                    "success": True,
                    "知识库ID": 新知识库ID,
                    "message": "知识库克隆成功",
                }
            else:
                return {"success": False, "error": "知识库克隆失败"}

        except Exception as e:
            知识库服务日志器.error(f"克隆知识库失败: {str(e)}")
            return {"success": False, "error": f"克隆知识库失败: {str(e)}"}

    # ==================== 文档管理相关方法 ====================

    async def 获取知识库文档列表(
        self, 知识库ID: int, 查询参数: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """获取知识库文档列表"""
        try:
            if 查询参数 is None:
                查询参数 = {}

            # 构建分页参数
            分页参数 = {
                "页码": 查询参数.get("页码", 1),
                "每页数量": 查询参数.get("每页数量", 10),
            }

            # 构建筛选条件
            筛选条件 = {}
            if 查询参数.get("搜索关键字"):
                筛选条件["搜索关键字"] = 查询参数["搜索关键字"]
            if 查询参数.get("文件类型"):
                筛选条件["文件类型"] = 查询参数["文件类型"]
            if 查询参数.get("处理状态"):
                筛选条件["处理状态"] = 查询参数["处理状态"]

            知识库服务日志器.debug(
                f"调用数据层获取文档列表: 知识库ID={知识库ID}, 分页参数={分页参数}, 筛选条件={筛选条件}"
            )

            # 调用数据层获取文档列表
            文档列表, 总数量 = await self.数据层.获取知识库文档列表(
                知识库ID, 分页参数, 筛选条件
            )

            # 计算总页数
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 10)
            总页数 = (总数量 + 每页数量 - 1) // 每页数量 if 总数量 > 0 else 1

            return {
                "success": True,
                "文档列表": 文档列表,
                "总数量": 总数量,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": 总页数,
            }

        except Exception as e:
            知识库服务日志器.error(f"获取知识库文档列表失败: {str(e)}")
            return {"success": False, "error": f"获取知识库文档列表失败: {str(e)}"}

    async def 通过ID获取文档详情(self, 文档记录ID: int) -> Dict[str, Any]:
        """通过数字ID获取文档详情"""
        try:
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录ID)

            if 文档详情:
                return {"success": True, "data": 文档详情}
            else:
                return {"success": False, "error": "文档不存在"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID获取文档详情失败: {str(e)}")
            return {"success": False, "error": f"通过ID获取文档详情失败: {str(e)}"}

    async def 通过UUID获取文档详情(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID获取文档详情"""
        try:
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if 文档详情:
                return {"success": True, "data": 文档详情}
            else:
                return {"success": False, "error": "文档不存在"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID获取文档详情失败: {str(e)}")
            return {"success": False, "error": f"通过UUID获取文档详情失败: {str(e)}"}

    async def 通过ID预览文档(
        self, 文档记录ID: int, 预览参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过数字ID预览文档"""
        try:
            # 先获取文档详情
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录ID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档内容并进行预览处理
            文档内容 = 文档详情.get("文档内容", "")
            最大长度 = 预览参数.get("最大长度", 5000)
            包含元数据 = 预览参数.get("包含元数据", True)

            # 截取内容
            预览内容 = 文档内容[:最大长度] if len(文档内容) > 最大长度 else 文档内容

            预览数据 = {
                "文档id": 文档详情.get("文档id"),
                "文档名称": 文档详情.get("文档名称"),
                "文档类型": 文档详情.get("文档类型"),
                "文档大小": 文档详情.get("文档大小"),
                "预览内容": 预览内容,
                "内容长度": len(文档内容),
                "预览长度": len(预览内容),
                "是否截断": len(文档内容) > 最大长度,
            }

            if 包含元数据:
                预览数据.update(
                    {
                        "创建时间": 文档详情.get("创建时间"),
                        "更新时间": 文档详情.get("更新时间"),
                        "状态": 文档详情.get("状态"),
                        "分块数量": 文档详情.get("分块数量"),
                    }
                )

            return {"success": True, "data": 预览数据}

        except Exception as e:
            知识库服务日志器.error(f"通过ID预览文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID预览文档失败: {str(e)}"}

    async def 通过UUID预览文档(
        self, 文档UUID: str, 预览参数: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过UUID预览文档"""
        try:
            # 先获取文档详情
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档内容并进行预览处理
            文档内容 = 文档详情.get("文档内容", "")
            最大长度 = 预览参数.get("最大长度", 5000)
            包含元数据 = 预览参数.get("包含元数据", True)

            # 截取内容
            预览内容 = 文档内容[:最大长度] if len(文档内容) > 最大长度 else 文档内容

            预览数据 = {
                "文档id": 文档详情.get("文档id"),
                "文档名称": 文档详情.get("文档名称"),
                "文档类型": 文档详情.get("文档类型"),
                "文档大小": 文档详情.get("文档大小"),
                "预览内容": 预览内容,
                "内容长度": len(文档内容),
                "预览长度": len(预览内容),
                "是否截断": len(文档内容) > 最大长度,
            }

            if 包含元数据:
                预览数据.update(
                    {
                        "创建时间": 文档详情.get("创建时间"),
                        "更新时间": 文档详情.get("更新时间"),
                        "状态": 文档详情.get("状态"),
                        "分块数量": 文档详情.get("分块数量"),
                    }
                )

            return {"success": True, "data": 预览数据}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID预览文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID预览文档失败: {str(e)}"}

    async def 通过ID更新文档(
        self, 文档记录ID: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过数字ID更新文档"""
        try:
            # 先获取文档详情确认存在
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录ID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档UUID用于更新
            文档UUID = 文档详情.get("文档id")
            if not 文档UUID:
                return {"success": False, "error": "文档UUID不存在"}

            # 调用数据层更新文档内容
            更新成功 = await self.数据层.更新文档内容(文档UUID, 更新数据)

            if 更新成功:
                return {
                    "success": True,
                    "message": "文档更新成功",
                    "data": {"文档记录ID": 文档记录ID, "文档UUID": 文档UUID},
                }
            else:
                return {"success": False, "error": "文档更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID更新文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID更新文档失败: {str(e)}"}

    async def 通过UUID更新文档(
        self, 文档UUID: str, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """通过UUID更新文档"""
        try:
            # 调用数据层更新文档内容
            更新成功 = await self.数据层.更新文档内容(文档UUID, 更新数据)

            if 更新成功:
                return {
                    "success": True,
                    "message": "文档更新成功",
                    "data": {"文档UUID": 文档UUID},
                }
            else:
                return {"success": False, "error": "文档更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID更新文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID更新文档失败: {str(e)}"}

    async def 重新处理文档(self, 文档UUID: str) -> Dict[str, Any]:
        """重新处理文档（统一方法）"""
        try:
            # 调用文档处理器重新处理文档
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 重新处理文档
            处理结果 = await LangChain文档处理器实例.重新处理文档(文档UUID)

            if 处理结果.get("success"):
                return {
                    "success": True,
                    "message": "文档重新处理已开始",
                    "data": {"文档UUID": 文档UUID},
                }
            else:
                return {
                    "success": False,
                    "error": 处理结果.get("error", "重新处理文档失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"重新处理文档失败: {str(e)}")
            return {"success": False, "error": f"重新处理文档失败: {str(e)}"}

    async def 通过ID下载文档(self, 文档记录ID: int) -> Dict[str, Any]:
        """通过数字ID下载文档"""
        try:
            # 先获取文档详情确认存在
            文档详情 = await self.数据层.通过ID获取文档详情(文档记录ID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档路径和内容
            文档路径 = 文档详情.get("文档路径")
            文档名称 = 文档详情.get("文档名称")
            文档内容 = 文档详情.get("文档内容")

            if not 文档内容:
                return {"success": False, "error": "文档内容为空"}

            # 返回文档下载信息
            return {
                "success": True,
                "data": {
                    "文档记录ID": 文档记录ID,
                    "文档名称": 文档名称,
                    "文档路径": 文档路径,
                    "文档内容": 文档内容,
                    "文件大小": len(文档内容.encode("utf-8")) if 文档内容 else 0,
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"通过ID下载文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID下载文档失败: {str(e)}"}

    async def 通过UUID下载文档(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID下载文档"""
        try:
            # 获取文档详情
            文档详情 = await self.数据层.通过UUID获取文档详情(文档UUID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 获取文档路径和内容
            文档路径 = 文档详情.get("文档路径")
            文档名称 = 文档详情.get("文档名称")
            文档内容 = 文档详情.get("文档内容")

            if not 文档内容:
                return {"success": False, "error": "文档内容为空"}

            # 返回文档下载信息
            return {
                "success": True,
                "data": {
                    "文档UUID": 文档UUID,
                    "文档名称": 文档名称,
                    "文档路径": 文档路径,
                    "文档内容": 文档内容,
                    "文件大小": len(文档内容.encode("utf-8")) if 文档内容 else 0,
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"通过UUID下载文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID下载文档失败: {str(e)}"}

    async def 批量删除文档(self, 文档ID列表: List[Union[int, str]]) -> Dict[str, Any]:
        """批量删除文档（智能识别ID类型）"""
        try:
            成功数量 = 0
            失败数量 = 0
            成功列表 = []
            失败列表 = []

            for 文档ID in 文档ID列表:
                try:
                    # 智能识别ID类型
                    if isinstance(文档ID, str) and len(文档ID) > 10:
                        # 长字符串，可能是UUID
                        删除结果 = await self.通过UUID删除知识库文档(文档ID)
                    else:
                        # 数字或短字符串，当作记录ID处理
                        try:
                            文档记录ID = int(文档ID)
                            删除结果 = await self.通过ID删除知识库文档(文档记录ID)
                        except (ValueError, TypeError):
                            # 如果转换失败，尝试作为UUID处理
                            删除结果 = await self.通过UUID删除知识库文档(str(文档ID))

                    if 删除结果.get("success"):
                        成功数量 += 1
                        成功列表.append(文档ID)
                    else:
                        失败数量 += 1
                        失败列表.append(
                            {
                                "文档ID": 文档ID,
                                "错误": 删除结果.get("error", "删除失败"),
                            }
                        )

                except Exception as e:
                    失败数量 += 1
                    失败列表.append({"文档ID": 文档ID, "错误": str(e)})
                    知识库服务日志器.error(f"删除文档失败: {文档ID}, 错误: {str(e)}")

            知识库服务日志器.info(
                f"批量删除文档完成，成功: {成功数量}, 失败: {失败数量}"
            )

            return {
                "success": True,
                "总数量": len(文档ID列表),
                "成功数量": 成功数量,
                "失败数量": 失败数量,
                "成功列表": 成功列表,
                "失败列表": 失败列表,
            }

        except Exception as e:
            知识库服务日志器.error(f"批量删除文档失败: {str(e)}")
            return {"success": False, "error": f"批量删除文档失败: {str(e)}"}

    async def 上传文档到知识库(
        self,
        知识库ID: int,
        文件路径: str,
        文件名: str,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ) -> Dict[str, Any]:
        """上传文档到知识库 - 优化版本，基于Context7最佳实践"""
        文档UUID = str(uuid.uuid4())
        文档记录ID = None

        try:
            知识库服务日志器.info(f"🚀 开始处理文档上传: {文件名} -> 知识库 {知识库ID}")

            # 1. 验证文件存在性和可读性
            if not os.path.exists(文件路径):
                return {"success": False, "error": f"文件不存在: {文件路径}"}

            if not os.access(文件路径, os.R_OK):
                return {"success": False, "error": f"文件不可读: {文件路径}"}

            文件大小 = os.path.getsize(文件路径)
            if 文件大小 == 0:
                return {"success": False, "error": "文件内容为空"}

            # 2. 初始化文档处理器
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 3. 处理文档内容和分块
            知识库服务日志器.info("📄 开始文档内容处理和分块...")
            处理结果 = await LangChain文档处理器实例.处理文档文件(
                文件路径, 知识库ID, 文件名, 分块策略, 分块大小, 分块重叠
            )

            if not 处理结果.get("success"):
                return {
                    "success": False,
                    "error": f"文档处理失败: {处理结果.get('error', '未知错误')}",
                }

            # 4. 准备文档数据并保存到数据库
            知识库服务日志器.info("💾 保存文档记录到数据库...")

            处理元数据 = 处理结果.get("元数据", {})
            文档分块列表 = 处理结果.get("文档分块", [])
            分块数量 = len(文档分块列表)

            文档数据 = {
                "文档uuid": 文档UUID,  # 使用预生成的UUID
                "langchain_知识库表id": 知识库ID,
                "文档路径": 文件路径,  # 保存文件路径
                "文档名称": 文件名,
                "文档类型": 处理结果.get("文档类型", ""),
                "文档大小": 文件大小,
                "文档内容": 处理结果.get("文档内容", ""),
                "文档状态": "处理中",  # 初始状态为处理中
                "向量分块数量": 分块数量,  # 直接设置表字段
                "向量状态": "待处理",  # 直接设置表字段
                "元数据": {
                    **(处理元数据 if isinstance(处理元数据, dict) else {}),
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                    "处理时间": datetime.now().isoformat(),
                    # 移除冗余的分块数量和向量化状态字段
                },
            }

            知识库服务日志器.debug(f"创建文档记录: {文档数据}")
            文档记录ID = await self.数据层.创建知识库文档(文档数据)

            if not 文档记录ID:
                return {"success": False, "error": "保存文档记录到数据库失败"}

            # 5. 进行向量化处理
            向量化状态 = "向量化失败"
            向量化成功数量 = 0

            try:
                知识库服务日志器.info("🔮 开始向量化处理...")

                # 检查RAG引擎是否可用
                if not RAG引擎实例.已初始化:
                    知识库服务日志器.info("RAG引擎未初始化，正在初始化...")
                    await RAG引擎实例.初始化()

                # 检查嵌入模型是否可用
                if not RAG引擎实例.嵌入模型:
                    知识库服务日志器.error("❌ 嵌入模型不可用，跳过向量化")
                    await self.数据层.通过ID更新文档状态(
                        文档记录ID, "已处理", "嵌入模型不可用"
                    )
                    向量化状态 = "向量化失败"
                else:
                    # 执行向量化处理
                    知识库服务日志器.info(f"📊 开始向量化 {分块数量} 个文档分块...")

                    向量化结果 = await self._向量化文档分块(
                        知识库ID,
                        文档记录ID,
                        文档分块列表,
                        文档UUID,
                    )

                    if 向量化结果.get("success"):
                        向量化成功数量 = 向量化结果.get("向量化成功数量", 0)
                        向量化状态 = "已向量化"

                        # 更新文档状态为已处理
                        await self.数据层.通过ID更新文档状态(文档记录ID, "已处理", None)
                        await self.数据层.更新文档向量化状态(文档记录ID, "已完成")
                        await self.数据层._更新文档分块统计(
                            文档记录ID
                        )  # 确保分块数量准确

                        知识库服务日志器.info(
                            f"✅ 向量化完成: {向量化成功数量}/{分块数量} 个分块"
                        )
                    else:
                        向量化状态 = "向量化失败"
                        错误信息 = 向量化结果.get("error", "未知错误")

                        # 更新文档状态为处理失败
                        await self.数据层.通过ID更新文档状态(
                            文档记录ID, "失败", 错误信息
                        )
                        await self.数据层.更新文档向量化状态(文档记录ID, "失败")

                        知识库服务日志器.error(f"❌ 向量化失败: {错误信息}")

            except Exception as vec_error:
                知识库服务日志器.error(f"❌ 向量化处理异常: {str(vec_error)}")
                向量化状态 = "向量化失败"

                # 更新文档状态为处理失败
                if 文档记录ID:
                    await self.数据层.通过ID更新文档状态(
                        文档记录ID, "失败", str(vec_error)
                    )
                    await self.数据层.更新文档向量化状态(文档记录ID, "失败")

            # 6. 返回处理结果
            知识库服务日志器.info(
                f"✅ 文档上传完成: {文件名} -> 知识库 {知识库ID}, 向量化状态: {向量化状态}"
            )

            return {
                "success": True,
                "文档ID": 文档记录ID,
                "文档UUID": 文档UUID,
                "文档名称": 文件名,
                "文档类型": 处理结果.get("文档类型", ""),
                "文档大小": 文件大小,
                "分块数量": 分块数量,
                "向量化成功数量": 向量化成功数量,
                "向量化状态": 向量化状态,
                "文件路径": 文件路径,
                "处理配置": {
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                },
                "处理时间": datetime.now().isoformat(),
                "message": "文档上传成功",
            }

        except Exception as e:
            知识库服务日志器.error(f"❌ 文档上传处理失败: {str(e)}")

            # 清理已创建的文档记录
            if 文档记录ID:
                try:
                    await self.数据层.删除知识库文档(文档记录ID)
                    知识库服务日志器.info(f"已清理失败的文档记录: {文档记录ID}")
                except Exception as cleanup_error:
                    知识库服务日志器.warning(f"清理文档记录失败: {cleanup_error}")

            return {"success": False, "error": f"文档上传处理失败: {str(e)}"}

    async def _向量化文档分块(
        self, 知识库ID: int, 文档记录ID: int, 文档分块: List[Dict], 文档UUID: str
    ) -> Dict[str, Any]:
        """向量化文档分块并保存到Chroma"""
        try:
            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化(智能体id=None, 知识库列表=[str(知识库ID)])

            if not RAG引擎实例.嵌入模型:
                return {"success": False, "error": "嵌入模型未初始化"}

            if not RAG引擎实例.向量存储:
                return {"success": False, "error": "向量存储未初始化"}

            向量化成功数量 = 0
            向量化失败数量 = 0

            # 准备文档和元数据
            documents = []
            metadatas = []
            ids = []

            for i, 分块信息 in enumerate(文档分块):
                try:
                    分块内容 = 分块信息.get("分块内容", "")
                    if not 分块内容.strip():
                        continue

                    # 准备元数据
                    分块元数据 = 分块信息.get("元数据", {})
                    metadata = {
                        "doc_uuid": 文档UUID,
                        "chunk_id": i,
                        "知识库ID": 知识库ID,
                        "文档记录ID": 文档记录ID,
                        "分块序号": i,
                        "分块索引": 分块信息.get("分块索引", i),
                        "分块长度": 分块信息.get("分块长度", len(分块内容)),
                    }

                    # 安全地合并分块元数据
                    if isinstance(分块元数据, dict):
                        metadata.update(分块元数据)

                    documents.append(分块内容)
                    metadatas.append(metadata)
                    ids.append(f"{文档UUID}_chunk_{i}")

                    # 调试：记录实际添加到Chroma的内容
                    知识库服务日志器.debug(
                        f"添加到Chroma - 分块{i}: 内容长度={len(分块内容)}, 内容预览='{分块内容[:100]}...'"
                    )

                except Exception as e:
                    知识库服务日志器.error(f"准备分块数据失败: {str(e)}")
                    向量化失败数量 += 1

            if documents:
                # 1. 分批添加到Chroma向量存储（根据嵌入模型动态调整批处理大小）
                try:
                    batch_size = (
                        RAG引擎实例.获取嵌入模型批处理大小()
                    )  # 动态获取批处理大小限制
                    total_batches = (len(documents) + batch_size - 1) // batch_size

                    知识库服务日志器.info(
                        f"开始分批向量化: 总共 {len(documents)} 个分块，批处理大小 {batch_size}，分 {total_batches} 批处理"
                    )

                    for i in range(0, len(documents), batch_size):
                        batch_texts = documents[i : i + batch_size]
                        batch_metadatas = metadatas[i : i + batch_size]
                        batch_ids = ids[i : i + batch_size]

                        RAG引擎实例.向量存储.add_texts(
                            texts=batch_texts, metadatas=batch_metadatas, ids=batch_ids
                        )

                        知识库服务日志器.info(
                            f"批次 {i // batch_size + 1}/{total_batches} 处理完成: {len(batch_texts)} 个分块"
                        )

                    知识库服务日志器.info(
                        f"✅ 所有批次处理完成，成功保存到Chroma: {len(documents)} 个文档分块"
                    )

                except Exception as e:
                    知识库服务日志器.error(f"Chroma向量化失败: {str(e)}")
                    return {"success": False, "error": f"Chroma向量化失败: {str(e)}"}

                # 2. 分批保存到MySQL向量表（避免重复调用嵌入模型）
                mysql_成功数量 = 0
                mysql_失败数量 = 0

                知识库服务日志器.info("开始分批生成向量数据并保存到MySQL...")

                # 分批处理文档分块，避免重复调用嵌入模型
                batch_size = (
                    RAG引擎实例.获取嵌入模型批处理大小()
                )  # 与Chroma保持一致的批处理大小

                for batch_start in range(0, len(文档分块), batch_size):
                    batch_end = min(batch_start + batch_size, len(文档分块))
                    batch_chunks = 文档分块[batch_start:batch_end]

                    try:
                        # 准备批次文本
                        batch_texts = []
                        batch_indices = []

                        for i, 分块信息 in enumerate(batch_chunks, start=batch_start):
                            分块内容 = 分块信息.get("分块内容", "")
                            if 分块内容.strip():
                                batch_texts.append(分块内容)
                                batch_indices.append(i)

                        if not batch_texts:
                            continue

                        # 批量生成向量数据
                        if len(batch_texts) == 1:
                            # 单个文本使用embed_query
                            向量数据列表 = [
                                RAG引擎实例.嵌入模型.embed_query(batch_texts[0])
                            ]
                        else:
                            # 多个文本使用embed_documents（如果支持）
                            try:
                                向量数据列表 = RAG引擎实例.嵌入模型.embed_documents(
                                    batch_texts
                                )
                            except AttributeError:
                                # 如果不支持embed_documents，逐个调用embed_query
                                向量数据列表 = []
                                for text in batch_texts:
                                    向量数据列表.append(
                                        RAG引擎实例.嵌入模型.embed_query(text)
                                    )

                        # 保存每个向量到MySQL
                        for 向量数据, 原始索引 in zip(向量数据列表, batch_indices):
                            try:
                                分块信息 = 文档分块[原始索引]
                                分块内容 = 分块信息.get("分块内容", "")

                                向量记录 = {
                                    "langchain_知识库文档表id": 文档记录ID,
                                    "分块序号": 原始索引,
                                    "分块内容": 分块内容,
                                    "向量维度": len(向量数据),
                                    "元数据": 分块信息.get("元数据", {}),
                                }

                                向量ID = await self.数据层.创建文档向量(向量记录)
                                if 向量ID:
                                    mysql_成功数量 += 1
                                else:
                                    mysql_失败数量 += 1

                            except Exception as e:
                                知识库服务日志器.error(
                                    f"保存单个向量记录失败: {str(e)}"
                                )
                                mysql_失败数量 += 1

                        知识库服务日志器.info(
                            f"MySQL批次 {batch_start // batch_size + 1} 处理完成: {len(batch_texts)} 个分块"
                        )

                    except Exception as e:
                        知识库服务日志器.error(f"MySQL向量化批次失败: {str(e)}")
                        mysql_失败数量 += len(batch_chunks)

                向量化成功数量 = len(documents)
                知识库服务日志器.info(
                    f"向量化完成 - Chroma: {向量化成功数量}, MySQL: {mysql_成功数量}/{mysql_成功数量 + mysql_失败数量}"
                )

            return {
                "success": True,
                "向量化成功数量": 向量化成功数量,
                "向量化失败数量": 向量化失败数量,
                "总分块数": len(文档分块),
            }

        except Exception as e:
            知识库服务日志器.error(f"向量化文档分块失败: {str(e)}")
            return {"success": False, "error": f"向量化失败: {str(e)}"}

    async def 通过UUID删除知识库文档(self, 文档UUID: str) -> Dict[str, Any]:
        """通过UUID删除知识库文档"""
        try:
            删除成功 = await self.数据层.通过UUID删除知识库文档(文档UUID)

            if 删除成功:
                return {"success": True, "message": "文档删除成功"}
            else:
                return {"success": False, "error": "文档删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过UUID删除知识库文档失败: {str(e)}")
            return {"success": False, "error": f"通过UUID删除知识库文档失败: {str(e)}"}

    async def 通过ID删除知识库文档(self, 文档记录ID: int) -> Dict[str, Any]:
        """通过数字ID删除知识库文档"""
        try:
            删除成功 = await self.数据层.通过ID删除知识库文档(文档记录ID)

            if 删除成功:
                return {"success": True, "message": "文档删除成功"}
            else:
                return {"success": False, "error": "文档删除失败"}

        except Exception as e:
            知识库服务日志器.error(f"通过ID删除知识库文档失败: {str(e)}")
            return {"success": False, "error": f"通过ID删除知识库文档失败: {str(e)}"}

    async def 搜索文档(
        self, 知识库ID: int, 搜索关键词: str, 页码: int = 1, 每页数量: int = 10
    ) -> Dict[str, Any]:
        """搜索文档"""
        try:
            # 构建搜索参数
            搜索参数 = {"搜索关键词": 搜索关键词, "页码": 页码, "每页数量": 每页数量}

            # 调用数据层搜索文档
            文档列表, 总数量 = await self.数据层.搜索知识库文档(知识库ID, 搜索参数)

            return {
                "success": True,
                "文档列表": 文档列表,
                "总数量": 总数量,
                "当前页码": 页码,
                "每页数量": 每页数量,
            }

        except Exception as e:
            知识库服务日志器.error(f"搜索文档失败: {str(e)}")
            return {"success": False, "error": f"搜索文档失败: {str(e)}"}

    # ==================== 向量化相关方法 ====================

    async def 向量化知识库(
        self,
        知识库ID: int,
        向量存储类型: str = "chroma",
        云服务配置: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """向量化知识库 - 支持Chroma和云托管服务"""
        try:
            # 使用RAG引擎进行向量化
            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化()

            # 执行向量化
            向量化结果 = await RAG引擎实例.向量化知识库(知识库ID)

            if 向量化结果.get("success"):
                知识库服务日志器.info(
                    f"知识库向量化成功: {知识库ID} - 存储类型: {向量存储类型}"
                )
                return {
                    "success": True,
                    "message": "知识库向量化成功",
                    "向量化统计": 向量化结果.get("统计信息", {}),
                    "向量存储类型": 向量存储类型,
                }
            else:
                return {
                    "success": False,
                    "error": 向量化结果.get("error", "知识库向量化失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"向量化知识库失败: {str(e)}")
            return {"success": False, "error": f"向量化知识库失败: {str(e)}"}

    async def 获取向量化状态(self, 知识库ID: int) -> Dict[str, Any]:
        """获取知识库向量化状态"""
        try:
            # 调用数据层获取向量化状态
            向量化状态 = await self.数据层.获取知识库向量化状态(知识库ID)

            return {"success": True, "向量化状态": 向量化状态}

        except Exception as e:
            知识库服务日志器.error(f"获取向量化状态失败: {str(e)}")
            return {"success": False, "error": f"获取向量化状态失败: {str(e)}"}

    async def 手动向量化文档(self, 文档记录ID: int) -> Dict[str, Any]:
        """手动向量化单个文档"""
        try:
            # 使用文档处理器进行向量化
            if not LangChain文档处理器实例.已初始化:
                await LangChain文档处理器实例.初始化()

            # 执行文档向量化
            向量化结果 = await LangChain文档处理器实例.手动向量化文档(文档记录ID)

            if 向量化结果.get("success"):
                知识库服务日志器.info(f"手动向量化文档成功: {文档记录ID}")
                return {
                    "success": True,
                    "message": "文档向量化成功",
                    "向量化统计": 向量化结果.get("统计信息", {}),
                }
            else:
                return {
                    "success": False,
                    "error": 向量化结果.get("error", "文档向量化失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"手动向量化文档失败: {str(e)}")
            return {"success": False, "error": f"手动向量化文档失败: {str(e)}"}

    # ==================== 向量检索相关方法 ====================

    async def 智能多层检索(
        self,
        知识库ID: int,
        查询文本: str,
        最大数量: int = 10,
        相似度阈值: float = 0.7,
        最小结果数量: int = 3,
        启用自适应阈值: bool = True,
    ) -> Dict[str, Any]:
        """智能多层检索 - 解决高阈值导致结果不足的问题"""
        try:
            知识库服务日志器.info(
                f"🔍 智能多层检索: 知识库ID={知识库ID}, 阈值={相似度阈值}, 最小结果数={最小结果数量}"
            )

            # 定义检索层级配置 (简化为3层)
            检索层级 = [
                ("精确检索", 相似度阈值, 最大数量),
                ("扩展检索", 相似度阈值 * 0.7, min(最大数量 * 2, 15)),
                ("兜底检索", 0.3, min(最大数量 * 3, 20)),
            ]

            # 逐层尝试检索
            for i, (层级名称, 当前阈值, 当前数量) in enumerate(检索层级):
                知识库服务日志器.debug(
                    f"📊 第{i + 1}层: {层级名称} (阈值={当前阈值:.3f}, 数量={当前数量})"
                )

                当前结果 = await self.Chroma向量检索(
                    知识库ID=知识库ID,
                    查询文本=查询文本,
                    最大数量=当前数量,
                    相似度阈值=当前阈值,
                )

                if 当前结果.get("success"):
                    结果数量 = len(当前结果.get("检索结果", []))

                    # 检查是否满足要求 (最后一层或数量充足)
                    if 结果数量 >= 最小结果数量 or i == len(检索层级) - 1:
                        # 增强结果信息
                        当前结果.update(
                            {
                                "使用多层检索": True,
                                "成功层级": 层级名称,
                                "最终阈值": 当前阈值,
                                "尝试层数": i + 1,
                            }
                        )

                        知识库服务日志器.info(
                            f"✅ {层级名称}成功: {结果数量}个结果 (阈值={当前阈值:.3f})"
                        )
                        return 当前结果
                    else:
                        知识库服务日志器.debug(
                            f"⚠️ {层级名称}结果不足: {结果数量} < {最小结果数量}"
                        )

            # 所有层级都失败
            知识库服务日志器.warning("❌ 所有检索层级都失败")
            return {
                "success": False,
                "error": "多层检索全部失败",
                "使用多层检索": True,
            }

        except Exception as e:
            知识库服务日志器.error(f"智能多层检索异常: {str(e)}")
            return {
                "success": False,
                "error": f"智能多层检索异常: {str(e)}",
                "使用多层检索": True,
            }

    def _简单质量评估(self, 检索结果: List[Dict], 相似度阈值: float) -> float:
        """简化的质量评估 - 基于平均相似度"""
        if not 检索结果:
            return 0.0

        相似度分数列表 = [结果.get("相似度分数", 0) for 结果 in 检索结果]
        平均相似度 = sum(相似度分数列表) / len(相似度分数列表)

        # 简单的质量分数：平均相似度相对于阈值的比例
        质量分数 = min(1.0, 平均相似度 / max(相似度阈值, 0.1))
        return 质量分数

    async def Chroma向量检索(
        self, 知识库ID: int, 查询文本: str, 最大数量: int = 10, 相似度阈值: float = 0.5
    ) -> Dict[str, Any]:
        """使用Chroma进行向量检索"""
        try:
            知识库服务日志器.info(
                f"Chroma向量检索: 知识库ID={知识库ID}, 查询='{查询文本}', "
                f"最大数量={最大数量}, 相似度阈值={相似度阈值}"
            )

            # 🔧 关键修复：获取知识库的具体配置信息
            知识库详情 = await self.数据层.获取知识库详情(知识库ID)
            if not 知识库详情:
                return {"success": False, "error": f"知识库 {知识库ID} 不存在"}

            # 获取知识库的集合名称
            集合名称 = 知识库详情.get("chroma_集合名称")
            if not 集合名称:
                return {
                    "success": False,
                    "error": f"知识库 {知识库ID} 未设置Chroma集合名称",
                }

            # 初始化RAG引擎，确保使用正确的嵌入模型和集合
            # 🔧 重要：使用新的知识库ID直接初始化方法
            初始化成功 = await RAG引擎实例.根据知识库ID初始化(知识库ID)
            if not 初始化成功:
                return {
                    "success": False,
                    "error": f"知识库 {知识库ID} RAG引擎初始化失败",
                }

            if not RAG引擎实例.向量存储:
                return {"success": False, "error": "向量存储未初始化"}

            # 验证嵌入模型是否可用
            if not RAG引擎实例.嵌入模型:
                return {"success": False, "error": "嵌入模型未初始化"}

            # 验证集合名称
            当前集合名称 = getattr(RAG引擎实例, "chroma_集合名称", None)
            知识库服务日志器.info(f"🔍 使用Chroma集合: {当前集合名称}")

            # 验证集合是否存在且有数据
            if RAG引擎实例.chroma_客户端 and 当前集合名称:
                try:
                    collection = RAG引擎实例.chroma_客户端.get_collection(当前集合名称)
                    文档数量 = collection.count()
                    知识库服务日志器.info(
                        f"📊 集合 {当前集合名称} 包含 {文档数量} 个文档"
                    )
                    if 文档数量 == 0:
                        return {
                            "success": False,
                            "error": f"集合 {当前集合名称} 为空，没有可检索的文档",
                        }
                except Exception as e:
                    知识库服务日志器.warning(f"⚠️ 检查集合状态失败: {str(e)}")
                    return {
                        "success": False,
                        "error": f"集合 {当前集合名称} 不存在或无法访问",
                    }

            # 使用Chroma进行相似度搜索
            检索结果 = RAG引擎实例.向量存储.similarity_search_with_score(
                query=查询文本, k=最大数量
            )

            # 处理检索结果并收集文档记录ID
            结果列表 = []
            文档记录ID集合 = set()
            原始结果数量 = len(检索结果)
            过滤掉的数量 = 0

            for document, distance_score in 检索结果:
                try:
                    元数据 = document.metadata or {}
                    文档记录ID = 元数据.get("document_record_id", 0)
                    if 文档记录ID:
                        文档记录ID集合.add(文档记录ID)

                    # 将距离分数转换为相似度分数
                    # Chroma使用余弦相似度，返回余弦距离（范围[0,1]，0表示完全相似）
                    # 正确的转换公式: 相似度 = 1 - 距离
                    相似度分数 = 1.0 - float(distance_score)

                    # 确保相似度在合理范围内
                    相似度分数 = max(0.0, min(1.0, 相似度分数))

                    # 添加详细的调试日志
                    知识库服务日志器.debug(
                        f"处理检索结果: 原始距离={float(distance_score):.6f}, "
                        f"转换相似度={相似度分数:.6f}, 阈值={相似度阈值}, "
                        f"内容预览='{document.page_content[:50]}...'"
                    )

                    # 🔧 关键修复：确保只返回当前知识库的结果
                    文档知识库ID = 元数据.get("knowledge_base_id")
                    if 文档知识库ID and int(文档知识库ID) != int(知识库ID):
                        过滤掉的数量 += 1
                        知识库服务日志器.debug(
                            f"过滤掉其他知识库结果: 文档知识库ID={文档知识库ID}, 当前知识库ID={知识库ID}"
                        )
                        continue  # 跳过其他知识库的结果

                    # 应用相似度阈值过滤
                    if 相似度分数 < 相似度阈值:
                        过滤掉的数量 += 1
                        知识库服务日志器.debug(
                            f"过滤掉低相似度结果: {相似度分数:.6f} < {相似度阈值}"
                        )
                        continue  # 跳过低于阈值的结果

                    结果项 = {
                        "分块内容": document.page_content,
                        "相似度分数": round(相似度分数, 4),
                        "原始距离分数": round(
                            float(distance_score), 4
                        ),  # 保留原始距离分数用于调试
                        "chroma_元数据": 元数据,
                        "分块序号": 元数据.get("chunk_index", 0),
                        "知识库ID": 知识库ID,  # 确保使用当前知识库ID
                        "文档记录ID": 文档记录ID,
                        "分块大小": 元数据.get(
                            "chunk_size", len(document.page_content)
                        ),
                    }
                    结果列表.append(结果项)
                except Exception as e:
                    知识库服务日志器.warning(f"处理检索结果失败: {str(e)}")
                    continue

            # 批量获取MySQL中的文档元数据
            if 文档记录ID集合:
                try:
                    文档元数据映射 = await self._批量获取文档元数据(
                        list(文档记录ID集合)
                    )

                    # 增强检索结果
                    for 结果项 in 结果列表:
                        文档记录ID = 结果项.get("文档记录ID")
                        if 文档记录ID and 文档记录ID in 文档元数据映射:
                            mysql_元数据 = 文档元数据映射[文档记录ID]
                            结果项.update(
                                {
                                    "文档名称": mysql_元数据.get("文档名称", ""),
                                    "文档类型": mysql_元数据.get("文档类型", ""),
                                    "文档大小": mysql_元数据.get("文档大小", 0),
                                    "上传时间": mysql_元数据.get("创建时间", ""),
                                    "mysql_元数据": mysql_元数据,
                                }
                            )
                except Exception as e:
                    知识库服务日志器.warning(f"获取MySQL元数据失败: {str(e)}")

            # 添加详细的调试信息
            if 结果列表:
                最高相似度 = max(结果["相似度分数"] for 结果 in 结果列表)
                最低相似度 = min(结果["相似度分数"] for 结果 in 结果列表)
                知识库服务日志器.info(
                    f"Chroma向量检索完成: 知识库ID {知识库ID}, 查询: '{查询文本}', "
                    f"原始结果: {原始结果数量}, 过滤掉: {过滤掉的数量}, 最终返回: {len(结果列表)} 个结果, "
                    f"相似度范围: [{最低相似度:.4f}, {最高相似度:.4f}], 阈值: {相似度阈值}"
                )
            else:
                知识库服务日志器.warning(
                    f"Chroma向量检索无结果: 知识库ID {知识库ID}, 查询: '{查询文本}', "
                    f"原始结果: {原始结果数量}, 过滤掉: {过滤掉的数量}, 阈值: {相似度阈值}"
                )

            return {
                "success": True,
                "查询文本": 查询文本,
                "知识库ID": 知识库ID,
                "结果数量": len(结果列表),
                "检索结果": 结果列表,
                "检索统计": {
                    "原始结果数量": 原始结果数量,
                    "过滤掉的数量": 过滤掉的数量,
                    "相似度阈值": 相似度阈值,
                    "最终结果数量": len(结果列表),
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"Chroma向量检索失败: {str(e)}")
            return {"success": False, "error": f"向量检索失败: {str(e)}"}

    async def 混合检索(
        self, 知识库ID: int, 查询文本: str, 最大数量: int = 10, 相似度阈值: float = 0.5
    ) -> Dict[str, Any]:
        """混合检索：结合Chroma向量检索和PostgreSQL元数据查询"""
        try:
            # 1. 使用Chroma进行向量检索
            向量检索结果 = await self.Chroma向量检索(
                知识库ID, 查询文本, 最大数量, 相似度阈值
            )

            if not 向量检索结果.get("success"):
                return 向量检索结果

            # 2. 从PostgreSQL获取文档元数据，丰富检索结果
            检索结果 = 向量检索结果["检索结果"]
            文档UUID列表 = list(
                set([结果.get("文档UUID") for 结果 in 检索结果 if 结果.get("文档UUID")])
            )

            if 文档UUID列表:
                # 批量查询文档信息
                文档信息映射 = {}
                for 文档UUID in 文档UUID列表:
                    try:
                        # 使用PostgreSQL查询文档详情
                        查询SQL = """
                        SELECT 文档名称, 文档类型, 创建时间
                        FROM langchain_知识库文档表
                        WHERE 文档uuid = $1
                        """
                        async with 异步连接池实例.获取连接() as 连接:
                            结果 = await 连接.fetchrow(查询SQL, 文档UUID)
                            if 结果:
                                文档信息映射[文档UUID] = {
                                    "文档名称": 结果.get("文档名称", ""),
                                    "文档类型": 结果.get("文档类型", ""),
                                    "创建时间": 结果.get("创建时间", ""),
                                }
                    except Exception as e:
                        知识库服务日志器.warning(
                            f"获取文档信息失败: {文档UUID}, {str(e)}"
                        )

                # 丰富检索结果
                for 结果 in 检索结果:
                    文档UUID = 结果.get("文档UUID")
                    if 文档UUID and 文档UUID in 文档信息映射:
                        结果.update(文档信息映射[文档UUID])

            return {
                "success": True,
                "查询文本": 查询文本,
                "知识库ID": 知识库ID,
                "结果数量": len(检索结果),
                "检索结果": 检索结果,
                "检索方式": "混合检索(向量+PostgreSQL)",
                "向量存储类型": self._获取当前向量存储类型(),
            }

        except Exception as e:
            知识库服务日志器.error(f"混合检索失败: {str(e)}")
            return {"success": False, "error": f"混合检索失败: {str(e)}"}

    def _获取当前向量存储类型(self) -> str:
        """获取当前向量存储类型"""
        return "ChromaDB"

    async def _批量获取文档元数据(
        self, 文档记录ID列表: List[int]
    ) -> Dict[int, Dict[str, Any]]:
        """批量获取文档元数据"""
        try:
            if not 文档记录ID列表:
                return {}

            # 构建批量查询SQL
            占位符 = ",".join(["%s"] * len(文档记录ID列表))
            查询SQL = f"""
            SELECT id, 文档名称, 文档类型, 文档大小, 创建时间, 更新时间,
                   状态, 元数据, langchain_知识库表id
            FROM langchain_知识库文档表
            WHERE id IN ({占位符})
            """

            查询结果 = await self.数据层.执行查询(查询SQL, 文档记录ID列表)

            # 构建映射
            文档元数据映射 = {}
            for 行 in 查询结果:
                文档记录ID = 行[0]
                文档元数据映射[文档记录ID] = {
                    "文档名称": 行[1],
                    "文档类型": 行[2],
                    "文档大小": 行[3],
                    "创建时间": 行[4],
                    "更新时间": 行[5],
                    "状态": 行[6],
                    "元数据": 行[7],
                    "知识库ID": 行[8],
                }

            return 文档元数据映射

        except Exception as e:
            知识库服务日志器.error(f"批量获取文档元数据失败: {str(e)}")
            return {}

    # ==================== 检索配置和测试相关方法 ====================

    async def 获取检索配置(self, 知识库ID: int) -> Dict[str, Any]:
        """获取检索配置"""
        try:
            # 调用数据层获取检索配置
            检索配置 = await self.数据层.获取知识库检索配置(知识库ID)

            if 检索配置:
                知识库服务日志器.info(f"获取知识库 {知识库ID} 的检索配置成功")
                return {"success": True, "data": 检索配置}
            else:
                # 从数据库获取默认嵌入模型作为默认配置
                默认模型结果 = await self.获取默认嵌入模型()

                if 默认模型结果["success"]:
                    默认模型 = 默认模型结果["data"]
                    默认配置 = {
                        "检索策略": "similarity",
                        "相似度阈值": 0.7,  # 系统默认值，实际使用时会被关联配置覆盖
                        "最大检索数量": 5,  # 系统默认值，实际使用时会被关联配置覆盖
                        "分块大小": 1000,
                        "分块重叠": 200,
                        "分块策略": "智能递归分块",
                        "嵌入模型": 默认模型["模型名称"],
                        "嵌入模型ID": 默认模型["id"],
                        "向量维度": 默认模型["向量维度"],
                        "模型提供商": 默认模型["供应商名称"],
                        "支持语言": 默认模型["支持语言"],
                    }
                else:
                    # 如果没有可用的嵌入模型，返回基础配置
                    默认配置 = {
                        "检索策略": "keyword",
                        "相似度阈值": 0.7,  # 系统默认值，实际使用时会被关联配置覆盖
                        "最大检索数量": 5,  # 系统默认值，实际使用时会被关联配置覆盖
                        "分块大小": 1000,
                        "分块重叠": 200,
                        "分块策略": "智能递归分块",
                        "嵌入模型": None,
                        "嵌入模型ID": None,
                        "向量维度": 0,
                        "模型提供商": "",
                        "支持语言": "",
                        "警告": "未配置嵌入模型，将使用关键词检索",
                    }

                知识库服务日志器.info(f"知识库 {知识库ID} 使用默认检索配置")
                return {"success": True, "data": 默认配置}

        except Exception as e:
            知识库服务日志器.error(f"获取检索配置失败: {str(e)}")
            return {"success": False, "error": f"获取检索配置失败: {str(e)}"}

    async def 更新检索配置(
        self, 知识库ID: int, 配置数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新检索配置"""
        try:
            # 调用数据层更新检索配置
            更新成功 = await self.数据层.更新知识库检索配置(知识库ID, 配置数据)

            if 更新成功:
                return {"success": True, "message": "检索配置更新成功"}
            else:
                return {"success": False, "error": "检索配置更新失败"}

        except Exception as e:
            知识库服务日志器.error(f"更新检索配置失败: {str(e)}")
            return {"success": False, "error": f"更新检索配置失败: {str(e)}"}

    async def 直接知识库检索(
        self, 知识库ID: int, 查询文本: str, 检索参数: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """直接知识库检索 - 纯粹的知识库向量检索测试，不依赖智能体"""
        try:
            if 检索参数 is None:
                检索参数 = {}

            # 获取检索配置
            配置结果 = await self.获取检索配置(知识库ID)
            if not 配置结果.get("success"):
                return {"success": False, "error": "无法获取检索配置"}

            检索配置 = 配置结果["data"]

            # 如果指定了嵌入模型ID，使用指定的模型
            if 检索参数.get("嵌入模型ID"):
                指定模型结果 = await self._获取指定嵌入模型(检索参数["嵌入模型ID"])
                if 指定模型结果["success"]:
                    指定模型 = 指定模型结果["data"]
                    检索配置.update(
                        {
                            "嵌入模型": 指定模型["模型名称"],
                            "嵌入模型ID": 指定模型["id"],
                            "向量维度": 指定模型["向量维度"],
                            "模型提供商": 指定模型["供应商名称"],
                        }
                    )

            # 优先使用传入的检索参数，其次使用检索配置，最后使用系统默认值
            最大检索数量 = 检索参数.get("最大检索数量", 检索配置.get("最大检索数量", 5))
            相似度阈值 = 检索参数.get("相似度阈值", 检索配置.get("相似度阈值", 0.7))

            # 获取知识库的向量化文档
            向量文档列表 = await self._获取知识库向量文档(
                知识库ID, 最大检索数量 * 2
            )  # 获取更多候选文档

            if not 向量文档列表:
                return {
                    "success": True,
                    "data": {
                        "查询文本": 查询文本,
                        "检索结果": [],
                        "结果数量": 0,
                        "检索参数": 检索参数,
                        "检索配置": 检索配置,
                        "message": "知识库中没有向量化文档，请先进行向量化",
                    },
                }

            # 执行相似度检索
            检索结果 = await self._执行相似度检索(
                查询文本, 向量文档列表, 最大检索数量, 相似度阈值, 检索配置
            )

            知识库服务日志器.info(
                f"检索测试成功: 知识库 {知识库ID}, 查询: {查询文本}, 结果数量: {len(检索结果)}"
            )
            return {
                "success": True,
                "data": {
                    "查询文本": 查询文本,
                    "检索结果": 检索结果,
                    "结果数量": len(检索结果),
                    "检索参数": 检索参数,
                    "检索配置": 检索配置,
                    "message": f"基于向量相似度检索找到 {len(检索结果)} 个相关文档片段",
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"测试检索失败: {str(e)}")
            return {"success": False, "error": f"测试检索失败: {str(e)}"}

    async def 获取知识库Chroma文档(self, 知识库ID: int) -> List[Dict[str, Any]]:
        """从Chroma获取知识库的向量文档"""
        try:
            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化(智能体id=知识库ID)

            # 获取Chroma集合信息
            集合信息 = await RAG引擎实例.获取Chroma集合信息()

            if 集合信息.get("success"):
                return [
                    {
                        "集合名称": 集合信息.get("集合名称"),
                        "文档数量": 集合信息.get("文档数量", 0),
                        "向量维度": 集合信息.get("向量维度"),
                        "最后更新时间": 集合信息.get("最后更新时间"),
                        "状态": "正常",
                    }
                ]
            else:
                return []

        except Exception as e:
            知识库服务日志器.error(f"获取Chroma文档失败: {str(e)}")
            return []

    async def 获取Chroma集合状态(self, 知识库ID: int) -> Dict[str, Any]:
        """获取知识库的Chroma集合状态"""
        try:
            # 获取数据库中的Chroma信息
            chroma信息 = await self.数据层.获取知识库Chroma信息(知识库ID)
            if not chroma信息:
                return {"success": False, "error": "知识库不存在"}

            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化(智能体id=知识库ID)

            # 获取Chroma集合信息
            集合信息 = await RAG引擎实例.获取Chroma集合信息()

            if 集合信息.get("success"):
                return {
                    "success": True,
                    "data": {
                        "知识库ID": 知识库ID,
                        "知识库名称": chroma信息.get("知识库名称"),
                        "chroma_集合名称": chroma信息.get("chroma_集合名称"),
                        "chroma_状态": chroma信息.get("chroma_状态"),
                        "向量存储类型": chroma信息.get("向量存储类型"),
                        "文档数量": 集合信息.get("文档数量", 0),
                        "向量维度": 集合信息.get("向量维度"),
                        "最后更新时间": 集合信息.get("最后更新时间"),
                    },
                }
            else:
                return {
                    "success": False,
                    "error": 集合信息.get("error", "获取Chroma集合状态失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"获取Chroma集合状态失败: {str(e)}")
            return {"success": False, "error": f"获取Chroma集合状态失败: {str(e)}"}

    async def 清空知识库向量数据(self, 知识库ID: int) -> Dict[str, Any]:
        """清空知识库的向量数据"""
        try:
            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化(智能体id=知识库ID)

            # 清空Chroma数据
            清空结果 = await RAG引擎实例.清空向量存储()

            if 清空结果.get("success"):
                # 更新数据库状态
                await self.数据层.更新知识库Chroma状态(知识库ID, "active")
                return {"success": True, "message": "知识库向量数据已清空"}
            else:
                return {
                    "success": False,
                    "error": 清空结果.get("error", "清空向量数据失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"清空知识库向量数据失败: {str(e)}")
            return {"success": False, "error": f"清空知识库向量数据失败: {str(e)}"}

    async def 切换向量存储服务(
        self, 知识库ID: int, 目标服务类型: str, 云服务配置: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """切换向量存储服务"""
        try:
            if 目标服务类型 == "chroma":
                切换结果 = await RAG引擎实例.切换到本地服务()
            elif 目标服务类型 == "cloud":
                if not 云服务配置:
                    return {"success": False, "error": "切换到云服务需要提供云服务配置"}
                切换结果 = await RAG引擎实例.切换到云服务(云服务配置)
            else:
                return {"success": False, "error": f"不支持的服务类型: {目标服务类型}"}

            if 切换结果.get("success"):
                # 更新数据库配置
                await self.数据层.更新向量存储类型(知识库ID, 目标服务类型, 云服务配置)

                return {
                    "success": True,
                    "data": {
                        "知识库ID": 知识库ID,
                        "目标服务类型": 目标服务类型,
                        "切换时间": 切换结果.get("切换时间"),
                        "状态": "切换成功",
                    },
                }
            else:
                return {
                    "success": False,
                    "error": 切换结果.get("error", "向量存储服务切换失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"切换向量存储服务失败: {str(e)}")
            return {"success": False, "error": f"切换向量存储服务失败: {str(e)}"}

    async def _执行相似度检索(
        self,
        查询文本: str,
        向量文档列表: List[Dict[str, Any]],
        最大检索数量: int,
        相似度阈值: float,
        检索配置: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """执行相似度检索 - 基于LangChain similarity_search模式"""
        try:
            # 简化的相似度计算（实际应该使用嵌入模型）
            检索结果 = []
            查询关键词 = 查询文本.lower().split()

            for 文档 in 向量文档列表:
                文档内容 = str(文档.get("内容", "")).lower()

                # 计算简单的关键词匹配分数
                匹配分数 = 0.0
                for 关键词 in 查询关键词:
                    if 关键词 in 文档内容:
                        匹配分数 += 1.0 / len(查询关键词)

                # 只保留超过阈值的结果
                if 匹配分数 >= 相似度阈值:
                    检索结果.append(
                        {
                            "序号": len(检索结果) + 1,
                            "内容": 文档.get("内容", "")[:300] + "..."
                            if len(文档.get("内容", "")) > 300
                            else 文档.get("内容", ""),
                            "元数据": {
                                "文档UUID": 文档.get("uuid", ""),
                                "创建时间": str(文档.get("创建时间", "")),
                                **文档.get("元数据", {}),
                            },
                            "相似度分数": round(匹配分数, 3),
                            "检索策略": 检索配置.get("检索策略", "similarity"),
                        }
                    )

            # 按相似度分数排序
            检索结果.sort(key=lambda x: x["相似度分数"], reverse=True)

            # 限制返回数量
            return 检索结果[:最大检索数量]

        except Exception as e:
            知识库服务日志器.error(f"执行相似度检索失败: {str(e)}")
            return []

    # ==================== 智能体检索测试相关方法 ====================

    async def 智能体检索测试(
        self, 智能体ID: int, 查询文本: str, 检索参数: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """智能体检索测试 - 测试智能体通过RAG引擎检索关联知识库的能力"""
        try:
            if 检索参数 is None:
                检索参数 = {}

            # 使用RAG引擎进行智能体检索测试
            # 初始化RAG引擎（传入智能体ID）
            知识库服务日志器.debug(
                f"🔧 RAG引擎状态: 已初始化={RAG引擎实例.已初始化}, 当前智能体ID={getattr(RAG引擎实例, '当前智能体id', None)}"
            )

            if not RAG引擎实例.已初始化:
                知识库服务日志器.info(f"🚀 初始化RAG引擎，智能体ID: {智能体ID}")
                await RAG引擎实例.初始化(智能体id=智能体ID)
            else:
                # 如果已初始化但智能体ID不同，重新初始化
                if getattr(RAG引擎实例, "当前智能体id", None) != 智能体ID:
                    知识库服务日志器.info(f"🔄 重新初始化RAG引擎，智能体ID: {智能体ID}")
                    await RAG引擎实例.初始化(智能体id=智能体ID)

            # 检查向量存储状态
            知识库服务日志器.debug(
                f"📊 向量存储状态: {RAG引擎实例.向量存储 is not None}, 集合名称: {getattr(RAG引擎实例, 'chroma_集合名称', 'None')}"
            )

            # 执行检索 - 支持智能多层检索
            最大检索数量 = 检索参数.get("最大检索数量", 5)
            相似度阈值 = 检索参数.get("相似度阈值", 0.7)
            启用智能检索 = 检索参数.get("启用智能多层检索", True)
            最小结果数量 = 检索参数.get("最小结果数量", 3)

            知识库服务日志器.info(
                f"🔍 开始检索: 查询='{查询文本}', 最大数量={最大检索数量}, "
                f"相似度阈值={相似度阈值}, 智能检索={'启用' if 启用智能检索 else '禁用'}"
            )

            # 获取智能体关联的知识库列表
            关联知识库 = await LangChain智能体数据层实例.获取智能体关联知识库(智能体ID)

            if not 关联知识库:
                知识库服务日志器.warning(f"智能体 {智能体ID} 未关联任何知识库")
                return {
                    "success": False,
                    "error": "智能体未关联任何知识库",
                    "data": {"检索结果": []},
                }

            # 收集所有知识库的检索结果
            所有检索结果 = []

            for 关联信息 in 关联知识库:
                知识库ID = 关联信息.get("知识库ID")
                if not 知识库ID:
                    continue

                try:
                    if 启用智能检索:
                        # 使用智能多层检索
                        检索结果 = await self.智能多层检索(
                            知识库ID=知识库ID,
                            查询文本=查询文本,
                            最大数量=最大检索数量,
                            相似度阈值=相似度阈值,
                            最小结果数量=最小结果数量,
                        )
                    else:
                        # 使用传统检索
                        检索结果 = await self.Chroma向量检索(
                            知识库ID=知识库ID,
                            查询文本=查询文本,
                            最大数量=最大检索数量,
                            相似度阈值=相似度阈值,
                        )

                    if 检索结果.get("success"):
                        检索文档列表 = 检索结果.get("检索结果", [])
                        # 为每个结果添加知识库ID标识
                        for 文档 in 检索文档列表:
                            文档["知识库ID"] = 知识库ID
                        所有检索结果.extend(检索文档列表)

                        知识库服务日志器.info(
                            f"✅ 知识库 {知识库ID} 检索成功: {len(检索文档列表)} 个结果"
                        )

                except Exception as e:
                    知识库服务日志器.warning(f"知识库 {知识库ID} 检索失败: {str(e)}")
                    continue

            知识库服务日志器.info(
                f"📊 检索完成: 总结果数量={len(所有检索结果)}, "
                f"智能检索={'启用' if 启用智能检索 else '禁用'}"
            )

            # 处理检索结果
            if 所有检索结果:
                # 按相似度分数排序
                所有检索结果.sort(key=lambda x: x.get("相似度分数", 0), reverse=True)

                # 格式化结果
                格式化结果 = []
                for i, 结果 in enumerate(所有检索结果):
                    格式化结果.append(
                        {
                            "序号": i + 1,
                            "文档内容": 结果.get("分块内容", "")[:500] + "..."
                            if len(结果.get("分块内容", "")) > 500
                            else 结果.get("分块内容", ""),
                            "元数据": 结果.get("chroma_元数据", {}),
                            "相似度分数": 结果.get("相似度分数", 0),
                            "原始距离分数": 结果.get("原始距离分数", 0),
                            "检索方式": 结果.get("检索方式", "智能多层检索"),
                            "知识库ID": 结果.get("知识库ID", 0),
                        }
                    )

                # 构建返回结果
                返回结果 = {
                    "success": True,
                    "data": {
                        "检索结果": 格式化结果,
                        "检索参数": 检索参数,
                        "智能体ID": 智能体ID,
                        "查询文本": 查询文本,
                        "使用智能检索": 启用智能检索,
                    },
                }

                知识库服务日志器.info(
                    f"✅ 智能体检索成功: {len(格式化结果)}个结果, 智能检索={'启用' if 启用智能检索 else '禁用'}"
                )

                return 返回结果
            else:
                return {
                    "success": True,
                    "data": {
                        "检索结果": [],
                        "智能体ID": 智能体ID,
                        "查询文本": 查询文本,
                        "使用智能检索": 启用智能检索,
                    },
                }

        except Exception as e:
            知识库服务日志器.error(f"智能体检索测试失败: {str(e)}")
            return {"success": False, "error": f"智能体检索测试失败: {str(e)}"}

    async def 获取智能体关联知识库(self, 智能体ID: int) -> Dict[str, Any]:
        """获取智能体关联的知识库列表 - 使用关联表"""
        try:
            # 获取智能体基本信息
            智能体详情 = await LangChain智能体数据层实例.获取智能体详细信息(智能体ID)

            if not 智能体详情:
                return {"success": False, "error": "智能体不存在"}

            # 通过关联表获取知识库列表
            关联查询SQL = """
            SELECT
                akr.langchain_知识库表id as 知识库ID,
                akr.权重,
                akr.检索策略,
                akr.最大检索数量,
                akr.相似度阈值,
                akr.状态 as 关联状态
            FROM langchain_智能体知识库关联表 akr
            WHERE akr.langchain_智能体配置表id = %s AND akr.状态 = 'active'
            ORDER BY akr.权重 DESC, akr.id ASC
            """

            关联结果 = await self.数据层.数据库连接池.执行查询(
                关联查询SQL, (智能体ID,)
            )

            # 获取知识库详细信息
            知识库详情列表 = []
            for 关联记录 in 关联结果 or []:
                知识库ID = 关联记录["知识库ID"]
                知识库详情 = await self.数据层.获取知识库详情(知识库ID)
                if 知识库详情:
                    # 合并关联信息和知识库详情
                    知识库详情.update(
                        {
                            "权重": 关联记录["权重"],
                            "检索策略": 关联记录["检索策略"],
                            "最大检索数量": 关联记录["最大检索数量"],
                            "相似度阈值": 关联记录["相似度阈值"],
                            "关联状态": 关联记录["关联状态"],
                        }
                    )
                    知识库详情列表.append(知识库详情)

            return {
                "success": True,
                "data": {
                    "智能体ID": 智能体ID,
                    "智能体名称": 智能体详情.get("智能体名称", ""),
                    "关联知识库": 知识库详情列表,
                    "知识库数量": len(知识库详情列表),
                },
            }

        except Exception as e:
            知识库服务日志器.error(f"获取智能体关联知识库失败: {str(e)}")
            return {"success": False, "error": f"获取智能体关联知识库失败: {str(e)}"}

    # ==================== 嵌入模型管理相关方法 ====================

    async def 获取可用嵌入模型列表(self) -> Dict[str, Any]:
        """获取所有可用的嵌入模型列表"""
        try:
            # 查询所有可用的嵌入模型
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商,
                API密钥,
                API基础URL,
                最大令牌数,
                启用状态,
                创建时间
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%embedding%'
            AND 启用状态 = 1
            ORDER BY 提供商, 模型名称
            """

            结果 = await 异步连接池实例.执行查询(查询SQL)

            嵌入模型列表 = []
            for 模型信息 in 结果:
                模型参数 = 模型信息.get("模型参数", {})

                # 解析模型参数
                if isinstance(模型参数, str):
                    try:
                        模型参数 = json.loads(模型参数)
                    except (json.JSONDecodeError, ValueError) as e:
                        知识库服务日志器.warning(f"解析模型参数失败: {str(e)}")
                        模型参数 = {}

                嵌入模型列表.append(
                    {
                        "id": 模型信息["id"],
                        "模型名称": 模型信息["模型名称"],
                        "模型类型": 模型信息["模型类型"],
                        "显示名称": 模型信息["显示名称"],
                        "提供商": 模型信息["提供商"],
                        "向量维度": 模型参数.get("embedding_dimension", 1536),
                        "支持语言": 模型参数.get("supported_languages", "中文/英文"),
                        "最大令牌数": 模型信息["最大令牌数"],
                        "启用状态": 模型信息["启用状态"],
                        "创建时间": str(模型信息["创建时间"]),
                    }
                )

            知识库服务日志器.info(
                f"获取可用嵌入模型列表成功: {len(嵌入模型列表)} 个模型"
            )
            return {"success": True, "data": 嵌入模型列表}

        except Exception as e:
            知识库服务日志器.error(f"获取可用嵌入模型列表失败: {str(e)}")
            return {"success": False, "error": f"获取可用嵌入模型列表失败: {str(e)}"}

    async def 获取可用AI模型列表(self) -> Dict[str, Any]:
        """获取可用的AI对话模型列表"""
        try:
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商,
                启用状态
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%chat%'
            AND 启用状态 = 1
            ORDER BY 创建时间 DESC
            """

            结果 = await 异步连接池实例.执行查询(查询SQL)

            if 结果:
                知识库服务日志器.info(f"获取到 {len(结果)} 个可用AI模型")
                return {"success": True, "data": 结果}
            else:
                知识库服务日志器.warning("没有找到可用的AI模型")
                return {"success": True, "data": []}

        except Exception as e:
            知识库服务日志器.error(f"获取可用AI模型列表失败: {str(e)}")
            return {"success": False, "error": f"获取可用AI模型列表失败: {str(e)}"}

    async def 获取默认嵌入模型(self) -> Dict[str, Any]:
        """获取默认的嵌入模型"""
        try:
            # 获取第一个可用的嵌入模型作为默认模型
            模型列表结果 = await self.获取可用嵌入模型列表()

            if 模型列表结果["success"] and 模型列表结果["data"]:
                默认模型 = 模型列表结果["data"][0]
                return {"success": True, "data": 默认模型}
            else:
                return {"success": False, "error": "没有可用的嵌入模型"}

        except Exception as e:
            知识库服务日志器.error(f"获取默认嵌入模型失败: {str(e)}")
            return {"success": False, "error": f"获取默认嵌入模型失败: {str(e)}"}

    # AI模型相关方法移除 - 应该在智能体服务中处理

    async def _获取指定嵌入模型(self, 模型ID: int) -> Dict[str, Any]:
        """根据ID获取指定的嵌入模型"""
        try:
            查询SQL = """
            SELECT
                id,
                模型名称,
                模型类型,
                显示名称,
                提供商,
                模型参数,
                启用状态
            FROM langchain_模型配置表
            WHERE id = %s
            AND 模型类型 LIKE '%embedding%'
            AND 启用状态 = 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL, (模型ID,))

            if 结果:
                模型信息 = 结果[0]
                模型参数 = 模型信息.get("模型参数", {})

                # 解析模型参数
                if isinstance(模型参数, str):
                    try:
                        模型参数 = json.loads(模型参数)
                    except (json.JSONDecodeError, ValueError) as e:
                        知识库服务日志器.warning(f"解析模型参数失败: {str(e)}")
                        模型参数 = {}

                return {
                    "success": True,
                    "data": {
                        "id": 模型信息["id"],
                        "模型名称": 模型信息["模型名称"],
                        "显示名称": 模型信息["显示名称"],
                        "提供商": 模型信息["提供商"],
                        "向量维度": 模型参数.get("embedding_dimension", 1536),
                        "支持语言": 模型参数.get("supported_languages", "中文/英文"),
                        "模型参数": 模型参数,
                    },
                }
            else:
                return {"success": False, "error": f"未找到ID为 {模型ID} 的嵌入模型"}

        except Exception as e:
            知识库服务日志器.error(f"获取指定嵌入模型失败: {str(e)}")
            return {"success": False, "error": f"获取指定嵌入模型失败: {str(e)}"}

    # ==================== Chroma向量存储管理方法 ====================

    # ==================== 云服务管理方法（预留接口） ====================

    async def 重置ChromaDB存储(self, 知识库ID: int) -> Dict[str, Any]:
        """重置知识库的ChromaDB存储"""
        try:
            # 使用RAG引擎重置ChromaDB
            # 初始化RAG引擎
            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化(智能体id=知识库ID)

            # 清空并重新初始化ChromaDB
            清空结果 = await RAG引擎实例.清空Chroma集合()

            if 清空结果.get("success"):
                知识库服务日志器.info(f"知识库 {知识库ID} 的ChromaDB存储已重置")
                return {
                    "success": True,
                    "message": "ChromaDB存储已重置",
                    "知识库ID": 知识库ID,
                }
            else:
                return {
                    "success": False,
                    "error": 清空结果.get("error", "重置ChromaDB存储失败"),
                }

        except Exception as e:
            知识库服务日志器.error(f"重置ChromaDB存储失败: {str(e)}")
            return {"success": False, "error": f"重置ChromaDB存储失败: {str(e)}"}


# 创建全局实例
LangChain知识库服务实例 = LangChain知识库服务()
