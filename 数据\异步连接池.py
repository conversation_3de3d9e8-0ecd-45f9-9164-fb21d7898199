import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

import aiomysql

from config import 数据库配置
from 日志 import 数据库日志器, 错误日志器


class 异步数据库连接池:
    """极简高效的异步数据库连接池"""

    _实例: Optional["异步数据库连接池"] = None
    _锁 = asyncio.Lock()

    def __new__(cls) -> "异步数据库连接池":
        if cls._实例 is None:
            cls._实例 = super().__new__(cls)
        return cls._实例

    def __init__(self):
        if hasattr(self, "_已初始化"):
            return

        self._连接池: Optional[aiomysql.Pool] = None
        self._已初始化 = True
        self._连接池状态 = "未初始化"  # 新增：连接池状态跟踪
        self._上次初始化时间 = None  # 新增：记录上次初始化时间
        self._初始化失败次数 = 0  # 新增：记录初始化失败次数
        self._连接失败次数 = 0  # 新增：记录连接失败次数
        数据库日志器.info("数据库连接池实例创建完成")

    async def 初始化数据库连接池(self) -> None:
        """初始化数据库连接池"""
        async with self._锁:
            # 如果连接池已存在且未关闭，直接返回
            if self._连接池 and not self._连接池.closed:
                return

            try:
                数据库日志器.info("正在初始化数据库连接池...")

                # 关闭现有连接池（如果存在）
                await self._安全关闭连接池()

                # 保守的连接池配置，确保兼容性
                self._连接池 = await aiomysql.create_pool(
                    host=数据库配置["host"],
                    port=3306,
                    user=数据库配置["user"],
                    password=数据库配置["password"],
                    db=数据库配置["database"],
                    charset="utf8mb4",
                    cursorclass=aiomysql.DictCursor,
                    maxsize=12,  # 略微减少但不影响并发性能
                    minsize=2,  # 保持最小连接数
                    connect_timeout=8,  # 适中的连接超时时间
                    autocommit=True,
                    pool_recycle=900,  # 15分钟回收，确保比大多数服务器的wait_timeout短
                    echo=False,
                )

                # 更新状态信息
                self._连接池状态 = "正常"
                self._上次初始化时间 = datetime.now()
                self._初始化失败次数 = 0  # 重置失败计数
                self._连接失败次数 = 0  # 重置失败计数

                数据库日志器.info(
                    "数据库连接池初始化成功 [最大连接数: 12, 连接回收时间: 900s]"
                )

            except Exception as e:
                self._连接池状态 = "初始化失败"
                self._初始化失败次数 += 1
                错误日志器.error(
                    f"初始化连接池失败 (第{self._初始化失败次数}次): {str(e)}"
                )
                raise

    @asynccontextmanager
    async def 获取连接(self) -> AsyncGenerator[aiomysql.Connection, None]:
        """获取数据库连接 - 增强版本，支持自动恢复"""
        连接 = None
        重试次数 = 0
        最大重试次数 = 3  # 增加重试次数

        while 重试次数 <= 最大重试次数:
            try:
                # 检查并尝试恢复连接池状态
                await self._检查并恢复连接池状态()

                # 确保连接池已初始化且可用
                if not self._连接池 or self._连接池.closed:
                    数据库日志器.info("连接池未初始化或已关闭，正在重新初始化...")
                    await self.初始化数据库连接池()

                # 再次检查连接池状态
                if self._连接池状态 in ["初始化失败", "重新初始化失败"]:
                    if 重试次数 < 最大重试次数:
                        数据库日志器.warning(
                            f"连接池状态异常 '{self._连接池状态}'，尝试强制重新初始化 (第{重试次数 + 1}次)"
                        )
                        await self._强制重新初始化()
                        重试次数 += 1
                        await asyncio.sleep(1.0 * 重试次数)  # 递增等待时间
                        continue
                    else:
                        错误日志器.error(
                            f"连接池处于无效状态 '{self._连接池状态}'，已达到最大重试次数"
                        )
                        raise ConnectionError(
                            f"数据库连接池状态异常: {self._连接池状态}"
                        )

                # 最终检查连接池是否可用
                if not self._连接池 or self._连接池.closed:
                    raise ConnectionError("连接池初始化后仍然不可用")

                # 使用适中的超时时间获取连接
                async with asyncio.timeout(10):  # 增加超时时间
                    连接 = await self._连接池.acquire()
                    # 测试连接有效性
                    async with 连接.cursor() as 游标:
                        await 游标.execute("SELECT 1")

                try:
                    yield 连接
                finally:
                    # 确保连接被释放
                    if 连接 and self._连接池 and not self._连接池.closed:
                        try:
                            self._连接池.release(连接)
                        except Exception as release_error:
                            数据库日志器.warning(f"释放连接时发生异常: {release_error}")

                # 成功获取连接，重置失败计数
                self._连接失败次数 = 0
                return

            except Exception as e:
                原始错误信息 = str(e)
                错误信息_小写 = 原始错误信息.lower()
                self._连接失败次数 += 1
                重试次数 += 1

                # 记录详细的错误信息
                # 修复：避免错误信息中的%s导致格式化问题
                安全错误信息 = 原始错误信息.replace("%", "%%")  # 转义%符号
                数据库日志器.warning(
                    f"获取数据库连接失败 (第{重试次数}次): {安全错误信息}"
                )

                # 检查是否是可恢复的连接问题
                可恢复错误关键词 = [
                    "lost connection",
                    "connection closed",
                    "server has gone away",
                    "cannot acquire connection",
                    "timeout",
                    "connection refused",
                    "connection reset",
                    "broken pipe",
                ]

                if (
                    any(词 in 错误信息_小写 for 词 in 可恢复错误关键词)
                    and 重试次数 <= 最大重试次数
                ):
                    数据库日志器.info("检测到可恢复的连接问题，准备重新初始化连接池")
                    try:
                        await self._强制重新初始化()
                        await asyncio.sleep(0.5 * 重试次数)  # 递增等待
                    except Exception as init_error:
                        数据库日志器.error(f"重新初始化连接池失败: {init_error}")
                        if 重试次数 >= 最大重试次数:
                            raise
                else:
                    # 非连接问题或已达到最大重试次数，直接抛出
                    if 重试次数 > 最大重试次数:
                        错误日志器.error(
                            f"获取数据库连接失败，已重试{最大重试次数}次，最后错误: {str(e)}"
                        )
                        raise ConnectionError(
                            f"无法获取数据库连接，请检查数据库服务是否正常。最后错误: {str(e)}"
                        )
                    else:
                        raise

        # 理论上不会到达这里，但为了安全起见
        raise ConnectionError(f"获取数据库连接失败，已重试{最大重试次数}次")

    async def _安全关闭连接池(self) -> None:
        """安全关闭现有连接池"""
        if self._连接池:
            try:
                # 标记状态为正在关闭，防止新的连接请求
                self._连接池状态 = "正在关闭"

                # 关闭连接池
                self._连接池.close()

                try:
                    # 等待所有连接关闭，使用较短的超时时间
                    await asyncio.wait_for(self._连接池.wait_closed(), timeout=2)
                    self._连接池状态 = "已关闭"
                    数据库日志器.info("数据库连接池已安全关闭")
                except asyncio.TimeoutError:
                    数据库日志器.warning("关闭连接池超时，强制继续执行")
                    self._连接池状态 = "关闭超时"
                except Exception as e:
                    数据库日志器.warning(f"等待连接池关闭时发生异常: {str(e)}")
                    self._连接池状态 = "关闭失败"

            except Exception as e:
                数据库日志器.warning(f"关闭连接池失败: {str(e)}")
                self._连接池状态 = "关闭失败"
            finally:
                # 无论关闭成功与否，都将引用置空，防止内存泄漏
                self._连接池 = None

    async def _检查并恢复连接池状态(self) -> None:
        """检查连接池状态并尝试自动恢复"""
        # 如果连接池处于关闭相关状态，尝试恢复
        if self._连接池状态 in ["关闭超时", "关闭失败", "强制关闭"]:
            数据库日志器.info(f"检测到连接池状态为 '{self._连接池状态}'，尝试自动恢复")
            try:
                # 重置状态并重新初始化
                self._连接池状态 = "恢复中"
                await self._强制重新初始化()
                数据库日志器.info("连接池状态恢复成功")
            except Exception as e:
                数据库日志器.error(f"连接池状态恢复失败: {str(e)}")
                self._连接池状态 = "恢复失败"

    async def _强制重新初始化(self) -> None:
        """强制重新初始化连接池，忽略当前状态"""
        async with self._锁:
            try:
                数据库日志器.info("开始强制重新初始化连接池...")

                # 强制关闭现有连接池，不管当前状态
                if self._连接池:
                    try:
                        self._连接池.close()
                        # 不等待关闭完成，直接继续
                        await asyncio.sleep(0.1)
                    except Exception as close_error:
                        数据库日志器.warning(f"强制关闭连接池时发生异常: {close_error}")
                    finally:
                        self._连接池 = None

                # 重置状态
                self._连接池状态 = "强制初始化中"

                # 重新创建连接池
                self._连接池 = await aiomysql.create_pool(
                    host=数据库配置["host"],
                    port=3306,
                    user=数据库配置["user"],
                    password=数据库配置["password"],
                    db=数据库配置["database"],
                    charset="utf8mb4",
                    cursorclass=aiomysql.DictCursor,
                    maxsize=12,
                    minsize=2,
                    connect_timeout=10,  # 增加连接超时时间
                    autocommit=True,
                    pool_recycle=900,
                    echo=False,
                )

                # 测试连接池
                if not self._连接池:
                    raise Exception("连接池创建失败")

                async with self._连接池.acquire() as test_conn:
                    async with test_conn.cursor() as cursor:
                        await cursor.execute("SELECT 1")
                        result = await cursor.fetchone()
                        if not (result and result["1"] == 1):
                            raise Exception("连接池测试失败")

                # 更新状态信息
                self._连接池状态 = "正常"
                self._上次初始化时间 = datetime.now()
                self._初始化失败次数 = 0
                self._连接失败次数 = 0

                数据库日志器.info("强制重新初始化连接池成功")

            except Exception as e:
                self._连接池状态 = "强制初始化失败"
                self._初始化失败次数 += 1
                错误日志器.error(f"强制重新初始化失败: {str(e)}")
                raise

    async def _重新初始化(self) -> None:
        """重新初始化连接池"""
        async with self._锁:
            try:
                # 安全关闭现有连接池
                await self._安全关闭连接池()

                # 短暂等待后重新初始化
                await asyncio.sleep(0.2)
                await self.初始化数据库连接池()

            except Exception as e:
                错误日志器.error(f"重新初始化失败: {str(e)}")
                # 标记状态
                self._连接池状态 = "重新初始化失败"

    async def 执行查询(
        self, sql: str, 参数: Optional[tuple] = None
    ) -> List[Dict[str, Any]]:
        """执行查询操作"""
        async with self.获取连接() as 连接:
            async with 连接.cursor(aiomysql.DictCursor) as 游标:
                await 游标.execute(sql, 参数)
                return await 游标.fetchall()

    async def 执行更新(self, sql: str, 参数: Optional[tuple] = None) -> int:
        """执行更新操作"""
        async with self.获取连接() as 连接:
            async with 连接.cursor(aiomysql.DictCursor) as 游标:
                await 游标.execute(sql, 参数)
                return 游标.rowcount

    async def 执行插入(
        self, sql: str, 参数: Optional[tuple] = None
    ) -> Optional[int]:
        """执行插入操作"""
        async with self.获取连接() as 连接:
            async with 连接.cursor(aiomysql.DictCursor) as 游标:
                await 游标.execute(sql, 参数)
                插入id = 连接.insert_id()
                return 插入id if 插入id and 插入id > 0 else None

    async def 执行数据库删除(self, sql: str, 参数: Optional[tuple] = None) -> int:
        """执行删除操作"""
        return await self.执行更新(sql, 参数)

    async def 执行数据库批量插入(self, sql: str, 参数列表: List[tuple]) -> int:
        """
        执行批量插入操作

        参数:
            sql: SQL插入语句
            参数列表: 参数元组列表

        返回:
            int: 影响的行数
        """
        if not 参数列表:
            return 0

        async with self.获取连接() as 连接:
            async with 连接.cursor(aiomysql.DictCursor) as 游标:
                await 游标.executemany(sql, 参数列表)
                return 游标.rowcount



    async def 执行删除(self, sql: str, 参数: Optional[tuple] = None) -> int:
        return await self.执行数据库删除(sql, 参数)

    async def 执行事务(
        self, sql_列表: List[tuple], 参数_列表: Optional[List[tuple]] = None
    ) -> bool:
        """执行事务操作"""
        if not sql_列表:
            return True

        async with self.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                try:
                    await 连接.begin()

                    for i, sql in enumerate(sql_列表):
                        参数 = (
                            参数_列表[i] if 参数_列表 and i < len(参数_列表) else None
                        )
                        await 游标.execute(sql, 参数)

                    await 连接.commit()
                    return True

                except Exception as e:
                    await 连接.rollback()
                    raise e

    async def 关闭数据库连接池(self) -> None:
        """关闭连接池"""
        async with self._锁:
            await self._安全关闭连接池()
            # self._连接池状态 已在 _安全关闭连接池 中更新
            # self._连接池 也已在 _安全关闭连接池 中置空
            数据库日志器.info("数据库连接池关闭完成")

    async def 优雅关闭(self) -> None:
        """优雅关闭连接池，用于应用关闭时调用"""
        try:
            # 标记连接池为正在关闭状态
            self._连接池状态 = "正在关闭"

            # 等待当前正在进行的操作完成（最多等待5秒）
            await asyncio.sleep(0.1)

            # 关闭连接池
            await self.关闭数据库连接池()

        except Exception as e:
            错误日志器.error(f"优雅关闭连接池时发生异常: {str(e)}")
        finally:
            # 确保状态被正确设置
            if self._连接池状态 not in ["已关闭", "关闭超时"]:
                self._连接池状态 = "强制关闭"

    async def 获取连接池状态(self) -> Dict[str, Any]:
        """获取连接池状态信息"""
        if not self._连接池:
            return {
                "状态": "未初始化",
                "连接数": 0,
                "初始化失败次数": self._初始化失败次数,
            }

        return {
            "状态": self._连接池状态,
            "连接数": self._连接池.size,
            "空闲连接数": self._连接池.freesize,
            "最大连接数": self._连接池.maxsize,
            "最小连接数": self._连接池.minsize,
            "上次初始化时间": self._上次初始化时间.strftime("%Y-%m-%d %H:%M:%S")
            if self._上次初始化时间
            else None,
            "初始化失败次数": self._初始化失败次数,
            "连接失败次数": self._连接失败次数,
        }

    async def 健康检查(self) -> Dict[str, Any]:
        """执行连接池健康检查"""
        检查结果 = {
            "健康状态": "未知",
            "检查时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "详细信息": {},
        }

        try:
            # 检查连接池基本状态
            if not self._连接池 or self._连接池.closed:
                检查结果["健康状态"] = "不健康"
                检查结果["详细信息"]["错误"] = "连接池未初始化或已关闭"
                return 检查结果

            # 检查连接池状态
            if self._连接池状态 not in ["正常"]:
                检查结果["健康状态"] = "警告"
                检查结果["详细信息"]["警告"] = f"连接池状态异常: {self._连接池状态}"

            # 尝试获取连接并执行简单查询
            async with asyncio.timeout(5):
                async with self.获取连接() as 连接:
                    async with 连接.cursor() as 游标:
                        await 游标.execute("SELECT 1 as health_check")
                        结果 = await 游标.fetchone()
                        if 结果 and 结果["health_check"] == 1:
                            if 检查结果["健康状态"] == "未知":
                                检查结果["健康状态"] = "健康"
                            检查结果["详细信息"]["数据库连接"] = "正常"
                        else:
                            检查结果["健康状态"] = "不健康"
                            检查结果["详细信息"]["错误"] = "数据库查询测试失败"

            # 添加连接池统计信息
            检查结果["详细信息"]["连接池统计"] = await self.获取连接池状态()

        except asyncio.TimeoutError:
            检查结果["健康状态"] = "不健康"
            检查结果["详细信息"]["错误"] = "健康检查超时"
        except Exception as e:
            检查结果["健康状态"] = "不健康"
            检查结果["详细信息"]["错误"] = f"健康检查异常: {str(e)}"

        return 检查结果

    async def 自动修复(self) -> bool:
        """尝试自动修复连接池问题"""
        try:
            数据库日志器.info("开始自动修复连接池...")

            # 执行健康检查
            健康状态 = await self.健康检查()

            if 健康状态["健康状态"] == "健康":
                数据库日志器.info("连接池状态正常，无需修复")
                return True

            # 尝试强制重新初始化
            await self._强制重新初始化()

            # 再次检查健康状态
            修复后状态 = await self.健康检查()

            if 修复后状态["健康状态"] == "健康":
                数据库日志器.info("连接池自动修复成功")
                return True
            else:
                数据库日志器.error(f"连接池自动修复失败: {修复后状态}")
                return False

        except Exception as e:
            数据库日志器.error(f"自动修复过程中发生异常: {str(e)}")
            return False

    # 兼容性方法：确保向后兼容
    @asynccontextmanager
    async def get_connection(self):
        """向后兼容的连接获取方法"""
        async with self.获取连接() as 连接:
            yield 连接


# 创建全局实例
异步连接池实例 = 异步数据库连接池()


# 极简的异步接口日志插入函数
async def 异步插入接口日志(
    用户id: Optional[int] = None,
    ip: str = "",
    路径: str = "",
    方法: str = "",
    状态码: int = 0,
    耗时: float = 0.0,
    错误详情: Optional[str] = None,
    堆栈跟踪: Optional[str] = None,
    请求头: str = "",
    请求参数: str = "",
    请求体: str = "",
):
    """异步插入接口调用日志，带重试机制"""

    # 简单截断
    def 截断(文本: str, 长度: int) -> str:
        return (文本[:长度] + "...") if len(文本) > 长度 else 文本

    # 准备参数
    参数 = (
        用户id,
        截断(ip, 45),
        截断(路径, 255),
        截断(方法, 10),
        状态码,
        round(耗时, 4),
        截断(错误详情 or "", 1000),
        截断(堆栈跟踪 or "", 3000),
        截断(请求头, 2000),
        截断(请求参数, 2000),
        截断(请求体, 5000),
    )

    # 重构版本：消除嵌套try-except，简化重试逻辑
    最大重试次数 = 2
    重试次数 = 0
    最后异常 = None

    while 重试次数 <= 最大重试次数:
        try:
            sql = """INSERT INTO 接口日志表 (
                用户ID, IP地址, 请求路径, 请求方法, 状态码, 耗时,
                错误信息, 堆栈跟踪, 请求头, 请求参数, 请求体, 创建时间
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())"""

            await 异步连接池实例.执行插入(sql, 参数)
            return  # 成功则直接返回

        except Exception as e:
            最后异常 = e
            重试次数 += 1
            错误信息 = str(e).lower()

            # 检查是否是连接问题且还有重试机会
            连接问题关键词 = [
                "lost connection",
                "connection closed",
                "server has gone away",
                "cannot acquire connection",
            ]
            是连接问题 = any(词 in 错误信息 for 词 in 连接问题关键词)

            if 是连接问题 and 重试次数 <= 最大重试次数:
                # 连接问题，尝试重新初始化连接池
                await _安全重新初始化连接池(重试次数)
            else:
                # 非连接问题或已达到最大重试次数，停止重试
                break

    # 所有重试都失败，记录最终错误
    if 最后异常:
        _记录接口日志失败(最后异常, 最大重试次数)


async def _安全重新初始化连接池(重试次数: int):
    """安全地重新初始化连接池，忽略失败"""
    try:
        await 异步连接池实例._重新初始化()
        await asyncio.sleep(0.5 * 重试次数)  # 指数退避
    except Exception:
        # 忽略重新初始化失败，不影响主流程
        pass


def _记录接口日志失败(异常: Exception, 重试次数: int):
    """安全地记录接口日志失败信息"""
    try:
        错误日志器.error(f"插入接口日志失败 (已重试{重试次数}次): {str(异常)}")
    except Exception:
        # 如果连日志都无法记录，则使用print作为最后手段
        try:
            print(f"日志记录失败: {str(异常)}")
        except Exception:
            # 连print都失败了，静默忽略
            pass
