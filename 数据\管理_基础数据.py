"""
SuperAdmin基础数据操作模块
包含SuperAdmin权限验证、用户管理、系统监控等核心数据操作功能

@deprecated: 此文件中的大部分同步函数已废弃，请使用对应的异步版本：
- 用户管理相关：使用 数据.管理_用户管理 中的异步函数
- 统计分析相关：使用 数据.管理_统计分析 中的异步函数
- 系统监控相关：使用 数据.管理_系统监控 中的异步函数
"""

import datetime
import platform
import time
from typing import Optional, Dict, Any

import psutil
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例


# 导入统一日志系统

async def 获取用户总数():
    结果 = await 异步连接池实例.执行查询("SELECT COUNT(*) AS 用户总数 FROM 用户表")
    return 结果[0]["用户总数"] if 结果 else 0

async def 获取今日新增用户():
    today = datetime.datetime.now().strftime('%Y-%m-%d')
    结果 = await 异步连接池实例.执行查询(
        "SELECT COUNT(*) AS 今日新增 FROM 用户表 WHERE DATE(created_at) = $1",
        (today,)
    )
    return 结果[0]["今日新增"] if 结果 else 0

async def 获取活跃用户数():
    # 最近7天有登录记录的用户
    七天前 = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
    结果 = await 异步连接池实例.执行查询(
        """
        SELECT COUNT(DISTINCT 用户id) AS 活跃用户
        FROM 用户登陆记录表
        WHERE 登陆时间 >= %s
        """,
        (七天前,)
    )
    return 结果[0]["活跃用户"] if 结果 else 0

async def 获取系统信息():
    try:
        # 获取磁盘使用情况，Windows系统使用C盘
        if platform.system() == "Windows":
            磁盘路径 = "C:\\"  # 修正Windows路径格式，使用双反斜杠
        else:
            磁盘路径 = "/"
            
        # 获取CPU使用率 - 使用替代方法
        try:
            if platform.system() == "Windows":
                # 使用 subprocess 调用 wmic 命令获取 CPU 使用率
                import subprocess
                cpu_output = subprocess.check_output('wmic cpu get loadpercentage', shell=True)
                cpu_output = cpu_output.decode('utf-8').strip().split('\n')
                if len(cpu_output) >= 2:
                    cpu_load = cpu_output[1].strip()
                    cpu使用率 = f"{cpu_load}%"
                else:
                    cpu使用率 = "N/A"
            else:
                cpu使用率 = f"{psutil.cpu_percent(interval=0.1)}%"
        except Exception as e:
            print(f"获取CPU使用率失败: {e}")
            cpu使用率 = "N/A"
            
        # 获取内存使用率
        try:
            内存使用率 = f"{psutil.virtual_memory().percent }%"
        except Exception as e:
            print(f"获取内存使用率失败: {e}")
            内存使用率 = "N/A"
            
        # 获取磁盘使用率
        try:
            磁盘使用率 = f"{psutil.disk_usage(磁盘路径).percent}%"
        except Exception as e:
            print(f"获取磁盘使用率失败: {e}")
            磁盘使用率 = "N/A"
            
        # 获取运行时间
        try:
            启动时间 = psutil.boot_time()
            当前时间 = time.time()
            运行秒数 = int(当前时间 - 启动时间)
            小时 = 运行秒数 // 3600
            分钟 = (运行秒数 % 3600) // 60
            秒 = 运行秒数 % 60
            运行时间 = f"{小时}小时{分钟}分钟{秒}秒"
        except Exception as e:
            print(f"获取运行时间失败: {e}")
            运行时间 = "N/A"
            
        # 获取用户统计数据
        try:
            用户总数 = await 获取用户总数()
        except Exception as e:
            print(f"获取用户总数失败: {e}")
            用户总数 = 0

        try:
            今日新增 = await 获取今日新增用户()
        except Exception as e:
            print(f"获取今日新增用户失败: {e}")
            今日新增 = 0

        try:
            活跃用户 = await 获取活跃用户数()
        except Exception as e:
            print(f"获取活跃用户数失败: {e}")
            活跃用户 = 0
            
        return {
            "操作系统": platform.system() + " " + platform.release(),
            "CPU使用率": cpu使用率,
            "内存使用率": 内存使用率,
            "磁盘使用率": 磁盘使用率,
            "运行时间": 运行时间,
            "Python版本": platform.python_version(),
            "用户总数": 用户总数,
            "今日新增": 今日新增,
            "活跃用户": 活跃用户
        }
    except Exception as e:
        print(f"获取系统信息失败: {e}")
        return {
            "操作系统": platform.system() + " " + platform.release(),
            "CPU使用率": "N/A",
            "内存使用率": "N/A",
            "磁盘使用率": "N/A",
            "运行时间": "N/A",
            "Python版本": platform.python_version(),
            "用户总数": 0,
            "今日新增": 0,
            "活跃用户": 0
        }

async def 获取最近接口调用():
    # 从数据库中获取接口调用统计数据
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                请求路径,
                COUNT(*) AS 调用次数,
                AVG(耗时) AS 平均耗时
            FROM 接口日志表
            GROUP BY 请求路径
            ORDER BY 调用次数 DESC
            LIMIT 10
            """
        )
        if not 结果:
                # 如果没有数据，返回示例数据
                return [
                    {"请求路径": "/api/login", "调用次数": 120, "平均耗时": 0.25},
                    {"请求路径": "/api/register", "调用次数": 45, "平均耗时": 0.35},
                    {"请求路径": "/api/user/profile", "调用次数": 78, "平均耗时": 0.15},
                    {"请求路径": "/api/products", "调用次数": 230, "平均耗时": 0.55},
                    {"请求路径": "/api/orders", "调用次数": 67, "平均耗时": 0.85},
                    {"请求路径": "/api/payment", "调用次数": 32, "平均耗时": 1.25}
                ]

        return [
                {
                    "请求路径": 行["请求路径"],
                    "调用次数": 行["调用次数"],
                    "平均耗时": round(float(行["平均耗时"]), 2) if 行["平均耗时"] else 0
                }
                for 行 in 结果
            ]
    except Exception as e:
        print(f"获取接口调用数据失败: {e}")
        # 出错时返回示例数据
        return [
            {"请求路径": "/api/login", "调用次数": 120, "平均耗时": 0.25},
            {"请求路径": "/api/register", "调用次数": 45, "平均耗时": 0.35},
            {"请求路径": "/api/user/profile", "调用次数": 78, "平均耗时": 0.15},
            {"请求路径": "/api/products", "调用次数": 230, "平均耗时": 0.55},
            {"请求路径": "/api/orders", "调用次数": 67, "平均耗时": 0.85},
            {"请求路径": "/api/payment", "调用次数": 32, "平均耗时": 1.25}
        ]

def 获取会员统计():
    """
    @deprecated: 此函数已废弃，请使用 数据.管理_统计分析.异步获取会员统计()
    """
    # 返回示例数据，避免运行时错误
    return [
        {"会员类型": "免费用户", "数量": 1200},
        {"会员类型": "基础会员", "数量": 450},
        {"会员类型": "高级会员", "数量": 180},
        {"会员类型": "VIP会员", "数量": 70}
    ]

def 获取用户列表(page: int = 1, size: int = 10, search: Optional[str] = None) -> Dict[str, Any]:
    """
    @deprecated: 此函数已废弃，请使用 数据.管理_用户管理.异步获取用户列表()
    """
    return {
        "用户列表": [],
        "总数": 0,
        "页码": page,
        "每页数量": size
    }

def 添加用户(用户名, 邮箱, 密码, 手机号='', 用户类型='普通用户'):
    """
    @deprecated: 此函数已废弃，请使用 数据.管理_用户管理 中的异步函数
    """
    raise NotImplementedError("此函数已废弃，请使用异步版本")

# 以下同步函数已废弃，请使用对应的异步版本



if __name__ == "__main__":
    import asyncio
    async def test():
        print(await 获取用户总数())
        print(await 获取系统信息())
    asyncio.run(test())