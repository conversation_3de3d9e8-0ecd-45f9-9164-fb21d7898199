import asyncio
import json
import re  # 添加正则表达式库
import traceback

# 已使用统一日志系统替代
# import logging
# 已使用统一日志系统替代
from fastapi import HTTPException, status

from AI.COZE import Coze客户端
# PostgreSQL数据操作导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.用户 import 根据用户ID获取用户信息 as 异步获取用户信息
from 数据.管理数据操作 import 获取系统配置

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.异步数据库函数 import 异步查询微信对接进度

from 数据模型.响应模型 import 统一响应模型
from 日志 import 系统日志器, 错误日志器

from 状态 import 状态

# 使用统一日志系统替代
# logger = logging.getLogger("ai_service")


class AI对话服务:
    """AI对话服务类，负责处理AI对话请求和相关业务逻辑"""

    def __init__(self):
        self.Coze客户端 = Coze客户端()

        # 使用调试级别，生产环境可改为INFO或WARNING
        # logger.setLevel(logging.DEBUG)

    async def 获取历史寄样记录(self, 我方微信号, 对方微信号):
        """
        获取历史寄样记录

        参数:
            我方微信号: 我方微信号
            对方微信号: 对方微信号

        返回:
            历史寄样记录的JSON字符串
        """
        try:
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    # 查询微信号对应的微信信息ID
                    await 游标.execute(
                        """
                        SELECT id FROM 微信信息表 WHERE 微信号 = %s
                        """,
                        (我方微信号,),
                    )
                    我方微信号结果 = await 游标.fetchone()

                    if not 我方微信号结果:
                        错误日志器.warning(
                            f"未找到我方微信号: {我方微信号} 对应的微信信息ID"
                        )
                        return "[]"

                    我方微信号ID = 我方微信号结果["id"]

                    await 游标.execute(
                        """
                        SELECT id FROM 微信信息表 WHERE 微信号 = %s
                        """,
                        (对方微信号,),
                    )
                    对方微信号结果 = await 游标.fetchone()

                    if not 对方微信号结果:
                        错误日志器.warning(
                            f"未找到对方微信号: {对方微信号} 对应的微信信息ID"
                        )
                        return "[]"

                    对方微信号ID = 对方微信号结果["id"]

                    # 查询微信产品对接进度表中的记录
                    await 游标.execute(
                        """
                        SELECT id FROM 微信产品对接进度表 
                        WHERE 我方微信号id = %s AND 对方微信号id = %s
                        """,
                        (我方微信号ID, 对方微信号ID),
                    )
                    对接进度结果 = await 游标.fetchall()

                    if not 对接进度结果:
                        错误日志器.info(
                            f"未找到我方微信号ID: {我方微信号ID} 和对方微信号ID: {对方微信号ID} 对应的微信产品对接进度记录"
                        )
                        return "[]"

                    # 获取所有对接进度ID
                    对接进度IDs = [记录["id"] for 记录 in 对接进度结果]

                    # 查询样品信息记录表中的记录
                    placeholders = ", ".join(["%s"] * len(对接进度IDs))
                    await 游标.execute(
                        f"""
                        SELECT `地址`, `收件人`, `电话`, `用户产品表id`, `数量`, `快递单号`, `快递状态`, `快递公司`, `创建时间`
                        FROM 样品信息记录表
                        WHERE 微信产品对接进度表ID IN ({placeholders})
                        ORDER BY 创建时间 DESC
                        """,
                        tuple(对接进度IDs),
                    )
                    寄样记录结果 = await 游标.fetchall()

                    if not 寄样记录结果:
                        错误日志器.info(
                            f"未找到对接进度IDs: {对接进度IDs} 对应的样品信息记录"
                        )
                        return "[]"

                    # 处理样品记录数据，转换日期时间等不可序列化的字段
                    处理后寄样记录 = []
                    for 记录 in 寄样记录结果:
                        处理后记录 = {}
                        for 键, 值 in 记录.items():
                            if hasattr(值, "strftime"):  # 处理日期时间类型
                                处理后记录[键] = 值.strftime("%Y-%m-%d %H:%M:%S")
                            else:
                                处理后记录[键] = 值
                        处理后寄样记录.append(处理后记录)

                    # 转换为JSON字符串
                    return json.dumps(处理后寄样记录, ensure_ascii=False)

        except Exception as e:
            错误日志器.error(f"获取历史寄样记录失败: {str(e)}", exc_info=True)
            return "[]"  # 发生错误时返回空数组

    async def 处理对话请求(self, 请求数据, 用户id):
        """
        处理AI对话请求的核心业务逻辑

        参数:
            请求数据: 包含对话内容和微信标识信息
            用户id: 当前用户ID

        返回:
            AI助手的回复
        """
        try:
            用户标识 = f"{请求数据.我方微信号}-{请求数据.对方微信号}"
            # print(f"生成的用户标识: {用户标识}")  # DEBUG

            conversation_id = None  # 新增：存储Coze的会话ID，从数据库获取或Coze返回
            智能体id = None
            员工名称 = None
            员工性格 = None
            店铺名称 = None
            # 算力模式下不需要对话次数限制

            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    # 1. 获取用户默认AI配置
                    默认AI配置 = await self._获取用户默认AI配置(用户id, 游标)
                    if (
                        isinstance(默认AI配置, dict)
                        and "status" in 默认AI配置
                        and 默认AI配置["status"] != 100
                    ):
                        return 默认AI配置

                    # 2. 检查AI智能体有效性
                    智能体结果 = await self._检查AI智能体有效性(
                        默认AI配置["配置ID"], 用户id, 游标
                    )
                    if (
                        isinstance(智能体结果, dict)
                        and "status" in 智能体结果
                        and 智能体结果["status"] != 100
                    ):
                        return 智能体结果

                    # 算力模式下不需要检查对话次数上限

                    # 2. 获取Coze会话ID
                    conversation_id = await self._获取或管理Coze会话ID(用户标识, 游标)

                    # 3. 从智能体结果获取其他必要信息
                    智能体id = 智能体结果["智能体id"]
                    员工名称 = 智能体结果["员工名称"]
                    员工性格 = 智能体结果["员工性格"]
                    店铺名称 = 智能体结果["店铺名称"]

            # 确保有必要的数据后再继续
            if not 智能体id:  # 套餐结果有效性已在上面检查过
                return 统一响应模型.失败(
                    状态码=状态.AI.参数无效, 消息="获取套餐信息失败，无法确定智能体ID"
                ).转字典()

            # 6. 获取微信对接状态 (移到 Coze 调用前，因为它可能作为上下文)
            当前合作状态 = await 异步查询微信对接进度(
                我方微信号=请求数据.我方微信号, 对方微信号=请求数据.对方微信号
            )
            映射后合作状态 = self._应用状态映射(当前合作状态)
            当前合作状态_JSON = json.dumps(
                映射后合作状态, ensure_ascii=False, default=self._json序列化处理
            )
            历史寄样记录 = await self.获取历史寄样记录(
                请求数据.我方微信号, 请求数据.对方微信号
            )

            # 7. 调用Coze
            ai_内容 = None
            新会话ID = None
            try:
                ai_内容, 新会话ID = await self._调用并处理Coze接口(
                    智能体id=智能体id,
                    传入信息=请求数据.内容,
                    用户标识=用户标识,
                    自定义变量={
                        "店铺名称": str(店铺名称),
                        "商务名称": str(员工名称),
                        "员工性格": str(员工性格),
                        "当前合作状态": 当前合作状态_JSON,
                        "最近聊天内容": str(请求数据.最近聊天内容),
                        "历史寄样记录": 历史寄样记录,
                    },
                    conversation_id=conversation_id,
                )
                # 输出异步AI对话服务的原始响应数据
                系统日志器.info("🔍 ===== 异步AI对话服务原始响应数据 =====")
                系统日志器.info(f"📋 Coze原始AI内容: {ai_内容}")
                系统日志器.info(f"📋 新会话ID: {新会话ID}")
                系统日志器.info(f"📋 智能体ID: {智能体id}")
                系统日志器.info(f"📋 用户标识: {用户标识}")
                系统日志器.info(f"📋 传入信息: {请求数据.内容}")
                系统日志器.info("🔍 ===== 异步AI对话服务原始响应数据输出完毕 =====")

                ai_内容 = self._清除知识库引用标记(ai_内容)
                # 只有当内容是字符串时才处理Markdown格式JSON
                if isinstance(ai_内容, str):
                    ai_内容 = self._处理Markdown格式JSON(ai_内容)
                ai_内容 = await self._处理文件消息路径(ai_内容)

                # 输出处理后的AI内容
                系统日志器.info("🔍 ===== 处理后的AI内容 =====")
                系统日志器.info(f"📋 处理后AI内容: {ai_内容}")
                系统日志器.info("🔍 ===== 处理后AI内容输出完毕 =====")
            except asyncio.CancelledError:
                错误日志器.warning("Coze调用被取消")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail={
                        "status": 状态.通用.服务暂时不可用,
                        "message": "AI服务暂时不可用，请稍后再试",
                        "data": None,
                    },
                )
            except Exception as e:
                错误日志器.error(f"调用Coze接口时发生错误: {str(e)}", exc_info=True)
                # 根据错误类型决定是否需要特定响应，这里笼统处理
                return 统一响应模型.失败(
                    状态码=状态.AI.服务异常, 消息=f"AI服务处理出错: {str(e)}"
                ).转字典()

            if isinstance(ai_内容, str) and ai_内容.startswith("处理对话时发生错误"):
                return 统一响应模型.失败(状态码=状态.AI.服务异常, 消息=ai_内容).转字典()

            响应用AI内容 = ai_内容  # 保留一份原始处理后的ai_内容给响应
            新增寄样信息 = []  # 重置新增寄样信息，确保只包含本次对话的

            # 8. 更新Coze会话ID, 尝试解析报表, 保存对话记录 (在一个事务中完成)
            try:
                async with 异步连接池实例.获取连接() as 连接:
                    async with 连接.cursor() as 游标:
                        if 新会话ID and 新会话ID != conversation_id:
                            await self._更新Coze会话ID(用户标识, 新会话ID, 游标)



                        # 确定要记录到 AI对话记录表 的Coze会话ID
                        # 优先使用本次Coze调用返回的新会话ID，如果Coze未返回新的，则使用调用时使用的会话ID
                        coze_session_id_for_log = (
                            新会话ID if 新会话ID else conversation_id
                        )

                        # 获取AI模型信息用于记录
                        AI模型id = 智能体结果.get("模型ID")  # 从智能体结果中获取模型ID
                        消耗算力 = 智能体结果.get(
                            "算力消耗", 1
                        )  # 从智能体结果中获取算力消耗，默认为1

                        await self._保存对话记录(
                            用户id,
                            coze_session_id_for_log,
                            请求数据.内容,
                            ai_内容,
                            AI模型id,
                            消耗算力,
                            游标,
                        )

                        # 算力模式下无需统计次数



            except asyncio.CancelledError:
                错误日志器.warning("后续数据库操作被取消，但AI回复已获取")
            except Exception as e:
                错误日志器.error(
                    f"保存对话记录或解析报表等后续操作失败: {str(e)}", exc_info=True
                )

            # 6. 准备最终响应数据 - 算力模式
            # 如果响应用AI内容是字典且包含"产品讨论列表"，则删除它 (按原逻辑)
            if isinstance(响应用AI内容, dict) and "产品讨论列表" in 响应用AI内容:
                del 响应用AI内容["产品讨论列表"]

            # 算力模式下的响应数据结构
            成功数据 = {"ai": 响应用AI内容, "算力消耗": 消耗算力}

            # 根据新增寄样信息是否存在，调整最终返回的数据结构
            if 新增寄样信息:
                if isinstance(成功数据["ai"], dict):
                    成功数据["ai"]["新增寄样"] = 新增寄样信息
                else:
                    成功数据["新增寄样信息"] = 新增寄样信息

            return 统一响应模型.成功(数据=成功数据, 消息="对话成功").转字典()

        except HTTPException as he:
            raise he
        except asyncio.CancelledError:
            错误日志器.warning("处理对话请求被意外取消")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "status": 状态.通用.服务暂时不可用,
                    "message": "请求已被取消，请稍后再试",
                    "data": None,
                },
            )
        except Exception as e:
            错误详情 = traceback.format_exc()
            错误日志器.error(f"AI对话请求处理中发生未捕获异常: {str(e)}\n{错误详情}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"系统处理AI对话时发生内部错误: {str(e)}",
                    "data": None,
                },
            )

    async def 清除会话上下文(self, 请求数据, 用户id):
        """
        清除用户的会话上下文

        参数:
            请求数据: 包含微信标识信息
            用户id: 当前用户ID

        返回:
            清除结果
        """
        try:
            # 1. 构建用户标识
            用户标识 = f"{请求数据.我方微信号}-{请求数据.对方微信号}"
            # print(f"清除会话上下文 - 用户标识: {用户标识}")  # DEBUG
            # 2. 查询会话ID并验证用户权限
            async with 异步连接池实例.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    # 首先验证用户是否有权限访问此会话
                    # 通过微信号和用户ID的关联来验证权限
                    await 游标.execute(
                        """
                        SELECT c.会话id
                        FROM Coze会话ID表 c
                        WHERE c.用户标识 = %s
                        AND EXISTS (
                            SELECT 1 FROM 微信产品对接进度表 mp
                            INNER JOIN 微信信息表 m1 ON mp.我方微信号ID = m1.id
                            INNER JOIN 微信信息表 m2 ON mp.对方微信号ID = m2.id
                            WHERE CONCAT(m1.微信号, '-', m2.微信号) = c.用户标识
                            AND mp.用户ID = %s
                        )
                        LIMIT 1
                        """,
                        (用户标识, 用户id),
                    )
                    会话ID结果 = await 游标.fetchone()

            # 3. 如果没有找到会话ID，返回错误
            if not 会话ID结果:
                return 统一响应模型.失败(
                    状态码=状态.AI.会话不存在, 消息="未找到相关会话"
                ).转字典()

            会话ID = 会话ID结果["会话id"]
            # print(f"找到会话ID: {会话ID}")  # DEBUG
            # 4. 调用Coze客户端清除会话上下文
            清除结果 = await self.Coze客户端.清除会话上下文(会话ID)
            # print(f"清除结果: {清除结果}")  # DEBUG
            # 5. 检查清除结果
            if "错误" in 清除结果:
                return 统一响应模型.失败(
                    状态码=状态.AI.服务异常,
                    消息=f"清除会话上下文失败: {清除结果['错误']}",
                ).转字典()

            # 6. 返回成功结果
            return 统一响应模型.成功(
                数据={"会话ID": 会话ID}, 消息="会话上下文已清除"
            ).转字典()

        except Exception as e:
            print(f"清除会话上下文失败: {str(e)}")
            # print(f"详细错误: {traceback.format_exc()}")  # DEBUG
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.服务器错误,
                    "message": f"清除会话上下文时发生错误: {str(e)}",
                    "data": None,
                },
            )

    def _json序列化处理(self, obj):
        """处理JSON序列化时的特殊类型"""
        if hasattr(obj, "strftime"):  # 处理日期时间类型
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        return str(obj)  # 其他类型转为字符串

    def _处理Markdown格式JSON(self, 内容):
        """处理Markdown格式的JSON代码块"""
        try:
            import re

            markdown_json_pattern = r"```(?:json)?\s*\n([\s\S]*?)\n```"
            匹配 = re.search(markdown_json_pattern, 内容)
            if 匹配:
                json_文本 = 匹配.group(1)
                try:
                    json_数据 = json.loads(json_文本)
                    # 直接返回JSON对象
                    return json_数据
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    # 解析失败时返回原始内容
                    return 内容

            # 不是Markdown格式的JSON，返回原始内容
            return 内容
        except Exception as e:
            print(f"处理Markdown格式JSON时发生错误: {str(e)}")
            # 解析失败时返回原始内容
            return 内容

    def _清除知识库引用标记(self, ai内容):
        """清除AI回复中的知识库引用标记，如[1]、[2]等"""
        if not isinstance(ai内容, dict):
            return ai内容

        # 如果存在消息数组，处理每条消息内容
        if "消息数组" in ai内容 and isinstance(ai内容["消息数组"], list):
            for 消息 in ai内容["消息数组"]:
                if "内容" in 消息 and isinstance(消息["内容"], str):
                    # 使用正则表达式移除形如[1]、[2]等的引用标记
                    消息["内容"] = re.sub(r"\[\d+\]", "", 消息["内容"]).strip()

        return ai内容

    async def _处理文件消息路径(self, ai内容):
        """处理AI回复中的文件类型消息，添加本地路径"""
        if not isinstance(ai内容, dict):
            return ai内容

        # 检查是否存在消息数组
        if "消息数组" not in ai内容 or not isinstance(ai内容["消息数组"], list):
            return ai内容

        # 遍历消息数组，处理文件类型消息
        for 消息 in ai内容["消息数组"]:
            # 检查是否是文件类型消息且文件类型为手卡或货盘
            if (
                消息.get("消息类型") == "文件"
                and "文件类型" in 消息
                and (消息["文件类型"] == "手卡" or 消息["文件类型"] == "货盘")
                and "产品id" in 消息
            ):  # 只有在还没有内容字段时添加
                try:
                    产品id = 消息["产品id"]
                    文件类型 = 消息["文件类型"]

                    # 从数据库查询产品对应的文件路径
                    async with 异步连接池实例.获取连接() as 连接:
                        async with 连接.cursor() as 游标:
                            # 根据文件类型选择查询字段
                            字段名 = (
                                "手卡文件本地路径"
                                if 文件类型 == "手卡"
                                else "货盘文件本地路径"
                            )

                            # 查询产品文件路径
                            await 游标.execute(
                                f"SELECT {字段名} FROM 用户产品表 WHERE id = %s AND 状态 = 1",
                                (产品id,),
                            )
                            结果 = await 游标.fetchone()

                            # 如果查到结果，添加到消息内容中
                            if 结果 and 结果[字段名]:
                                消息["内容"] = 结果[字段名]
                                # print(f"为产品ID {产品id} 的{文件类型}添加路径: {结果[字段名]}")  # DEBUG
                            else:
                                错误日志器.warning(
                                    f"未找到产品ID {产品id} 的{文件类型}本地路径"
                                )
                                # 添加默认内容字段
                                消息["内容"] = ""

                except Exception as e:
                    错误日志器.error(
                        f"处理文件消息路径时发生错误: {str(e)}", exc_info=True
                    )
                    # 添加默认内容字段，确保前端不会因缺少内容字段而出错
                    消息["内容"] = ""

        return ai内容

    async def _获取用户默认AI配置(self, 用户id, 游标):
        """获取用户的默认AI配置"""
        try:
            await 游标.execute(
                """
                SELECT id, 智能体ID, 知识库ID, 员工名称, 员工性格, 店铺名称
                FROM 用户ai信息表
                WHERE 用户ID = %s AND AI类型 = '默认'
                LIMIT 1
                """,
                (用户id,),
            )
            配置结果 = await 游标.fetchone()

            if not 配置结果:
                return 统一响应模型.失败(
                    状态码=1001, 消息="未找到默认AI配置，请先配置AI智能体"
                ).转字典()

            return {
                "status": 100,
                "配置ID": 配置结果["id"],
                "智能体ID": 配置结果["智能体ID"],
                "知识库ID": 配置结果["知识库ID"],
                "员工名称": 配置结果["员工名称"],
                "员工性格": 配置结果["员工性格"],
                "店铺名称": 配置结果["店铺名称"],
            }

        except Exception as e:
            错误日志器.error(f"获取用户默认AI配置失败: {str(e)}")
            return 统一响应模型.失败(状态码=1002, 消息="获取AI配置失败").转字典()

    async def _检查AI智能体有效性(self, 智能体关联id, 用户id, 游标):
        """检查AI智能体是否有效 - 算力消耗模式，无时间限制"""
        # 算力模式：直接查询用户AI信息表，关联AI模型表获取算力消耗信息
        await 游标.execute(
            """
            SELECT
                uai.id,
                uai.智能体ID as 智能体id,
                uai.知识库ID as 知识库id,
                uai.员工名称,
                uai.员工性格,
                uai.店铺名称,
                uai.模型ID,
                am.算力消耗,
                am.名称 as 模型名称,
                am.提示词模板
            FROM
                用户ai信息表 uai
            LEFT JOIN AI模型表 am ON uai.模型ID = am.id
            WHERE
                uai.id = %s AND uai.用户ID = %s
            """,
            (智能体关联id, 用户id),
        )
        智能体结果 = await 游标.fetchone()

        if not 智能体结果:
            return 统一响应模型.失败(
                状态码=1003, 消息="AI智能体不存在或不属于当前用户"
            ).转字典()

        # 算力消耗模式：无时间限制，直接返回有效结果
        return 智能体结果

    # 移除冗余的算力消耗方法，现在在 _保存对话记录 中统一处理

    async def _获取或管理Coze会话ID(self, 用户标识, 游标):
        """获取或管理Coze会话ID"""
        await 游标.execute(
            """
            SELECT 会话id
            FROM Coze会话ID表
            WHERE 用户标识 = %s
            LIMIT 1
            """,
            (用户标识,),
        )
        会话ID结果 = await 游标.fetchone()

        if not 会话ID结果:
            return None

        return 会话ID结果["会话id"]

    async def _更新Coze会话ID(self, 用户标识, 新会话ID, 游标):
        """更新Coze会话ID"""
        await 游标.execute(
            """
            REPLACE INTO Coze会话ID表 (用户标识, 会话id, 创建时间, 更新时间)
            VALUES (%s, %s, NOW(), NOW())
            """,
            (用户标识, 新会话ID),
        )

    async def _调用并处理Coze接口(
        self, 智能体id, 传入信息, 用户标识, 自定义变量, conversation_id
    ):
        """调用Coze接口并处理响应"""
        try:
            # 统一格式化传入信息
            格式化传入信息 = f"【指令】你的任务是严格按照我给你的提示词规则生成 JSON 输出。请处理以下用户消息：【用户消息开始】{传入信息}【用户消息结束】请立即生成 JSON。"
            系统日志器.info(
                f"用户标识 {用户标识}: 应用统一格式化消息"
            )  # 添加日志记录

            # 调用Coze接口，传入conversation_id（如果有）
            AI回复 = await self.Coze客户端.对话_异步调用同步函数(
                智能体ID=智能体id,
                传入信息=格式化传入信息,  # 使用格式化后的信息
                用户标识=用户标识,
                自定义变量=自定义变量,
                conversation_id=conversation_id,  # 使用正确的参数名
            )
            系统日志器.debug(
                f"用户标识 {用户标识} 的AI原始回复: {AI回复}"
            )  # 调整日志级别和内容

            # 获取返回的内容和会话ID
            if isinstance(AI回复, dict):
                # 新的返回格式，包含content和conversation_id
                if "content" in AI回复:
                    AI内容 = AI回复["content"]
                    新会话ID = AI回复.get("conversation_id")
                    # print(f"AI内容: {AI内容}")  # DEBUG
                    # print(f"新会话ID: {新会话ID}")  # DEBUG
                    # 处理可能的Markdown格式JSON
                    AI内容 = self._处理Markdown格式JSON(AI内容)
                    return AI内容, 新会话ID
                else:
                    # 返回格式不符合预期
                    return str(AI回复), None
            else:
                # 旧的返回格式或其他类型
                return AI回复, None

        except asyncio.CancelledError:
            # 显式处理异步取消异常，确保资源被正确释放
            # print("Coze对话请求被取消")  # DEBUG
            错误日志器.warning("Coze对话请求被取消")
            # 返回错误信息和空会话ID
            return "处理对话时发生错误: 请求被取消", None
        except Exception as e:
            print(f"Coze对话调用异常: {str(e)}")
            错误日志器.error(f"Coze对话调用异常: {str(e)}", exc_info=True)
            # 返回错误信息和空会话ID
            return f"处理对话时发生错误: {str(e)}", None



    async def _保存对话记录(
        self, 用户id, 会话id, 用户内容, ai内容, AI模型id, 消耗算力, 游标
    ):
        """保存对话记录到数据库，包含AI模型ID和算力消耗"""
        try:
            await 游标.execute(
                """
                INSERT INTO ai对话记录表 (用户id, 会话id, 用户对话内容, AI回复内容, AI模型id, 消耗算力, 创建时间)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """,
                (用户id, 会话id, 用户内容, str(ai内容), AI模型id, 消耗算力),
            )

            # 记录日志

        except Exception as e:
            错误日志器.error(f"保存AI对话记录失败: {str(e)}")
            raise

    def _应用状态映射(self, 状态结果):
        """应用状态代码到文本的映射，返回带有映射后文本的新字典"""
        if not 状态结果:
            return {"状态说明": "无合作记录"}

        # 如果是列表形式，处理第一个元素
        if isinstance(状态结果, list):
            if not 状态结果:
                return {"状态说明": "无合作记录"}

            # 创建新列表存储处理后的结果
            映射后结果 = []

            # 处理每个状态记录
            for 状态 in 状态结果:
                映射后记录 = self._映射单个状态记录(状态)
                映射后结果.append(映射后记录)

            return 映射后结果
        else:
            # 单个字典的情况
            return self._映射单个状态记录(状态结果)

    def _映射单个状态记录(self, 状态):
        """映射单个状态记录中的代码为文本描述"""
        # 创建状态的副本，避免修改原数据
        映射后状态 = 状态.copy() if isinstance(状态, dict) else {}

        # 删除回复状态，不需要传递给AI
        if "回复状态" in 映射后状态:
            del 映射后状态["回复状态"]

        # 状态映射字典
        意向状态映射 = {-1: "无意向", 0: "未沟通", 1: "有意向"}

        样品状态映射 = {
            0: "初始",
            -1: "不需要样品",
            1: "申样",
            -2: "申样被拒",
            2: "申样通过",
            3: "已出单",
            -3: "出单异常",
            4: "到样",
            -4: "到样异常",
            5: "主播已取样",
            -5: "主播取样失败",
        }

        排期状态映射 = {0: "未排期", 1: "模糊排期", 2: "明确排期"}

        # 应用映射
        if "意向状态" in 状态:
            意向状态值 = 状态.get("意向状态")
            if 意向状态值 is not None:
                映射后状态["意向状态文本"] = 意向状态映射.get(意向状态值, "未知")

        if "样品状态" in 状态:
            样品状态值 = 状态.get("样品状态")
            if 样品状态值 is not None:
                映射后状态["样品状态文本"] = 样品状态映射.get(样品状态值, "未知")

        if "排期状态" in 状态:
            排期状态值 = 状态.get("排期状态")
            if 排期状态值 is not None:
                映射后状态["排期状态文本"] = 排期状态映射.get(排期状态值, "未知")

        # 开播状态简单转换
        if "开播状态" in 状态:
            开播状态值 = 状态.get("开播状态", 0)
            映射后状态["开播状态文本"] = "已开播" if 开播状态值 else "未开播"

        return 映射后状态
