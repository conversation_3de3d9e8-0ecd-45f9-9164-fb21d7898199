# 已使用统一日志系统替代
import random
import re
from datetime import UTC, datetime, timedelta, timezone
from typing import Any, Dict, Optional

# 第三方库模块
import jwt
from aliyunsdkcore.acs_exception.exceptions import ClientException, ServerException
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from fastapi import HTTPException, status
from passlib.context import CryptContext

# 自定义模块
import 状态
from config import (  # 移除了 会员专用权限ID
    ALIYUN_SMS_CONFIG,
    JWT_ALGORITHM,
    JWT_SECRET_KEY,
    邀约权限ID,
)
# 用户数据操作函数导入
from 数据.用户 import (
    异步创建用户,
    异步获取用户_电话,
    异步用户是否存在_id,
    异步获取用户权限_id,
    异步更新用户密码,
    异步增加用户邀约次数,
    异步用户是否存在_电话,
    异步用户是否已注册_电话,
    异步获取用户含邀约上限,
    异步获取用户今日成功邀约次数,
    异步获取用户昵称和邀约次数,
    异步更新用户昵称,
    异步获取用户密码哈希,
    异步记录邀约日志,
)

# 从用户模块导入用户相关函数
from 数据.用户 import (
    获取用户邀请人ID as 异步获取用户邀请人ID,
)

# 保留部分非用户相关函数
from 数据.异步数据库函数 import (
    异步根据ID获取微信好友信息,
    异步添加微信好友并生成识别ID,
    异步关联用户店铺,
    异步插入店铺数据,
    异步获取店铺ID,
    异步插入激活记录,
    异步查询激活码信息,
    异步查询激活码类型,
    异步查询激活记录,  # 新增导入
    异步查询用户指定会员到期时间,
    异步获取推广用户列表,
    异步设置激活码使用状态,
    异步设置用户指定权限时间,
    异步验证码系统,
    异步获取或创建微信ID,
    异步更新微信对接进度,
    异步查询微信对接进度,
)
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例  # 使用PostgreSQL连接池

# 导入统一日志系统
from 日志 import 安全日志器, 接口日志器, 系统日志器, 错误日志器


# -----------------------------
# 异常类定义
# -----------------------------
class SMSException(Exception):
    """自定义短信服务异常"""

    pass


# -----------------------------
# 认证服务
# -----------------------------
class 异步认证服务:
    """处理JWT令牌生成和验证的异步服务类"""

    def __init__(self):
        """初始化认证服务"""
        self.JWT秘钥 = JWT_SECRET_KEY
        self.算法 = JWT_ALGORITHM
        安全日志器.debug(
            f"异步认证服务初始化完成。JWT秘钥已加载: {'是' if self.JWT秘钥 else '否 (严重警告!)'}, 算法: {self.算法}"
        )
        if not self.JWT秘钥:
            错误日志器.critical("CRITICAL: JWT_SECRET_KEY 未在 config.py 中配置或为空!")

    async def 生成令牌(self, 用户数据: dict) -> str:
        """
        为用户生成JWT令牌
        :param 用户数据: 用户信息字典，通常包含id和电话
        :return: 生成的JWT令牌
        :raises HTTPException: 令牌生成失败时抛出
        """
        try:
            安全日志器.info(f"开始生成令牌，用户ID: {用户数据.get('id')}。")
            安全日志器.debug(
                f"生成令牌所用秘钥已加载: {'是' if self.JWT秘钥 else '否!'}, 算法: {self.算法}"
            )

            负载 = {
                "用户信息": 用户数据,
                "exp": int(
                    (datetime.now(UTC) + timedelta(hours=100)).timestamp()
                ),  # JWT的exp应为UTC时间戳
            }
            安全日志器.debug(f"令牌负载: {负载}")

            if not self.JWT秘钥:
                错误日志器.error("JWT秘钥为空，无法生成令牌。请检查config.py。")
                raise ValueError("JWT秘钥不能为空")

            token = jwt.encode(负载, self.JWT秘钥, algorithm=self.算法)
            安全日志器.info(f"令牌生成成功。Token (前20位): {token[:20]}...")
            安全日志器.debug(f"完整生成的Token: {token}")  # 仅在调试级别记录完整令牌
            return token
        except ValueError as ve:  # 捕获 ValueError 以便记录更具体的秘钥问题
            错误日志器.error(f"生成令牌失败 (ValueError): {str(ve)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.生成令牌失败,
                    "message": f"令牌生成配置错误: {str(ve)}",
                },
            )
        except Exception as e:
            错误日志器.error(f"生成令牌时发生未知异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "status": 状态.通用.生成令牌失败,
                    "message": f"令牌生成失败: {str(e)}",
                },
            )

    async def 验证令牌(self, token: str) -> dict:
        """
        验证JWT令牌
        :param token: JWT令牌字符串
        :return: 包含状态和解码数据的字典
        """
        安全日志器.debug(f"开始验证令牌。Token (前20位): {token[:20]}...")
        安全日志器.debug(
            f"完整接收的Token进行验证: {token}"
        )  # 仅在调试级别记录完整令牌
        安全日志器.debug(
            f"验证令牌所用秘钥已加载: {'是' if self.JWT秘钥 else '否!'}, 算法: {self.算法}"
        )
        try:
            if not self.JWT秘钥:  # 再次检查，防御性编程
                错误日志器.error("JWT秘钥为空，无法验证令牌。请检查config.py。")
                raise ValueError("JWT秘钥不能为空")

            decoded_data = jwt.decode(token, self.JWT秘钥, algorithms=[self.算法])
            安全日志器.debug("令牌验证成功。")
            安全日志器.debug(f"解码后的数据: {decoded_data}")
            return {
                "status": 状态.通用.成功_旧,  # status 0
                "data": decoded_data,
            }
        except jwt.ExpiredSignatureError as e:
            安全日志器.warning(f"令牌已过期: {str(e)}. Token: {token[:20]}...")
            return {
                "status": 状态.通用.令牌已过期,  # 502
                "message": "令牌已过期",
                "error": str(e),
            }
        except jwt.InvalidTokenError as e:
            安全日志器.warning(
                f"无效令牌 (InvalidTokenError): {str(e)}. Token: {token[:20]}..."
            )
            return {
                "status": 状态.通用.令牌无效,  # 503
                "message": f"无效令牌: {str(e)}",  # 保持原始错误信息以便调试
            }
        except ValueError as ve:  # 捕获ValueError, e.g. 空密钥
            错误日志器.error(
                f"验证令牌失败 (ValueError): {str(ve)}. Token: {token[:20]}...",
                exc_info=True,
            )
            return {
                "status": 状态.通用.服务器错误,
                "message": f"令牌验证配置错误: {str(ve)}",
            }
        except Exception as e:
            错误日志器.error(
                f"令牌解析时发生未知异常: {str(e)}. Token: {token[:20]}...",
                exc_info=True,
            )
            return {  # 修正: 确保这里是 return, 而不是 raise
                "status": 状态.通用.服务器错误,
                "message": f"令牌解析失败: {str(e)}",
            }


# -----------------------------
# 权限服务
# -----------------------------
async def 异步处理权限状态(用户id):
    """
    异步处理用户权限状态
    :param 用户id: 用户ID
    :return: 包含权限状态信息的字典
    """
    权限列表 = await 异步获取用户权限_id(用户id)
    状态结果 = {}
    now = datetime.now(UTC)  # 统一当前时间

    def format_remaining_time(delta_seconds: int) -> str:
        """格式化剩余时间为可读形式"""
        days, remaining = divmod(delta_seconds, 86400)
        hours, remaining = divmod(remaining, 3600)
        minutes, _ = divmod(remaining, 60)
        return f"{days}天{hours}小时{minutes}分钟"

    local_tz = timezone(timedelta(hours=8))
    for 权限项 in 权限列表:
        permission_id = 权限项["permission_id"]
        expiry_date_str = 权限项["expiry_date"]

        # 处理新架构中的字符串时间格式
        try:
            if isinstance(expiry_date_str, str):
                expiry_date = datetime.strptime(expiry_date_str, "%Y-%m-%d %H:%M:%S")
            elif isinstance(expiry_date_str, datetime):
                expiry_date = expiry_date_str
            else:
                错误日志器.error(
                    f"权限 {permission_id} 的过期时间格式错误: {expiry_date_str}"
                )
                continue
        except ValueError as e:
            错误日志器.error(
                f"权限 {permission_id} 的过期时间解析失败: {expiry_date_str}, 错误: {e}"
            )
            continue

        # 若无时区信息，假设是本地时间并转换为UTC
        if expiry_date.tzinfo is None:
            expiry_date = expiry_date.replace(tzinfo=local_tz)

        expiry_date = expiry_date.astimezone(UTC)

        # 计算状态
        是否过期 = "已过期" if expiry_date < now else "有效"
        剩余时间 = "0天0小时0分钟"

        if 是否过期 == "有效":
            delta = expiry_date - now
            total_seconds = int(delta.total_seconds())
            剩余时间 = format_remaining_time(total_seconds)

        状态结果[permission_id] = {
            "权限名称": 权限项["permission_name"],
            "过期状态": 是否过期,
            "剩余时间": 剩余时间,
            "到期时间": expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
        }

    return 状态结果


# -----------------------------
# 短信服务
# -----------------------------
class AliYunSmsService:
    def __init__(self, access_key_id, access_key_secret, sign_name, template_code):
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.sign_name = sign_name
        self.template_code = template_code
        self.client = AcsClient(access_key_id, access_key_secret, "cn-hangzhou")

    def send_sms(self, phone_numbers, template_param):
        request = CommonRequest()
        request.set_accept_format("json")
        request.set_domain("dysmsapi.aliyuncs.com")
        request.set_method("POST")
        request.set_protocol_type("https")
        request.set_version("2017-05-25")
        request.set_action_name("SendSms")

        request.add_query_param("RegionId", "cn-hangzhou")
        request.add_query_param("PhoneNumbers", phone_numbers)
        request.add_query_param("SignName", self.sign_name)
        request.add_query_param("TemplateCode", self.template_code)
        request.add_query_param("TemplateParam", template_param)

        try:
            response = self.client.do_action_with_exception(request)
            return response
        except (ClientException, ServerException) as e:
            错误日志器.error(f"阿里云短信发送异常: {e}")
            raise


async def 异步发送验证码服务(phone: str, 场景: str):
    """
    异步发送短信验证码
    :param phone: 手机号
    :param 场景: 验证码使用场景，如"注册"、"重置密码"等
    :return: 发送结果
    :raises HTTPException: 发送失败时抛出
    """
    try:
        if 场景 == "注册":
            if await 异步用户是否存在_电话(phone):
                raise HTTPException(
                    status_code=200,
                    detail={
                        "status": 状态.注册.手机号已存在,
                        "message": "该手机号已注册",
                    },
                )
        # 密码重置场景需要验证用户存在
        if 场景 == "重置密码":
            if not await 异步用户是否存在_电话(phone):
                raise HTTPException(
                    status_code=200,
                    detail={"status": 状态.用户.用户不存在, "message": "用户不存在"},
                )

        # 生成随机6位验证码
        验证码 = str(random.randint(100000, 999999))

        # 开发环境：在控制台显示验证码，方便测试
        接口日志器.info(f"📱 验证码生成: 手机号 {phone}, 验证码 {验证码}, 用途: {场景}")

        # 只有在配置存在时才发送真实短信
        if ALIYUN_SMS_CONFIG.get("access_key_id") and ALIYUN_SMS_CONFIG.get(
            "access_key_secret"
        ):
            try:
                sms_service = AliYunSmsService(
                    access_key_id=ALIYUN_SMS_CONFIG["access_key_id"],
                    access_key_secret=ALIYUN_SMS_CONFIG["access_key_secret"],
                    sign_name=ALIYUN_SMS_CONFIG["sign_name"],
                    template_code=ALIYUN_SMS_CONFIG["template_code"],
                )
                template_param = f'{{"code":"{验证码}"}}'

                接口日志器.info(
                    f"准备发送阿里云短信: 手机号={phone}, 模板参数={template_param}"
                )
                response = sms_service.send_sms(phone, template_param)
                接口日志器.info(f"阿里云短信发送成功: 响应={response}")

            except Exception as e:
                错误日志器.error(f"发送阿里云短信失败: {e}", exc_info=True)
                # 发送失败不应中断流程，但需要记录错误
                # 依然将验证码存入缓存，允许开发/测试环境继续
        else:
            接口日志器.warning("阿里云短信配置缺失，跳过真实发送。")

        # 存储验证码
        await 异步验证码系统.存储验证码(phone, 验证码, 场景, 300)
        return {"status": 0, "message": "验证码已发送"}

    except HTTPException as he:
        安全日志器.warning(f"发送验证码失败: {he.detail}")
        raise
    except Exception as e:
        错误日志器.error(f"发送验证码发生未知错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "发送验证码时遇到未知问题，请稍后再试。",
            },
        )


# -----------------------------
# 用户注册服务
# -----------------------------
class 异步注册服务:
    """异步用户注册服务类"""

    @staticmethod
    async def 注册用户(phone: str, password: str, verification_code: str):
        """异步注册用户"""
        try:
            # 检查手机号格式
            if not phone or len(phone) != 11 or not phone.isdigit():
                return {"status": 状态.用户.手机号格式错误, "message": "手机号格式错误"}

            # 检查用户注册状态
            用户数据 = await 异步获取用户_电话(phone)
            用户已完成注册 = await 异步用户是否已注册_电话(phone)

            if 用户数据 and 用户已完成注册:
                return {
                    "status": 状态.用户.手机号已存在,
                    "message": "该手机号已完成注册",
                }

            # 验证验证码
            验证结果 = await 异步验证码系统.验证验证码(phone, verification_code, "注册")
            if not 验证结果["valid"]:
                return {"status": 验证结果["status"], "message": 验证结果["message"]}

            if 用户数据 and 用户数据.get("状态") == "未注册":
                # 半注册用户完善注册信息
                from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

                更新SQL = "UPDATE 用户表 SET password = %s, 状态 = %s WHERE id = %s"
                await 异步连接池实例.执行更新(
                    更新SQL, (password, None, 用户数据["id"])
                )
                用户id = 用户数据["id"]
                接口日志器.info(f"半注册用户 {phone} (ID: {用户id}) 完善注册信息成功")
            else:
                # 完全新用户，创建用户记录
                用户id = await 异步创建用户(phone, password)
                接口日志器.info(f"新用户 {phone} (ID: {用户id}) 注册成功")

            # 注册成功后自动开通2天邀约权限 (权限ID 1)
            await 异步设置用户指定权限时间(
                用户id, 1, 1 * 2 * 60 * 60
            )  # 2天 * 24小时 * 60分钟 * 60秒

            # 创建认证令牌
            认证服务实例 = 异步认证服务()
            token = await 认证服务实例.生成令牌({"id": 用户id, "手机号": phone})

            return {
                "status": 状态.通用.注册成功,
                "message": "注册成功",
                "data": {"user_id": 用户id, "token": token},
            }
        except Exception as e:
            错误日志器.error(f"注册用户异常: {str(e)}")
            raise HTTPException(
                status_code=状态.用户.注册失败,
                detail={"status": 状态.用户.注册失败, "message": f"注册失败: {str(e)}"},
            )


# -----------------------------
# 邀约请求服务
# -----------------------------

# 如果不在config中定义，可以直接在这里定义
# 会员专用权限ID_CONST = 2 # 例如，定义会员权限的ID为2
# 基础邀约权限ID_CONST = 1


async def 异步用户发送邀约请求(
    用户id: int, uid_number: str, account_douyin: str, nickname: str
):
    """
    异步处理用户发送邀约请求，增加了基于用户类型的每日邀约次数限制。
    逻辑：所有用户均可邀约。拥有有效邀约权限ID(1)的用户无次数上限，其他用户受每日上限约束。
    (注意：此版本已移除KOL数据的创建/更新逻辑)
    :param 用户id: 用户ID
    :param uid_number: 达人UID
    :param account_douyin: 抖音账号
    :param nickname: 昵称
    :return: 处理结果
    :raises HTTPException: 处理失败时抛出
    """
    # 修复：使用本地时间而非UTC时间，确保与数据库查询CURRENT_DATE一致
    邀约发起时间 = datetime.now()
    邀约日志条目 = {
        "用户id": 用户id,
        "kol_uid_number": uid_number,
        "kol_account_douyin": account_douyin,
        "kol_nickname": nickname,
        "邀约发起时间": 邀约发起时间,
        "邀约状态": "",
        "状态备注": "",
        "当日计次": False,
    }

    # 简化变量命名，移除冗余变量
    每日邀约上限 = 0
    拥有无限制权限 = False
    邀约日志已记录 = False

    try:
        接口日志器.info(
            f"用户ID {用户id} 开始邀约KOL {uid_number} ({nickname})。不再执行KOL数据更新。"
        )

        if not await 异步用户是否存在_id(用户id):
            邀约日志条目["邀约状态"] = 状态.邀约.用户不存在_内部
            邀约日志条目["状态备注"] = "发起邀约的用户不存在或无效"
            接口日志器.warning(f"邀约失败：用户ID {用户id} 不存在。")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "status": 状态.用户.用户不存在,
                    "message": "发起邀约的用户不存在",
                },
            )

        # 检查用户邀约权限 - 基于会员关联表的新架构
        # 注意：这里需要根据实际的会员ID来查询，邀约权限ID现在对应会员ID
        用户特定邀约权限到期时间 = await 异步查询用户指定会员到期时间(
            用户id, 邀约权限ID
        )
        当前时间 = datetime.now()
        if 用户特定邀约权限到期时间:
            # 移除时区处理，使用本地时间比较
            if 用户特定邀约权限到期时间 > 当前时间:
                拥有无限制权限 = True
                接口日志器.info(
                    f"用户ID {用户id} 拥有有效的会员权限(会员ID:{邀约权限ID})，到期时间: {用户特定邀约权限到期时间}，将不受邀约次数限制。"
                )
            else:
                接口日志器.info(
                    f"用户ID {用户id} 的会员权限(会员ID:{邀约权限ID})已于 {用户特定邀约权限到期时间} 过期。将受每日邀约次数限制。"
                )
        else:
            接口日志器.info(
                f"用户ID {用户id} 无会员权限(会员ID:{邀约权限ID})记录。将受每日邀约次数限制。"
            )

        # 如果没有无限制权限，检查每日邀约次数限制
        if not 拥有无限制权限:
            接口日志器.info(f"用户ID {用户id} 受每日邀约次数限制，开始检查。")
            用户信息 = await 异步获取用户含邀约上限(用户id)
            if not 用户信息 or "每日邀约次数" not in 用户信息:
                邀约日志条目["邀约状态"] = 状态.邀约.失败_系统错误
                邀约日志条目["状态备注"] = (
                    f"无法获取用户ID {用户id} 的每日邀约次数配置。"
                )
                错误日志器.error(
                    f"邀约失败：无法获取用户ID {用户id} 的每日邀约次数配置。"
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "status": 状态.通用.服务器错误,
                        "message": "系统配置错误，无法获取您的邀约限制信息。",
                    },
                )
            每日邀约上限 = 用户信息.get("每日邀约次数", 0)
            if not isinstance(每日邀约上限, int) or 每日邀约上限 < 0:
                每日邀约上限 = 0
                错误日志器.warning(
                    f"用户ID {用户id} 的每日邀约次数配置值 ({用户信息.get('每日邀约次数')}) 无效，已重置为默认值 0。"
                )

            # 获取当前已用次数
            今日已用次数 = await 异步获取用户今日成功邀约次数(用户id)
            接口日志器.info(
                f"用户ID {用户id} 每日邀约上限: {每日邀约上限}, 今日已用: {今日已用次数} 次。"
            )

            if 今日已用次数 >= 每日邀约上限:
                邀约日志条目["邀约状态"] = 状态.邀约.免费次数超限
                邀约日志条目["状态备注"] = (
                    f"用户ID {用户id} 今日邀约已达上限 ({每日邀约上限}次)，已用 {今日已用次数} 次。"
                )
                接口日志器.warning(
                    f"邀约失败：用户ID {用户id} 每日邀约次数已达上限 ({每日邀约上限}次)。"
                )
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        "status": 状态.邀约.免费次数超限,
                        "message": f"您今天的邀约次数已达上限（{每日邀约上限}次）。",
                    },
                )

        # 验证输入参数
        if not uid_number:
            邀约日志条目["邀约状态"] = 状态.邀约.请求参数错误_内部
            邀约日志条目["状态备注"] = "KOL的 uid_number 不能为空。"
            接口日志器.warning(f"邀约失败：用户ID {用户id} 提交的KOL uid_number 为空。")
            raise ValueError("uid_number 不能为空")
        if not account_douyin:
            邀约日志条目["邀约状态"] = 状态.邀约.请求参数错误_内部
            邀约日志条目["状态备注"] = "KOL的 account_douyin 不能为空。"
            接口日志器.warning(
                f"邀约失败：用户ID {用户id} 提交的KOL account_douyin 为空。"
            )
            raise ValueError("account_douyin 不能为空")
        if not nickname:
            邀约日志条目["邀约状态"] = 状态.邀约.请求参数错误_内部
            邀约日志条目["状态备注"] = "KOL的 nickname 不能为空。"
            接口日志器.warning(f"邀约失败：用户ID {用户id} 提交的KOL nickname 为空。")
            raise ValueError("nickname 不能为空")
        接口日志器.debug(f"用户ID {用户id} 邀约KOL参数校验通过。")

        # 邀约成功，设置状态
        邀约日志条目["邀约状态"] = 状态.邀约.成功
        邀约日志条目["状态备注"] = "邀约请求已成功处理。KOL数据未更新。"
        邀约日志条目["当日计次"] = True

        接口日志器.info(
            f"用户ID {用户id} 成功向KOL {uid_number} 发起邀约。KOL数据未更新。"
        )

        # 记录邀约日志
        try:
            接口日志器.info(f"记录邀约日志，用户ID: {用户id}, KOL_UID: {uid_number}")
            await 异步记录邀约日志(**邀约日志条目)
            邀约日志已记录 = True
            接口日志器.info(
                f"邀约日志记录完成，用户ID: {用户id}, KOL_UID: {uid_number}。"
            )
        except Exception as log_e:
            错误日志器.critical(
                f"CRITICAL: 记录邀约日志时发生严重错误! 用户ID: {用户id}, KOL_UID: {uid_number}. 日志记录异常: {str(log_e)}",
                exc_info=True,
            )
            # 继续执行，但邀约日志可能未记录成功，finally块会尝试重新记录

        # 获取最新的邀约次数信息用于返回
        if 拥有无限制权限:
            每日邀约上限 = -1  # 表示无限制
            剩余邀约次数 = -1  # 表示无限制
            今日已用次数 = await 异步获取用户今日成功邀约次数(用户id)
            接口日志器.info(
                f"用户ID {用户id} 拥有无限制邀约权限。返回次数信息：已用: {今日已用次数}, 上限: -1, 剩余: -1"
            )
        else:
            # 重新获取最新的已用次数（包含本次邀约）
            今日已用次数 = await 异步获取用户今日成功邀约次数(用户id)
            剩余邀约次数 = 每日邀约上限 - 今日已用次数
            接口日志器.info(
                f"用户ID {用户id} 为受限用户。返回次数信息：已用: {今日已用次数}, 上限: {每日邀约上限}, 剩余: {剩余邀约次数}"
            )

        return {
            "status": 状态.通用.成功_旧,
            "message": "邀约成功",
            "data": {
                "今日已用邀约次数": 今日已用次数,
                "每日邀约上限": 每日邀约上限,
                "剩余邀约次数": 剩余邀约次数,
            },
        }

    except HTTPException as he:
        if not 邀约日志条目["邀约状态"]:
            邀约日志条目["邀约状态"] = (
                he.detail.get("status", 状态.邀约.邀约失败)
                if isinstance(he.detail, dict)
                else 状态.邀约.邀约失败
            )
            邀约日志条目["状态备注"] = (
                he.detail.get("message", str(he.detail))
                if isinstance(he.detail, dict)
                else str(he.detail)
            )
        接口日志器.warning(
            f"用户ID {用户id} 邀约KOL {uid_number} 操作因HTTPException中止: {邀约日志条目['状态备注']}"
        )
        raise he
    except ValueError as ve:
        if not 邀约日志条目["邀约状态"]:
            邀约日志条目["邀约状态"] = 状态.邀约.请求参数错误_内部
        邀约日志条目["状态备注"] = str(ve)
        错误日志器.warning(
            f"用户ID {用户id} 邀约KOL {uid_number} 因参数错误 (ValueError) 失败: {str(ve)}"
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "status": 状态.邀约.请求参数错误,
                "message": f"请求参数无效: {str(ve)}",
                "data": None,
            },
        )
    except Exception as e:
        错误日志器.error(
            f"用户ID {用户id} 邀约KOL {uid_number} 时发生未捕获的严重异常: {str(e)}",
            exc_info=True,
        )
        邀约日志条目["邀约状态"] = 状态.邀约.失败_系统错误
        邀约日志条目["状态备注"] = f"系统在处理您的邀约请求时发生意外错误: {str(e)}"
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": "系统处理您的邀约请求时发生内部错误，请稍后重试或联系技术支持。",
                "data": None,
            },
        )
    finally:
        # 如果邀约日志尚未记录且有错误状态，则记录失败日志
        if not 邀约日志已记录 and 邀约日志条目["邀约状态"]:
            try:
                接口日志器.info(
                    f"在finally块中补充记录邀约日志，用户ID: {用户id}, KOL_UID: {uid_number}, 最终状态: {邀约日志条目['邀约状态']}"
                )
                await 异步记录邀约日志(**邀约日志条目)
                接口日志器.info(
                    f"邀约日志补充记录完成，用户ID: {用户id}, KOL_UID: {uid_number}。"
                )
            except Exception as log_e:
                错误日志器.critical(
                    f"CRITICAL: finally块中记录邀约日志失败! 用户ID: {用户id}, KOL_UID: {uid_number}. 日志记录异常: {str(log_e)}",
                    exc_info=True,
                )


# -----------------------------
# 店铺服务
# -----------------------------
async def 异步收到店铺信息(用户id: int, shop_id: str, shop_name: str, avatar: str):
    """
    异步处理接收到的店铺信息
    :param 用户id: 用户ID
    :param shop_id: 店铺ID
    :param shop_name: 店铺名称
    :param avatar: 店铺头像
    :return: 处理结果
    :raises HTTPException: 处理失败时抛出
    """
    try:
        # 验证用户
        if not 用户id:
            raise HTTPException(
                status_code=状态.用户.用户状态获取失败,
                detail={
                    "status": 状态.用户.用户状态获取失败,
                    "message": "用户状态获取失败",
                },
            )

        if not await 异步用户是否存在_id(用户id):
            raise HTTPException(
                status_code=状态.用户.用户不存在,
                detail={"status": 状态.用户.用户不存在, "message": "用户不存在"},
            )

        # 验证店铺信息
        if not shop_id:
            raise HTTPException(
                status_code=200,
                detail={
                    "status": 状态.用户.店铺状态获取失败,
                    "message": "店铺状态获取失败",
                },
            )

        if not shop_name:
            raise HTTPException(
                status_code=200,
                detail={
                    "status": 状态.用户.店铺名称获取失败,
                    "message": "店铺名称获取失败",
                },
            )

        if not avatar:
            raise HTTPException(
                status_code=200,
                detail={
                    "status": 状态.用户.店铺头像获取失败,
                    "message": "店铺头像获取失败",
                },
            )

        # 获取或创建店铺
        店铺ID = await 异步获取店铺ID(shop_id)
        是新插入的店铺 = False  # 初始化标志

        if 店铺ID is None:
            店铺ID = await 异步插入店铺数据(shop_id, shop_name, avatar)
            if not 店铺ID:
                raise HTTPException(
                    status_code=状态.用户.加入店铺失败,
                    detail={
                        "status": 状态.用户.加入店铺失败,
                        "message": "加入店铺失败",
                    },
                )
            是新插入的店铺 = True  # 标记为新插入的店铺

        # 如果是新插入的店铺并且成功获取ID，则执行奖励逻辑
        if 是新插入的店铺 and 店铺ID:
            系统日志器.info(
                f"店铺 {shop_id} (ID: {店铺ID}) 为首次成功关联到系统，尝试为用户 {用户id} 及其邀请人增加邀约次数。"
            )

            # 1. 为当前用户增加邀约次数
            增加本人次数成功 = await 异步增加用户邀约次数(用户id, 5)
            if 增加本人次数成功:
                系统日志器.info(
                    f"成功为用户 {用户id} 因首次关联店铺 {shop_id} 增加5次邀约次数。"
                )
            else:
                错误日志器.warning(
                    f"为用户 {用户id} 因首次关联店铺 {shop_id} 增加邀约次数失败。"
                )

            # 2. 为邀请人增加邀约次数（如果存在）
            邀请人ID = await 异步获取用户邀请人ID(用户id)
            if 邀请人ID:
                系统日志器.info(
                    f"用户 {用户id} 的邀请人是 {邀请人ID}，尝试为其增加邀约次数。"
                )
                增加邀请人次数成功 = await 异步增加用户邀约次数(邀请人ID, 5)
                if 增加邀请人次数成功:
                    系统日志器.info(
                        f"成功为邀请人 {邀请人ID} (因用户 {用户id} 关联新店铺 {shop_id}) 增加5次邀约次数。"
                    )
                else:
                    错误日志器.warning(
                        f"为邀请人 {邀请人ID} (因用户 {用户id} 关联新店铺 {shop_id}) 增加邀约次数失败。"
                    )
            else:
                系统日志器.info(f"用户 {用户id} 没有邀请人，无需为邀请人增加邀约次数。")

        # 关联用户和店铺
        关联结果 = await 异步关联用户店铺(用户id, 店铺ID)
        if not 关联结果:
            raise HTTPException(
                status_code=状态.用户.关联店铺失败,
                detail={"status": 状态.用户.关联店铺失败, "message": "关联店铺失败"},
            )

        # 根据是否为新店铺且成功增加次数来定制成功消息
        if 是新插入的店铺 and 店铺ID:  # 假设增加次数的逻辑在此之前已完成
            # 此处需要判断增加次数是否都成功了，如果都成功，可以返回更详细的信息
            # 为简化，我们先假设只要是新店铺插入，就认为增加了。
            # 后续可以根据 增加本人次数成功 和 增加邀请人次数成功（如果邀请人存在） 来构建更精确的消息
            return {
                "status": 状态.通用.成功,  # 修改点
                "message": "关联店铺成功，您的每日邀约次数已增加！",  # 确保这个消息与上一个需求一致
            }
        else:
            return {
                "status": 状态.通用.成功,  # 修改点
                "message": "关联店铺成功",
            }

    except HTTPException as he:
        # 捕获并直接抛出 HTTPException，保留原始状态码和错误信息
        安全日志器.warning(f"异步收到店铺信息处理失败: {he.detail}")
        raise

    except ValueError as ve:
        # 捕获值错误（例如，参数无效）
        错误日志器.error(f"异步收到店铺信息值错误: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"status": 状态.邀约.请求参数错误, "message": str(ve)},
        )
    except Exception as e:
        错误日志器.error(f"异步收到店铺信息处理失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"系统处理请求时发生错误: {str(e)}",
            },
        )


# -----------------------------
# 激活账户服务
# -----------------------------
async def 异步激活账户(用户id: int, code: str):
    """
    异步处理激活账户请求
    支持两种激活方式：
    1. 传统激活码激活
    2. 手机号推荐激活：如果code是手机号格式且用户存在且为会员，给当前用户2天个人版会员试用时间

    :param 用户id: 用户ID
    :param code: 激活码或推荐人手机号
    :return: 激活结果字典，不抛出异常
    """

    def 是否为手机号格式(text: str) -> bool:
        """判断文本是否为中国大陆手机号格式"""
        # 中国大陆手机号正则：1开头，第二位是3-9，总共11位数字
        pattern = r"^1[3-9]\d{9}$"
        return bool(re.match(pattern, text))

    def 是否为激活码格式(text: str) -> bool:
        """判断文本是否为16位激活码格式（数字和字母组合）"""
        pattern = r"^[A-Za-z0-9]{16}$"
        return bool(re.match(pattern, text)) and len(text) == 16

    try:
        # 验证输入格式
        if 是否为激活码格式(code):
            # 16位激活码激活逻辑
            系统日志器.info(f"用户 {用户id} 尝试使用16位激活码进行激活")
            return await 处理激活码激活(用户id, code)

        elif 是否为手机号格式(code):
            # 11位手机号激活逻辑
            系统日志器.info(f"用户 {用户id} 尝试使用手机号 {code} 进行激活")
            return await 处理手机号激活(用户id, code)

        else:
            # 既不是有效激活码，也不是手机号格式
            return {
                "status": 状态.激活.激活码不存在,
                "message": "请输入16位激活码或11位手机号",
                "data": None,
            }

    except Exception as e:
        错误日志器.error(f"激活账户服务异常: {str(e)}", exc_info=True)
        return {
            "status": 状态.通用.服务器错误,
            "message": "激活服务遇到问题，请稍后重试",
            "data": None,
        }


async def 处理手机号激活(用户id: int, 手机号: str):
    """
    处理手机号激活逻辑
    1. 验证手机号是否存在
    2. 验证该用户是否为代理用户（代理类型表id有值）
    3. 检查激活历史和限制（检查邀请人字段是否为null）
    4. 设置当前用户为会员试用
    5. 同步邀请人信息（与推广链接注册逻辑一致）
    """
    系统日志器.info(f"开始处理手机号激活: 用户ID={用户id}, 推荐人手机号={手机号}")

    # 1. 查询推荐人是否存在
    推荐人信息 = await 异步获取用户_电话(手机号)
    if not 推荐人信息:
        错误信息 = f"推荐人手机号 {手机号} 不存在，请确认手机号是否正确"
        系统日志器.warning(f"手机号激活失败: {错误信息}")
        return {
            "status": 状态.激活.激活码不存在,
            "message": 错误信息,
            "error_type": "推荐人不存在",
            "input_phone": 手机号,
            "data": None,
        }

    推荐人id = 推荐人信息["id"]
    推荐人昵称 = 推荐人信息.get("昵称", "未设置昵称")

    # 2. 检查是否自己推荐自己
    if 推荐人id == 用户id:
        错误信息 = "不能使用自己的手机号激活账户"
        系统日志器.warning(f"手机号激活失败: 用户ID {用户id} 尝试使用自己的手机号激活")
        return {
            "status": 状态.激活.激活失败,
            "message": 错误信息,
            "error_type": "自我推荐",
            "input_phone": 手机号,
            "data": None,
        }

    # 3. 验证推荐人是否有代理类型（代理类型表id字段有值）
    推荐人代理类型状态 = await 检查用户是否有代理类型(推荐人id)
    if not 推荐人代理类型状态:
        错误信息 = f"推荐人 {推荐人昵称}（{手机号}）不是代理用户，无法通过此手机号激活。只有代理用户才能邀请其他用户激活。"
        系统日志器.warning(f"手机号激活失败: 推荐人ID {推荐人id} 不是代理用户")
        return {
            "status": 状态.激活.激活失败,
            "message": 错误信息,
            "error_type": "推荐人非代理用户",
            "recommender_info": {
                "phone": 手机号,
                "nickname": 推荐人昵称,
                "user_id": 推荐人id,
            },
            "data": None,
        }

    # 4. 检查用户激活历史，防止重复激活和滥用
    激活历史检查结果 = await 检查用户激活历史(用户id, 推荐人id)
    if not 激活历史检查结果["可以激活"]:
        错误信息 = 激活历史检查结果["原因"]
        错误类型 = "重复激活" if "已经" in 错误信息 else "激活限制"
        系统日志器.warning(f"手机号激活失败: 用户ID {用户id}, 原因: {错误信息}")
        return {
            "status": 状态.激活.激活失败,
            "message": 错误信息,
            "error_type": 错误类型,
            "recommender_info": {
                "phone": 手机号,
                "nickname": 推荐人昵称,
                "user_id": 推荐人id,
            },
            "activation_stats": 激活历史检查结果.get("邀请统计", {}),
            "data": None,
        }

    # 5. 检查当前用户是否已经是会员，决定处理方式
    当前用户会员状态 = await 检查用户是否为会员(用户id)

    # 6. 获取推荐人的代理类型信息（直接从代理类型表获取赠送天数）
    推荐人代理信息 = await 获取用户代理类型信息(推荐人id)

    if 推荐人代理信息 and 推荐人代理信息.get("代理类型表id"):
        # 推荐人有代理类型表id，使用其赠送天数和会员类型
        赠送天数 = 推荐人代理信息.get("赠送天数", 2)  # 默认2天
        赠送会员类型 = 推荐人代理信息.get("赠送会员类型", 1)  # 默认个人版会员
        系统日志器.info(
            f"使用推荐人ID {推荐人id} 的代理类型信息: 赠送天数={赠送天数}, 会员类型={赠送会员类型}"
        )
    else:
        # 推荐人没有代理类型表id，使用默认值
        系统日志器.warning(f"推荐人ID {推荐人id} 没有代理类型表id，使用默认值")
        赠送天数 = 2
        赠送会员类型 = 1

    # 7. 无论用户会员状态如何，都增加代理权限表中的赠送会员天数
    试用秒数 = 赠送天数 * 24 * 60 * 60

    权限设置结果 = await 异步设置用户指定权限时间(用户id, 赠送会员类型, 试用秒数)

    if not 权限设置结果:
        错误信息 = "设置会员权限失败，请稍后重试"
        错误日志器.error(
            f"手机号激活设置会员权限失败: 用户ID={用户id}, 推荐人ID={推荐人id}"
        )
        return {
            "status": 状态.激活.激活失败,
            "message": 错误信息,
            "error_type": "权限设置失败",
            "technical_error": "Failed to set user permissions",
            "data": None,
        }

    # 根据用户原有会员状态记录不同的日志信息
    if not 当前用户会员状态:
        系统日志器.info(f"为新用户ID {用户id} 设置了{赠送天数}天会员试用权限")
    else:
        系统日志器.info(f"为现有会员用户ID {用户id} 延长了{赠送天数}天会员时间")

    # 8. 同步更新被激活用户的邀请人字段（与推广链接注册逻辑一致）
    更新结果 = await 同步用户代理类型和邀请人(
        被激活用户id=用户id,
        推荐人id=推荐人id,
        代理类型id=None,  # 不设置代理类型表id，与推广链接注册逻辑一致
    )

    if not 更新结果["成功"]:
        # 权限已设置成功，但代理类型同步失败，记录警告但不阻止激活
        错误日志器.warning(
            f"代理类型同步失败: 用户ID={用户id}, 推荐人ID={推荐人id}, 错误: {更新结果.get('错误')}"
        )

    # 根据用户会员状态生成不同的返回消息
    if not 当前用户会员状态:
        消息 = f"激活成功！通过推荐人 {推荐人昵称} 的邀请，您获得了{赠送天数}天会员试用时间"
        激活类型 = "手机号激活"
        系统日志器.info(
            f"手机号激活成功: 用户ID={用户id}, 推荐人ID={推荐人id}, 推荐人昵称={推荐人昵称}, 获得{赠送天数}天会员试用"
        )
    else:
        消息 = f"邀请关系绑定成功！通过推荐人 {推荐人昵称} 的邀请，您的会员时间延长了{赠送天数}天"
        激活类型 = "邀请关系绑定+会员延长"
        系统日志器.info(
            f"邀请关系绑定成功: 用户ID={用户id}, 推荐人ID={推荐人id}, 推荐人昵称={推荐人昵称}, 会员时间延长{赠送天数}天"
        )

    # 新增：如果用户的代理类型表id为空，则设置为1
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 查询用户当前的代理类型表id
        用户查询 = "SELECT 代理类型表id FROM 用户表 WHERE id = %s"
        用户结果 = await 异步连接池实例.执行查询(用户查询, (用户id,))

        if 用户结果 and 用户结果[0].get("代理类型表id") is None:
            # 更新代理类型表id为1
            更新查询 = "UPDATE 用户表 SET 代理类型表id = %s WHERE id = %s"
            await 异步连接池实例.执行更新(更新查询, (1, 用户id))
            系统日志器.info(
                f"激活码激活成功后，设置用户代理类型表id为1: 用户ID={用户id}"
            )
    except Exception as e:
        错误日志器.error(f"设置用户代理类型表id失败: 用户ID={用户id}, 错误: {str(e)}")
        # 不影响激活流程，只记录错误

    return {
        "status": 状态.通用.成功,
        "message": 消息,
        "data": {
            "激活类型": 激活类型,
            "推荐人信息": {"手机号": 手机号, "昵称": 推荐人昵称, "用户id": 推荐人id},
            "会员信息": {
                "会员类型id": 赠送会员类型,
                "增加天数": 赠送天数,
                "权限设置": "已设置" if not 当前用户会员状态 else "会员时间已延长",
            },
            "邀请统计": 激活历史检查结果.get("邀请统计", {}),
        },
    }


async def 处理激活码激活(用户id: int, 激活码: str):
    """
    处理16位激活码激活逻辑（原有逻辑）
    """
    # 首先尝试作为激活码查询
    激活码信息 = await 异步查询激活码信息(激活码)

    if not 激活码信息:
        return {
            "status": 状态.激活.激活码不存在,
            "message": "无效的激活码",
            "data": None,
        }

    # 根据激活码类型进行不同的验证逻辑
    是否为一次性激活 = 激活码信息.get(
        "是否为一次性激活", 0
    )  # 默认为一次性激活码（数据库中0表示一次性）

    if 是否为一次性激活 == 0:
        # 一次性激活码：检查激活记录表中是否存在该激活码ID的记录（任何用户使用过）
        任何用户激活记录 = await 异步查询激活记录(激活码信息["id"])
        if 任何用户激活记录:
            if 任何用户激活记录["用户id"] == 用户id:
                return {
                    "status": 状态.激活.激活码已被本人使用,
                    "message": "一次性激活码已被本人使用",
                    "data": None,
                }
            else:
                return {
                    "status": 状态.激活.激活码已被他人使用,
                    "message": "一次性激活码已被他人使用",
                    "data": None,
                }
    else:
        # 永久激活码：检查激活记录表中是否存在该用户+激活码的组合记录
        用户激活记录 = await 异步查询激活记录(激活码信息["id"], 用户id)
        if 用户激活记录:
            return {
                "status": 状态.激活.激活码已被本人使用,
                "message": "您已使用过该永久激活码",
                "data": None,
            }

    激活码类型 = await 异步查询激活码类型(激活码信息["激活码类型表id"])
    if not 激活码类型:
        return {
            "status": 状态.激活.激活码类型不存在,
            "message": "激活码类型不存在",
            "data": None,
        }

    if 激活码类型["会员表id"] is None:
        return {
            "status": 状态.激活.激活类型未绑定权限,
            "message": "激活码类型未绑定会员权限",
            "data": None,
        }

    if 激活码类型["会员天数"] is None:
        return {
            "status": 状态.激活.激活类型未绑定时间,
            "message": "激活码类型未绑定会员天数",
            "data": None,
        }

    # 设置用户会员时间 - 基于会员关联表的新架构
    权限设置结果 = await 异步设置用户指定权限时间(
        用户id, 激活码类型["会员表id"], 激活码类型["会员天数"] * 24 * 60 * 60
    )

    if not 权限设置结果:
        错误日志器.error(
            f"设置用户会员时间失败: 用户ID={用户id}, 会员ID={激活码类型['会员表id']}"
        )
        return {
            "status": 状态.激活.激活失败,
            "message": "设置用户会员时间失败",
            "data": None,
        }

    # 根据激活码类型进行不同的处理
    if 是否为一次性激活 == 0:
        # 一次性激活码：设置激活码使用状态（标记为已使用）+ 插入激活记录
        await 异步设置激活码使用状态(激活码, 用户id)
        await 异步插入激活记录(激活码信息["id"], 用户id)
        系统日志器.info(
            f"一次性激活码已标记为使用并记录: 激活码={激活码}, 用户ID={用户id}"
        )
    else:
        # 永久激活码：仅插入激活记录（记录该用户使用过此激活码）
        await 异步插入激活记录(激活码信息["id"], 用户id)
        系统日志器.info(f"永久激活码使用记录已插入: 激活码={激活码}, 用户ID={用户id}")

    # 新增：如果用户的代理类型表id为空，则设置为1
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 查询用户当前的代理类型表id
        用户查询 = "SELECT 代理类型表id FROM 用户表 WHERE id = %s"
        用户结果 = await 异步连接池实例.执行查询(用户查询, (用户id,))

        if 用户结果 and 用户结果[0].get("代理类型表id") is None:
            # 更新代理类型表id为1
            更新查询 = "UPDATE 用户表 SET 代理类型表id = %s WHERE id = %s"
            更新结果 = await 异步连接池实例.执行更新(更新查询, (1, 用户id))
            系统日志器.info(
                f"激活码激活成功后，设置用户代理类型表id为1: 用户ID={用户id}, 更新结果={更新结果}"
            )
        else:
            系统日志器.info(
                f"用户代理类型表id已有值或查询失败，跳过更新: 用户ID={用户id}"
            )
    except Exception as e:
        错误日志器.error(f"设置用户代理类型表id失败: 用户ID={用户id}, 错误: {str(e)}")
        # 不影响激活流程，只记录错误

    激活码类型名称 = "一次性激活码" if 是否为一次性激活 == 0 else "永久激活码"
    系统日志器.info(
        f"激活码激活成功: 用户ID={用户id}, 激活码={激活码}, 类型={激活码类型名称}"
    )

    return {
        "status": 状态.通用.成功,
        "message": f"{激活码类型名称}激活成功",
        "data": {
            "激活类型": "激活码",
            "激活码类型": 激活码类型名称,
            "激活码": 激活码,
            "会员表id": 激活码类型["会员表id"],
            "会员天数": 激活码类型["会员天数"],
        },
    }


async def 检查用户是否为会员(用户id: int) -> bool:
    """
    检查用户是否为会员
    :param 用户id: 用户ID
    :return: True表示是会员，False表示不是会员
    """
    try:
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as cursor:
                # 查询用户是否有有效的会员关联记录
                查询SQL = """
                SELECT COUNT(*) as count
                FROM `用户_会员_关联表` 
                WHERE `用户id` = %s 
                AND STR_TO_DATE(`到期时间`, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
                """
                await cursor.execute(查询SQL, (用户id,))
                结果 = await cursor.fetchone()

                会员数量 = 结果["count"] if 结果 else 0
                return 会员数量 > 0

    except Exception as e:
        错误日志器.error(
            f"检查用户会员状态失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return False


async def 检查用户是否为付费会员(用户id: int) -> bool:
    """
    检查用户是否为充钱会员（通过代理类型表id判断）
    :param 用户id: 用户ID
    :return: True表示是充钱会员，False表示不是充钱会员
    """
    try:
        # 获取用户的代理类型表id
        代理类型信息 = await 获取用户代理类型信息(用户id)

        if not 代理类型信息:
            系统日志器.info(f"用户ID {用户id} 无法获取代理类型信息，判断为非充钱会员")
            return False

        # 判断代理类型表id是否不为null
        代理类型表id = 代理类型信息.get("代理类型表id")
        是充钱会员 = 代理类型表id is not None

        系统日志器.info(
            f"用户ID {用户id} 充钱会员验证结果: 代理类型表id={代理类型表id}, 是充钱会员={是充钱会员}"
        )

        return 是充钱会员

    except Exception as e:
        错误日志器.error(
            f"检查用户充钱会员状态失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return False


async def 检查用户是否有代理类型(用户id: int) -> bool:
    """
    检查用户是否有代理类型（代理类型表id字段有值）
    :param 用户id: 用户ID
    :return: True表示有代理类型，False表示没有代理类型
    """
    try:
        # 获取用户的代理类型表id
        代理类型信息 = await 获取用户代理类型信息(用户id)

        if not 代理类型信息:
            系统日志器.info(f"用户ID {用户id} 无法获取代理类型信息，判断为无代理类型")
            return False

        # 判断代理类型表id是否不为null
        代理类型表id = 代理类型信息.get("代理类型表id")
        有代理类型 = 代理类型表id is not None

        系统日志器.info(
            f"用户ID {用户id} 代理类型验证结果: 代理类型表id={代理类型表id}, 有代理类型={有代理类型}"
        )

        return 有代理类型

    except Exception as e:
        错误日志器.error(
            f"检查用户代理类型状态失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return False


async def 检查用户激活历史(用户id: int, 推荐人id: int) -> dict:
    """
    检查用户的手机号激活历史，防止重复激活和滥用
    注意：此函数仅用于手机号激活，激活码激活没有邀请人限制
    :param 用户id: 被激活用户ID
    :param 推荐人id: 推荐人ID
    :return: 检查结果字典
    """
    try:
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as cursor:
                # 1. 检查用户是否已经被邀请过（手机号激活专用检查）
                检查邀请历史SQL = """
                SELECT 邀请人 FROM `用户表` 
                WHERE id = %s
                """
                await cursor.execute(检查邀请历史SQL, (用户id,))
                历史记录 = await cursor.fetchone()

                if 历史记录 and 历史记录["邀请人"] is not None:
                    return {
                        "可以激活": False,
                        "原因": "您已经被邀请，无法再次邀请。每个用户只能通过一个推荐人激活。",
                        "已有邀请人": True,
                    }

                # 2. 检查推荐人今日激活邀请次数（从用户表的每日邀约次数限制）
                # 获取推荐人的每日邀约次数限制
                获取限制SQL = """
                SELECT 每日邀约次数 FROM `用户表` 
                WHERE id = %s
                """
                await cursor.execute(获取限制SQL, (推荐人id,))
                限制结果 = await cursor.fetchone()

                每日邀约限制 = (
                    限制结果["每日邀约次数"]
                    if 限制结果 and 限制结果["每日邀约次数"]
                    else 30
                )  # 默认30次

                # 统计今日邀请次数：手机号激活 + 客户邀请记录
                统计邀请次数SQL = """
                SELECT 
                    (SELECT COUNT(*) FROM `用户表` 
                     WHERE 邀请人 = $1 AND DATE(created_at) = CURRENT_DATE) as 手机号激活次数,
                    (SELECT COUNT(*) FROM `用户客户邀请记录表`
                     WHERE 邀请人ID = $2 AND DATE(创建时间) = CURRENT_DATE) as 客户邀请次数
                """
                await cursor.execute(统计邀请次数SQL, (推荐人id, 推荐人id))
                统计结果 = await cursor.fetchone()

                手机号激活次数 = 统计结果["手机号激活次数"] if 统计结果 else 0
                客户邀请次数 = 统计结果["客户邀请次数"] if 统计结果 else 0
                今日总邀请次数 = 手机号激活次数 + 客户邀请次数

                if 今日总邀请次数 >= 每日邀约限制:
                    return {
                        "可以激活": False,
                        "原因": f"推荐人今日邀请次数已达上限（{今日总邀请次数}/{每日邀约限制}次），请明天再试",
                        "已有邀请人": False,
                        "邀请统计": {
                            "手机号激活": 手机号激活次数,
                            "客户邀请": 客户邀请次数,
                            "总次数": 今日总邀请次数,
                            "限制": 每日邀约限制,
                        },
                    }

                # 3. 通过所有检查，可以激活
                return {
                    "可以激活": True,
                    "原因": "验证通过，可以进行激活",
                    "已有邀请人": False,
                    "邀请统计": {
                        "手机号激活": 手机号激活次数,
                        "客户邀请": 客户邀请次数,
                        "总次数": 今日总邀请次数,
                        "限制": 每日邀约限制,
                        "剩余次数": 每日邀约限制 - 今日总邀请次数,
                    },
                }

    except Exception as e:
        错误日志器.error(
            f"检查用户激活历史失败: 用户ID={用户id}, 推荐人ID={推荐人id}, 错误: {str(e)}",
            exc_info=True,
        )
        return {
            "可以激活": False,
            "原因": "系统检查异常，请稍后重试",
            "已有邀请人": False,
        }


# -----------------------------
# 服务层 (添加到服务/异步用户服务.py)
# -----------------------------
async def 异步更新微信对接进度服务(数据: dict) -> bool:
    """带用户验证的微信对接进度更新服务"""
    try:
        # 参数有效性验证
        必填字段 = ["我方微信号ID", "对方微信号ID", "合作产品ID"]
        for 字段 in 必填字段:
            if not 数据.get(字段):
                raise ValueError(f"{字段}为必填字段")

        # 检查状态值有效性
        状态字段列表 = [
            "好友状态",
            "回复状态",
            "意向状态",
            "样品状态",
            "排期状态",
            "开播状态",
        ]
        for 状态字段 in 状态字段列表:
            if 数据.get(状态字段) is not None and not isinstance(数据[状态字段], int):
                raise ValueError(f"{状态字段}必须为整数类型")

        # 检查时间字段格式
        时间字段列表 = ["排期开始时间", "排期结束时间"]
        for 时间字段 in 时间字段列表:
            if 数据.get(时间字段) is not None and not isinstance(
                数据[时间字段], datetime
            ):
                try:
                    # 尝试将字符串转换为datetime
                    if isinstance(数据[时间字段], str):
                        数据[时间字段] = datetime.fromisoformat(
                            数据[时间字段].replace("Z", "+00:00")
                        )
                except Exception as e:
                    raise ValueError(f"{时间字段}格式不正确: {str(e)}")

        # 执行数据库操作
        await 异步更新微信对接进度(数据)
        return True

    except Exception as e:
        错误日志器.error(f"微信对接进度更新失败: {str(e)}")
        return False


# -----------------------------
# 密码服务
# -----------------------------
密码上下文 = CryptContext(schemes=["bcrypt"], deprecated="auto")


class 异步密码服务:
    """处理用户密码管理的异步服务类"""

    @staticmethod
    async def 修改密码(用户id: int, 旧密码: str, 新密码: str) -> dict:
        """
        修改用户密码
        :param 用户id: 用户ID
        :param 旧密码: 当前密码
        :param 新密码: 新密码
        :return: 操作结果
        :raises HTTPException: 操作失败时抛出
        """
        try:
            # 验证旧密码
            当前密码 = await 异步获取用户密码哈希(用户id)
            if not 当前密码:
                raise HTTPException(
                    status_code=400, detail={"status": 400, "message": "用户不存在"}
                )

            # 直接比较明文密码（根据当前系统的实现）
            if 旧密码 != 当前密码:
                raise HTTPException(
                    status_code=400, detail={"status": 400, "message": "当前密码不正确"}
                )

            # 验证新密码强度
            if len(新密码) < 6:
                raise HTTPException(
                    status_code=400,
                    detail={"status": 400, "message": "密码长度至少需要6个字符"},
                )

            # 检查新密码是否与旧密码相同
            if 新密码 == 旧密码:
                raise HTTPException(
                    status_code=400,
                    detail={"status": 400, "message": "新密码不能与当前密码相同"},
                )

            # 更新数据库（当前系统使用明文存储）
            if await 异步更新用户密码(用户id, 新密码):
                系统日志器.info(f"用户 {用户id} 修改密码成功")
                return {"status": 100, "message": "密码修改成功！"}

            raise HTTPException(
                status_code=500,
                detail={"status": 500, "message": "密码更新失败，请稍后重试"},
            )

        except HTTPException as he:
            安全日志器.warning(f"密码修改失败: {he.detail}")
            raise

        except Exception as e:
            错误日志器.error(f"密码修改系统错误: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={"status": 500, "message": "系统处理请求时发生错误，请稍后重试"},
            )

    @staticmethod
    async def 重置密码(用户id: int, 新密码: str) -> dict:
        """
        重置用户密码（通过验证码）
        :param 用户id: 用户ID
        :param 新密码: 新密码
        :return: 操作结果
        :raises HTTPException: 操作失败时抛出
        """
        try:
            # 验证密码强度
            if len(新密码) < 6:
                raise HTTPException(
                    status_code=400,
                    detail={"status": 400, "message": "密码长度至少需要6个字符"},
                )

            # 更新数据库（当前系统使用明文存储）
            if await 异步更新用户密码(用户id, 新密码):
                安全日志器.info(f"用户 {用户id} 通过忘记密码流程重置密码")
                return {"status": 100, "message": "密码重置成功，请使用新密码登录"}

            raise HTTPException(
                status_code=500,
                detail={"status": 500, "message": "密码更新失败，请稍后重试"},
            )

        except HTTPException:
            # 重新抛出HTTP异常
            raise

        except Exception as e:
            错误日志器.error(f"密码重置系统错误: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={"status": 500, "message": "系统处理请求时发生错误，请稍后重试"},
            )


async def 异步查询微信对接进度服务(
    我方微信号: str, 对方微信号: str, 合作产品ID: int
) -> dict:
    """
    查询微信对接进度服务

    参数:
        我方微信号: 我方微信号
        对方微信号: 对方微信号
        合作产品ID: 合作产品ID

    返回:
        包含状态和查询结果的字典
    """
    try:
        # 参数验证
        if not 我方微信号 or not 对方微信号 or not 合作产品ID:
            return {
                "status": 状态.通用.参数错误,
                "message": "我方微信号、对方微信号和合作产品ID均为必填项",
            }

        # 调用数据库查询函数
        结果列表 = await 异步查询微信对接进度(我方微信号, 对方微信号)

        if not 结果列表:
            return {
                "status": 状态.通用.未找到,
                "message": "未找到对应的微信对接进度记录",
            }

        # 取第一条记录（如果有多条记录，可以根据合作产品ID进一步筛选）
        结果 = 结果列表[0]

        # 将查询结果转换为更友好的格式
        好友状态映射 = {0: "正常", -1: "已删除"}

        回复状态映射 = {-1: "加好友后-未回复", 0: "已回复", 1: "正常聊天后-未回复"}

        意向状态映射 = {-1: "无意向", 0: "未沟通", 1: "有意向"}

        样品状态映射 = {
            0: "初始",
            -1: "不需要样品",
            1: "申样",
            -2: "申样被拒",
            2: "申样通过",
            3: "已出单",
            -3: "出单异常",
            4: "到样",
            -4: "到样异常",
            5: "主播已取样",
            -5: "主播取样失败",
        }

        排期状态映射 = {1: "模糊排期", 2: "明确排期"}

        微信类别映射 = {1: "商家", 2: "达人"}

        格式化结果 = {
            "id": 结果["id"],
            "我方微信号": 结果["我方微信号"],
            "我方微信类别": 微信类别映射.get(结果["我方微信类别"], "未知"),
            "对方微信号": 结果["对方微信号"],
            "对方微信类别": 微信类别映射.get(结果["对方微信类别"], "未知"),
            "好友状态": 好友状态映射.get(结果["好友状态"], "未知"),
            "回复状态": 回复状态映射.get(结果["回复状态"], "未知"),
            "意向状态": 意向状态映射.get(结果["意向状态"], "未知"),
            "样品状态": 样品状态映射.get(结果.get("样品状态") or 0, "未知"),
            "排期状态": 排期状态映射.get(结果.get("排期状态") or 0, "未知"),
            "排期开始时间": (
                排期开始时间.strftime("%Y-%m-%d %H:%M:%S")
                if (排期开始时间 := 结果.get("排期开始时间")) is not None
                else None
            ),
            "排期结束时间": (
                排期结束时间.strftime("%Y-%m-%d %H:%M:%S")
                if (排期结束时间 := 结果.get("排期结束时间")) is not None
                else None
            ),
            "开播状态": 结果.get("开播状态", 0),
            "销售额": 结果.get("销售额", "0"),
            "合作产品ID": 结果["合作产品ID"],
            "更新时间": 结果["更新时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 结果["更新时间"]
            else None,
        }

        return {"status": 状态.通用.成功_旧, "message": "查询成功", "data": 格式化结果}

    except Exception as e:
        错误日志器.error(f"查询微信对接进度异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"查询微信对接进度失败: {str(e)}",
            },
        )


async def 异步获取或创建微信ID服务(微信号: str) -> dict:
    """
    根据微信号获取或创建微信ID的服务

    参数:
        微信号: 微信号

    返回:
        包含状态和微信ID的字典
    """
    try:
        # 参数验证
        if not 微信号:
            return {"status": 状态.通用.参数错误, "message": "微信号不能为空"}

        # 调用数据库函数获取或创建微信ID
        微信结果 = await 异步获取或创建微信ID(微信号)

        # 返回结果
        return {
            "status": 状态.通用.成功,
            "message": "获取微信ID成功",
            "data": {"id": 微信结果["id"], "是否新增": 微信结果["是否新增"]},
        }

    except Exception as e:
        错误日志器.error(f"获取或创建微信ID服务异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"获取或创建微信ID失败: {str(e)}",
            },
        )


async def 异步添加微信好友服务(
    我方微信号: str, 对方微信号: str, 对方微信头像: Optional[str] = None
) -> dict:
    """
    添加微信好友关系并生成识别ID的服务

    参数:
        我方微信号: 我方微信号
        对方微信号: 对方微信号
        对方微信头像: 对方微信头像，base64格式（可选）

    返回:
        包含状态和识别ID的字典
    """
    try:
        # 参数验证
        if not 我方微信号 or not 对方微信号:
            return {
                "status": 状态.通用.参数错误,
                "message": "我方微信号和对方微信号均为必填项",
            }

        # 调用数据库函数获取或创建微信ID
        我方微信结果 = await 异步获取或创建微信ID(我方微信号.lower())
        对方微信结果 = await 异步获取或创建微信ID(对方微信号.lower())

        # 如果提供了对方微信头像，更新微信信息表中的头像字段
        if 对方微信头像:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            更新头像SQL = """
            UPDATE 微信信息表 SET 微信头像 = $1, 修改时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """
            await 异步连接池实例.执行更新(
                更新头像SQL, (对方微信头像, 对方微信结果["id"])
            )

        # 调用数据库函数添加微信好友关系并生成识别ID
        好友结果 = await 异步添加微信好友并生成识别ID(
            我方微信结果["id"], 对方微信结果["id"]
        )

        # 返回结果
        return {
            "status": 状态.通用.成功,
            "message": "添加微信好友成功",
            "data": {
                "我方微信号ID": 我方微信结果["id"],
                "对方微信号ID": 对方微信结果["id"],
                "识别ID": 好友结果["识别id"],
                "是否新增": 好友结果["是否新增"],
            },
        }

    except Exception as e:
        错误日志器.error(f"添加微信好友服务异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "status": 状态.通用.服务器错误,
                "message": f"添加微信好友失败: {str(e)}",
            },
        )


async def 异步根据ID获取微信好友信息服务(我方微信号ID: int, 好友识别ID: int) -> dict:
    """
    通过我方微信号ID和好友识别ID获取微信好友信息

    Args:
        我方微信号ID: 我方微信号在数据库中的ID
        好友识别ID: 微信好友关系的识别ID（针对我方微信号唯一）

    Returns:
        dict: 包含查询结果的字典
        {
            "status": 状态码,
            "message": 消息,
            "data": {
                "我方微信号": 我方微信号,
                "对方微信号": 对方微信号,
                "创建时间": 创建时间
            }
        }
    """
    try:
        # 记录日志
        接口日志器.info(
            f"根据ID获取微信好友信息: 我方微信号ID={我方微信号ID}, 识别ID={好友识别ID}"
        )

        # 调用数据层查询微信好友信息
        结果 = await 异步根据ID获取微信好友信息(我方微信号ID, 好友识别ID)

        # 如果查询结果为空，返回未找到错误
        if not 结果:
            return {
                "status": 状态.通用.未找到,
                "message": f"未找到我方微信号ID为{我方微信号ID}且识别ID为{好友识别ID}的微信好友关系",
            }

        # 返回查询结果
        return {
            "status": 状态.通用.成功,
            "message": "获取微信好友信息成功",
            "data": 结果,
        }

    except Exception as e:
        # 记录错误日志
        错误日志器.error(f"根据ID获取微信好友信息失败: {str(e)}", exc_info=True)

        # 返回错误信息
        return {
            "status": 状态.通用.服务器错误,
            "message": f"获取微信好友信息时发生错误: {str(e)}",
        }


# -----------------------------
# 新服务函数
# -----------------------------
async def 异步设置用户昵称服务(用户id: int, 新昵称: str) -> dict:
    """处理用户设置或更新昵称的请求。
    如果用户首次设置昵称，则其每日邀约次数增加10次。
    """
    接口日志器.info(f"用户 {用户id} 请求设置昵称为: {新昵称}")
    try:
        # 1. 获取用户当前的昵称和邀约次数信息
        用户信息 = await 异步获取用户昵称和邀约次数(用户id)
        if not 用户信息:
            错误日志器.warning(f"设置昵称失败：未找到用户 {用户id} 的信息。")
            return {
                "status": 状态.用户.用户不存在,
                "message": "用户信息不存在，无法设置昵称。",
            }

        当前昵称 = 用户信息.get("昵称")

        # 新增：检查新昵称是否与当前昵称相同
        if 当前昵称 and 当前昵称 == 新昵称:
            接口日志器.info(f"用户 {用户id} 的新昵称与当前昵称相同，无需更新。")
            return {
                "status": 状态.用户.昵称未变更,
                "message": "新昵称与当前昵称相同，无需更新。",
                "data": {"昵称": 新昵称},
            }

        # 2. 更新用户昵称
        更新昵称成功 = await 异步更新用户昵称(用户id, 新昵称)
        if not 更新昵称成功:
            错误日志器.error(f"为用户 {用户id} 更新昵称 {新昵称} 失败。")
            # 此处可能需要更细致的错误判断，例如昵称已被占用（如果业务逻辑需要唯一性）
            # 但当前数据库层面没有此约束，所以仅作为通用更新失败处理
            return {
                "status": 状态.用户.更新用户信息失败,
                "message": "更新昵称失败，请稍后再试。",
            }
        接口日志器.info(f"用户 {用户id} 的昵称已成功更新为: {新昵称}")

        # 3. 如果是首次设置昵称，则增加邀约次数
        奖励消息 = ""
        if 当前昵称 is None:  # 假设数据库中 NULL 表示未设置过昵称
            接口日志器.info(f"用户 {用户id} 是首次设置昵称，将增加10次每日邀约次数。")
            增加次数成功 = await 异步增加用户邀约次数(用户id, 10)
            if 增加次数成功:
                奖励消息 = "首次设置昵称，已为您增加10次每日邀约次数！"
                接口日志器.info(f"成功为用户 {用户id} 增加10次每日邀约次数。")
            else:
                奖励消息 = "昵称设置成功，但增加邀约次数失败，请联系客服。"
                错误日志器.error(f"为用户 {用户id} 首次设置昵称后，增加邀约次数失败。")

        成功消息 = f"昵称已成功设置为：{新昵称}。{奖励消息}".strip()
        return {
            "status": 状态.通用.成功,  # 或者可以根据是否有奖励消息使用不同的成功状态码
            "message": 成功消息,
            "data": {"昵称": 新昵称},
        }

    except Exception as e:
        错误日志器.error(
            f"异步设置用户昵称服务处理异常: 用户ID {用户id}, 新昵称 {新昵称} - {str(e)}",
            exc_info=True,
        )
        return {
            "status": 状态.通用.服务器错误,
            "message": f"设置昵称服务处理异常: {str(e)}",
        }


async def 异步处理获取推广用户列表(
    当前用户ID: int, 页码: int, 每页数量: int, 查询条件: Optional[str] = None
) -> Dict[str, Any]:
    """
    处理获取推广用户列表的业务逻辑。

    参数:
        当前用户ID (int): 推荐人的用户ID.
        页码 (int): 当前页码.
        每页数量 (int): 每页显示的数量.
        查询条件 (Optional[str]): 用于模糊搜索用户昵称或手机号的关键词.

    返回:
        Dict[str, Any]: 包含用户列表、总记录数和分页信息的字典。
    """
    try:
        if 页码 < 1:
            页码 = 1
        if 每页数量 < 1:
            每页数量 = 10  # 默认每页数量

        数据结果 = await 异步获取推广用户列表(
            当前用户ID=当前用户ID,
            页码=页码,
            每页数量=每页数量,
            查询昵称或手机号=查询条件,
        )

        if 数据结果:
            # 使用 get 方法并提供默认值，以增加代码的健壮性
            return {
                "status": 100,
                "message": "获取用户列表成功",
                "data": {
                    "用户列表": 数据结果.get("用户列表", []),
                    "total_count": 数据结果.get("total_count", 0),
                },
            }
        else:
            # 在数据库查询失败时，返回更具体的错误信息
            return {
                "status": 500,
                "message": "数据库查询失败，请稍后重试",
                "data": {"用户列表": [], "total_count": 0},
            }
    except Exception as e:
        错误日志器.error(f"处理获取推广用户列表失败: {e}", exc_info=True)
        return {
            "status": 状态.通用.服务器错误,
            "message": f"获取推广用户列表失败: {str(e)}",
            "data": None,
        }


async def 获取用户代理类型信息(用户id: int) -> dict:
    """
    获取用户的代理类型信息
    :param 用户id: 用户ID
    :return: 代理类型信息字典
    """
    try:
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as cursor:
                # 查询用户的代理类型信息
                查询SQL = """
                SELECT u.代理类型表id, d.返佣比例, d.赠送天数, d.赠送会员类型
                FROM `用户表` u
                LEFT JOIN `代理类型表` d ON u.代理类型表id = d.id
                WHERE u.id = %s
                """
                await cursor.execute(查询SQL, (用户id,))
                结果 = await cursor.fetchone()

                if 结果:
                    return {
                        "代理类型表id": 结果["代理类型表id"],
                        "返佣比例": 结果["返佣比例"],
                        "赠送天数": 结果["赠送天数"] or 2,  # 如果没有设置，默认2天
                        "赠送会员类型": 结果["赠送会员类型"]
                        or 1,  # 如果没有设置，默认个人版会员
                    }
                else:
                    # 如果用户不存在，返回默认值
                    return {
                        "代理类型表id": None,
                        "返佣比例": None,
                        "赠送天数": 2,
                        "赠送会员类型": 1,
                    }

    except Exception as e:
        错误日志器.error(
            f"获取用户代理类型信息失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        # 发生错误时返回默认值
        return {
            "代理类型表id": None,
            "返佣比例": None,
            "赠送天数": 2,
            "赠送会员类型": 1,
        }


async def 同步用户代理类型和邀请人(
    被激活用户id: int, 推荐人id: int, 代理类型id: Optional[int] = None
) -> dict:
    """
    同步更新被激活用户的邀请人字段和代理类型表id
    :param 被激活用户id: 被激活用户的ID
    :param 推荐人id: 推荐人的ID
    :param 代理类型id: 代理类型ID（如果为None则不更新代理类型表id）
    :return: 更新结果字典
    """
    try:
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as cursor:
                if 代理类型id is not None:
                    # 更新邀请人字段和代理类型表id
                    更新SQL = """
                    UPDATE `用户表` 
                    SET `邀请人` = %s, `代理类型表id` = %s 
                    WHERE id = %s
                    """
                    参数 = (推荐人id, 代理类型id, 被激活用户id)
                    系统日志器.info(
                        f"更新用户 {被激活用户id} 的邀请人为 {推荐人id}，代理类型表id为 {代理类型id}"
                    )
                else:
                    # 只更新邀请人字段
                    更新SQL = """
                    UPDATE `用户表` 
                    SET `邀请人` = %s 
                    WHERE id = %s
                    """
                    参数 = (推荐人id, 被激活用户id)
                    系统日志器.info(f"更新用户 {被激活用户id} 的邀请人为 {推荐人id}")

                # 执行更新
                影响行数 = await cursor.execute(更新SQL, 参数)
                await 连接.commit()

                if 影响行数 > 0:
                    if 代理类型id is not None:
                        系统日志器.info(
                            f"成功同步用户邀请人和代理类型信息: 被激活用户ID={被激活用户id}, 推荐人ID={推荐人id}, 代理类型ID={代理类型id}"
                        )
                        return {
                            "成功": True,
                            "影响行数": 影响行数,
                            "邀请人id": 推荐人id,
                            "代理类型id": 代理类型id,
                        }
                    else:
                        系统日志器.info(
                            f"成功同步用户邀请人信息: 被激活用户ID={被激活用户id}, 推荐人ID={推荐人id}"
                        )
                        return {
                            "成功": True,
                            "影响行数": 影响行数,
                            "邀请人id": 推荐人id,
                        }
                else:
                    错误日志器.warning(
                        f"用户信息同步未影响任何行: 被激活用户ID={被激活用户id}"
                    )
                    return {
                        "成功": False,
                        "错误": "更新未影响任何行，可能用户不存在",
                        "影响行数": 0,
                    }

    except Exception as e:
        错误日志器.error(
            f"同步用户代理类型和邀请人失败: 被激活用户ID={被激活用户id}, 推荐人ID={推荐人id}, 错误: {str(e)}",
            exc_info=True,
        )
        return {"成功": False, "错误": str(e), "影响行数": 0}


async def 获取用户完整权限信息(
    用户id: int, 包含团队权限: bool = True, 包含历史权限: bool = False
) -> dict:
    """
    获取用户完整权限信息
    :param 用户id: 用户ID
    :param 包含团队权限: 是否包含团队权限信息
    :param 包含历史权限: 是否包含历史权限信息
    :return: 用户完整权限信息字典
    """
    try:
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as cursor:
                # 1. 获取用户基本信息
                用户基本信息 = await _获取用户基本信息(cursor, 用户id)
                if not 用户基本信息:
                    return {"成功": False, "错误": "用户不存在", "数据": None}

                # 2. 获取会员信息
                会员信息 = await _获取用户会员信息(cursor, 用户id)

                # 3. 获取代理类型信息
                代理类型信息 = await _获取用户代理类型信息(cursor, 用户id)

                # 4. 获取个人权限列表
                个人权限列表 = await _获取用户个人权限列表(cursor, 用户id, 包含历史权限)

                # 5. 获取团队权限列表
                团队权限列表 = []
                if 包含团队权限:
                    团队权限列表 = await _获取用户团队权限列表(
                        cursor, 用户id, 包含历史权限
                    )

                # 6. 获取权限配置信息
                权限配置 = await _获取用户权限配置(cursor, 用户id)

                # 构建完整权限信息
                完整权限信息 = {
                    "用户基本信息": 用户基本信息,
                    "会员信息": 会员信息,
                    "代理类型信息": 代理类型信息,
                    "个人权限列表": 个人权限列表,
                    "团队权限列表": 团队权限列表,
                    "权限配置": 权限配置,
                }

                系统日志器.info(f"成功获取用户完整权限信息: 用户ID={用户id}")
                return {"成功": True, "数据": 完整权限信息, "错误": None}

    except Exception as e:
        错误日志器.error(
            f"获取用户完整权限信息失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return {"成功": False, "错误": str(e), "数据": None}


async def _获取用户基本信息(cursor, 用户id: int) -> Optional[dict]:
    """
    获取用户基本信息
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :return: 用户基本信息字典
    """
    try:
        # 查询用户基本信息
        sql = """
        SELECT 
            id,
            昵称,
            手机号,
            邮箱,
            `level`,
            experience_points,
            算力值,
            每日邀约次数,
            每日快递查询次数,
            可创建团队数,
            is_admin,
            状态,
            created_at
        FROM `用户表`
        WHERE id = %s
        """
        await cursor.execute(sql, (用户id,))
        结果 = await cursor.fetchone()

        if not 结果:
            return None

        return {
            "用户ID": 结果["id"],
            "昵称": 结果["昵称"],
            "手机号": 结果["手机号"],
            "邮箱": 结果["邮箱"],
            "等级": 结果["level"],
            "经验值": 结果["experience_points"],
            "算力值": 结果["算力值"],
            "每日邀约次数": 结果["每日邀约次数"],
            "每日快递查询次数": 结果["每日快递查询次数"],
            "可创建团队数": 结果["可创建团队数"],
            "是否管理员": bool(结果["is_admin"]),
            "状态": 结果["状态"],
            "创建时间": 结果["created_at"],
        }

    except Exception as e:
        错误日志器.error(
            f"获取用户基本信息失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return None


async def _获取用户会员信息(cursor, 用户id: int) -> Optional[dict]:
    """
    获取用户会员信息
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :return: 用户会员信息字典
    """
    try:
        # 查询用户会员信息
        sql = """
        SELECT 
            m.id as 会员ID,
            m.名称 as 会员名称,
            m.每月费用,
            m.每年费用,
            m.每月算力点,
            m.可创建团队数,
            m.创建团队默认人数上限,
            m.可加入团队数,
            um.开通时间,
            um.到期时间
        FROM `用户_会员_关联表` um
        JOIN `会员表` m ON um.会员id = m.id
        WHERE um.用户id = %s
        ORDER BY um.开通时间 DESC
        LIMIT 1
        """
        await cursor.execute(sql, (用户id,))
        结果 = await cursor.fetchone()

        if not 结果:
            return None

        # 判断会员是否有效
        当前时间 = datetime.now()
        是否有效 = 结果["到期时间"] is None or 结果["到期时间"] > 当前时间

        return {
            "会员ID": 结果["会员ID"],
            "会员名称": 结果["会员名称"],
            "每月费用": 结果["每月费用"],
            "每年费用": 结果["每年费用"],
            "每月算力点": 结果["每月算力点"],
            "可创建团队数": 结果["可创建团队数"],
            "创建团队默认人数上限": 结果["创建团队默认人数上限"],
            "可加入团队数": 结果["可加入团队数"],
            "开通时间": 结果["开通时间"],
            "到期时间": 结果["到期时间"],
            "是否有效": 是否有效,
        }

    except Exception as e:
        错误日志器.error(
            f"获取用户会员信息失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return None


async def _获取用户代理类型信息(cursor, 用户id: int) -> Optional[dict]:
    """
    获取用户代理类型信息
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :return: 用户代理类型信息字典
    """
    try:
        # 查询用户代理类型信息（使用LEFT JOIN以支持代理类型表id为null的情况）
        sql = """
        SELECT 
            u.代理类型表id,
            dt.id as 代理类型ID,
            dt.返佣比例,
            dt.赠送天数,
            dt.赠送会员类型,
            dt.代理等级
        FROM `用户表` u
        LEFT JOIN `代理类型表` dt ON u.代理类型表id = dt.id
        WHERE u.id = %s
        """
        await cursor.execute(sql, (用户id,))
        结果 = await cursor.fetchone()

        if not 结果:
            return None

        # 如果代理类型表id为null，返回默认值
        if 结果["代理类型表id"] is None:
            return {
                "代理类型表id": None,
                "代理类型ID": None,
                "返佣比例": None,
                "赠送天数": 2,  # 默认值
                "赠送会员类型": 1,  # 默认值
                "代理等级": 0,  # 默认值
            }

        return {
            "代理类型表id": 结果["代理类型表id"],
            "代理类型ID": 结果["代理类型ID"],
            "返佣比例": 结果["返佣比例"],
            "赠送天数": 结果["赠送天数"],
            "赠送会员类型": 结果["赠送会员类型"],
            "代理等级": 结果["代理等级"],
        }

    except Exception as e:
        错误日志器.error(
            f"获取用户代理类型信息失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return None


async def _获取用户个人权限列表(
    cursor, 用户id: int, 包含历史权限: bool = False
) -> list:
    """
    获取用户个人权限列表
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :param 包含历史权限: 是否包含历史权限
    :return: 用户个人权限列表
    """
    try:
        # 构建查询条件
        时间条件 = ""
        if not 包含历史权限:
            时间条件 = "AND (up.到期时间 IS NULL OR up.到期时间 > NOW())"

        # 查询用户个人权限
        sql = f"""
        SELECT 
            p.id as 权限ID,
            p.名称 as 权限名称,
            p.描述 as 权限描述,
            up.开通时间,
            up.到期时间
        FROM `用户权限表` up
        JOIN `权限表` p ON up.permission_id = p.id
        WHERE up.user_id = %s {时间条件}
        ORDER BY up.开通时间 DESC
        """
        await cursor.execute(sql, (用户id,))
        结果列表 = await cursor.fetchall()

        权限列表 = []
        当前时间 = datetime.now()

        for 权限 in 结果列表:
            是否有效 = 权限["到期时间"] is None or 权限["到期时间"] > 当前时间

            权限列表.append(
                {
                    "权限ID": 权限["权限ID"],
                    "权限名称": 权限["权限名称"],
                    "权限描述": 权限["权限描述"],
                    "开通时间": 权限["开通时间"],
                    "到期时间": 权限["到期时间"],
                    "是否有效": 是否有效,
                }
            )

        return 权限列表

    except Exception as e:
        错误日志器.error(
            f"获取用户个人权限列表失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return []


async def _获取用户团队权限列表(
    cursor, 用户id: int, 包含历史权限: bool = False
) -> list:
    """
    获取用户团队权限列表
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :param 包含历史权限: 是否包含历史权限
    :return: 用户团队权限列表
    """
    try:
        # 构建查询条件
        时间条件 = ""
        if not 包含历史权限:
            时间条件 = "AND (tp.过期时间 IS NULL OR tp.过期时间 > NOW())"

        # 查询用户团队权限
        sql = f"""
        SELECT 
            t.id as 团队ID,
            t.团队名称,
            t.团队代码,
            ut.职位,
            ut.状态,
            ut.加入时间,
            tp.权限ID,
            p.名称 as 权限名称,
            p.描述 as 权限描述,
            tp.授权时间,
            tp.过期时间
        FROM `用户团队关联表` ut
        JOIN `团队表` t ON ut.团队ID = t.id
        LEFT JOIN `用户团队权限表` tp ON ut.用户ID = tp.用户ID AND ut.团队ID = tp.团队ID
        LEFT JOIN `权限表` p ON tp.权限ID = p.id
        WHERE ut.用户ID = %s 
        AND ut.状态 IN ('正常', '暂停')
        {时间条件}
        ORDER BY ut.加入时间 DESC, tp.授权时间 DESC
        """
        await cursor.execute(sql, (用户id,))
        结果列表 = await cursor.fetchall()

        # 按团队分组权限
        团队权限字典 = {}
        当前时间 = datetime.now()

        for 记录 in 结果列表:
            团队ID = 记录["团队ID"]

            if 团队ID not in 团队权限字典:
                团队权限字典[团队ID] = {
                    "团队ID": 团队ID,
                    "团队名称": 记录["团队名称"],
                    "团队代码": 记录["团队代码"],
                    "职位": 记录["职位"],
                    "状态": 记录["状态"],
                    "加入时间": 记录["加入时间"],
                    "团队权限列表": [],
                }

            # 添加权限信息（如果存在）
            if 记录["权限ID"]:
                是否有效 = 记录["过期时间"] is None or 记录["过期时间"] > 当前时间

                团队权限字典[团队ID]["团队权限列表"].append(
                    {
                        "权限ID": 记录["权限ID"],
                        "权限名称": 记录["权限名称"],
                        "权限描述": 记录["权限描述"],
                        "开通时间": 记录["授权时间"],
                        "到期时间": 记录["过期时间"],
                        "是否有效": 是否有效,
                    }
                )

        return list(团队权限字典.values())

    except Exception as e:
        错误日志器.error(
            f"获取用户团队权限列表失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return []


async def _获取用户权限配置(cursor, 用户id: int) -> dict:
    """
    获取用户权限配置信息
    :param cursor: 数据库游标
    :param 用户id: 用户ID
    :return: 用户权限配置字典
    """
    try:
        # 获取用户的各种权限配置
        权限配置 = {
            "邀约权限": {"每日邀约次数": 0, "今日已邀约次数": 0, "剩余邀约次数": 0},
            "快递查询权限": {
                "每日快递查询次数": 0,
                "今日已查询次数": 0,
                "剩余查询次数": 0,
            },
            "团队权限": {"可创建团队数": 0, "已创建团队数": 0, "剩余创建团队数": 0},
            "算力权限": {"当前算力值": 0, "会员每月算力点": 0},
        }

        # 1. 获取用户基本权限配置
        sql = """
        SELECT 
            每日邀约次数,
            每日快递查询次数,
            可创建团队数,
            算力值
        FROM `用户表`
        WHERE id = %s
        """
        await cursor.execute(sql, (用户id,))
        用户基本信息 = await cursor.fetchone()

        if 用户基本信息:
            权限配置["邀约权限"]["每日邀约次数"] = 用户基本信息["每日邀约次数"] or 0
            权限配置["快递查询权限"]["每日快递查询次数"] = (
                用户基本信息["每日快递查询次数"] or 0
            )
            权限配置["团队权限"]["可创建团队数"] = 用户基本信息["可创建团队数"] or 0
            权限配置["算力权限"]["当前算力值"] = 用户基本信息["算力值"] or 0

        # 2. 获取今日邀约次数
        sql = """
        SELECT COUNT(*) as 今日邀约次数
        FROM `用户抖音达人邀约记录表`
        WHERE 用户ID = $1 AND DATE(邀约发起时间) = CURRENT_DATE
        """
        await cursor.execute(sql, (用户id,))
        今日邀约结果 = await cursor.fetchone()
        if 今日邀约结果:
            权限配置["邀约权限"]["今日已邀约次数"] = 今日邀约结果["今日邀约次数"]
            权限配置["邀约权限"]["剩余邀约次数"] = max(
                0,
                权限配置["邀约权限"]["每日邀约次数"]
                - 权限配置["邀约权限"]["今日已邀约次数"],
            )

        # 3. 获取今日快递查询次数
        sql = """
        SELECT COUNT(*) as 今日查询次数
        FROM `用户_快递查询记录表`
        WHERE 用户表id = $1 AND DATE(查询时间) = CURRENT_DATE
        """
        await cursor.execute(sql, (用户id,))
        今日查询结果 = await cursor.fetchone()
        if 今日查询结果:
            权限配置["快递查询权限"]["今日已查询次数"] = 今日查询结果["今日查询次数"]
            权限配置["快递查询权限"]["剩余查询次数"] = max(
                0,
                权限配置["快递查询权限"]["每日快递查询次数"]
                - 权限配置["快递查询权限"]["今日已查询次数"],
            )

        # 4. 获取已创建团队数
        sql = """
        SELECT COUNT(*) as 已创建团队数
        FROM `团队表`
        WHERE 创建人ID = %s AND 团队状态 = '正常'
        """
        await cursor.execute(sql, (用户id,))
        创建团队结果 = await cursor.fetchone()
        if 创建团队结果:
            权限配置["团队权限"]["已创建团队数"] = 创建团队结果["已创建团队数"]
            权限配置["团队权限"]["剩余创建团队数"] = max(
                0,
                权限配置["团队权限"]["可创建团队数"]
                - 权限配置["团队权限"]["已创建团队数"],
            )

        # 5. 获取会员算力点
        sql = """
        SELECT m.每月算力点
        FROM `用户_会员_关联表` um
        JOIN `会员表` m ON um.会员id = m.id
        WHERE um.用户id = %s
        AND (um.到期时间 IS NULL OR um.到期时间 > NOW())
        ORDER BY um.开通时间 DESC
        LIMIT 1
        """
        await cursor.execute(sql, (用户id,))
        会员算力结果 = await cursor.fetchone()
        if 会员算力结果:
            权限配置["算力权限"]["会员每月算力点"] = 会员算力结果["每月算力点"] or 0

        return 权限配置

    except Exception as e:
        错误日志器.error(
            f"获取用户权限配置失败: 用户ID={用户id}, 错误: {str(e)}", exc_info=True
        )
        return {}
