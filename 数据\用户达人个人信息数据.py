import json
from datetime import datetime
from typing import Dict, Optional, List, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
# 导入统一日志系统
from 日志 import 错误日志器, 应用日志器 as 数据库日志器

async def 异步获取用户达人个人信息(关联表ID: int) -> Optional[Dict[str, Any]]:
    """
    获取用户达人的补充信息

    参数:
        关联表ID: 用户达人关联表的ID

    返回:
        达人补充信息字典，如果不存在则返回None
    """
    try:
        查询SQL = """
        SELECT
            id, 用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 创建时间, 更新时间
        FROM invite.用户达人补充信息表
        WHERE 用户达人关联表id = %s
        LIMIT 1
        """

        数据库日志器.debug(f"获取用户达人补充信息: 关联表ID={关联表ID}")
        结果 = await 异步连接池实例.执行查询(查询SQL, (关联表ID,))
        
        if 结果:
            个人信息 = 结果[0]
            # 处理JSON字段
            if 个人信息.get('个人标签'):
                try:
                    个人信息['个人标签'] = json.loads(个人信息['个人标签']) if isinstance(个人信息['个人标签'], str) else 个人信息['个人标签']
                except (json.JSONDecodeError, TypeError):
                    个人信息['个人标签'] = []
            else:
                个人信息['个人标签'] = []

            数据库日志器.debug(f"用户达人补充信息查询成功: 关联表ID={关联表ID}")
            return 个人信息
        else:
            数据库日志器.debug(f"用户达人补充信息不存在: 关联表ID={关联表ID}")
            return None

    except Exception as e:
        错误日志器.error(f"获取用户达人补充信息失败: 关联表ID={关联表ID}, 错误={str(e)}")
        raise

async def 异步创建或更新用户达人个人信息(
    关联表ID: int,
    微信号: Optional[str] = None,
    联系方式类型: Optional[str] = None,
    个人备注: Optional[str] = None,
    个人标签: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    智能创建或更新用户达人补充信息

    性能优化：
    - 使用事务处理，确保数据一致性
    - 合并查询，减少数据库往返次数
    - 优化JSON处理，减少序列化开销
    - 增加操作超时控制

    重要特性：
    - 只更新传入的非空字段，不影响其他已有数据
    - 如果传入None或空字符串，保持原有数据不变
    - 支持部分字段更新，避免数据丢失
    - 自动处理联系方式表ID的创建和关联
    - 注意：联系方式和联系方式类型一旦创建就不能修改，只能删除后重新添加

    参数:
        关联表ID: 用户达人关联表的ID
        微信号: 联系方式内容（仅在创建时使用，更新时会被忽略）
        联系方式类型: 联系方式类型（仅在创建时使用，更新时会被忽略，只支持"微信"、"手机"、"邮箱"）
        个人备注: 个人备注（None时不更新，空字符串时清空）
        个人标签: 个人标签列表（None时不更新，空数组时清空）

    返回:
        操作结果字典，包含状态和消息
    """
    开始时间 = datetime.now()
    
    try:
        # 使用事务处理，确保操作的原子性
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                # 设置事务隔离级别，优化并发性能
                await 游标.execute("SET TRANSACTION ISOLATION LEVEL READ COMMITTED")
                await 连接.begin()
                
                try:
                    # 第一步：验证关联表ID是否存在（优化查询，只检查必要字段）
                    验证SQL = """
                    SELECT 1 FROM invite.用户达人关联表
                    WHERE id = %s AND 状态 = 1
                    LIMIT 1
                    """

                    await 游标.execute(验证SQL, [关联表ID])
                    验证结果 = await 游标.fetchone()

                    if not 验证结果:
                        await 连接.rollback()
                        return {
                            "状态": "失败",
                            "消息": "关联记录不存在或已失效，无法编辑补充信息"
                        }
                    
                    # 第二步：检查记录是否已存在，决定是创建还是更新
                    检查记录SQL = """
                    SELECT id, 联系方式, 联系方式类型 FROM invite.用户达人补充信息表
                    WHERE 用户达人关联表id = %s
                    LIMIT 1
                    """
                    await 游标.execute(检查记录SQL, [关联表ID])
                    现有记录 = await 游标.fetchone()

                    联系方式表ID = None
                    是否创建新记录 = 现有记录 is None

                    if 是否创建新记录:
                        # 创建新记录：需要联系方式和类型
                        if 微信号 is not None and 联系方式类型 is not None:
                            # 验证联系方式类型
                            允许的类型 = ["微信", "手机", "邮箱"]
                            if 联系方式类型 not in 允许的类型:
                                await 连接.rollback()
                                return {
                                    "状态": "失败",
                                    "消息": f"不支持的联系方式类型，只支持: {', '.join(允许的类型)}"
                                }

                            # 获取或创建联系方式表记录
                            from 数据.线索数据操作 import 获取或创建联系方式并返回ID

                            联系方式表ID = await 获取或创建联系方式并返回ID(
                                联系方式=微信号,
                                联系方式类型=联系方式类型,
                                来源="用户创建"
                            )

                            if not 联系方式表ID:
                                await 连接.rollback()
                                return {
                                    "状态": "失败",
                                    "消息": "创建联系方式记录失败"
                                }
                        else:
                            # 创建记录但没有提供联系方式，设置为空
                            微信号 = None
                            联系方式类型 = None
                            联系方式表ID = None
                    else:
                        # 更新现有记录：忽略联系方式相关参数
                        if 微信号 is not None or 联系方式类型 is not None:
                            await 连接.rollback()
                            return {
                                "状态": "失败",
                                "消息": "联系方式和联系方式类型一旦创建就不能修改，如需更改请删除后重新添加"
                            }
                        # 保持现有的联系方式信息
                        微信号 = 现有记录["联系方式"]
                        联系方式类型 = 现有记录["联系方式类型"]
                        # 联系方式表ID保持不变，在UPDATE中不会被修改

                    # 预处理个人标签JSON
                    个人标签JSON = None
                    if 个人标签 is not None:
                        if isinstance(个人标签, list):
                            个人标签JSON = json.dumps(个人标签, ensure_ascii=False) if 个人标签 else json.dumps([])
                        else:
                            个人标签JSON = json.dumps([])

                    if 是否创建新记录:
                        # 创建新记录
                        插入SQL = """
                        INSERT INTO invite.用户达人补充信息表
                        (用户达人关联表id, 联系方式, 联系方式类型, 联系方式表id, 个人备注, 个人标签, 创建时间, 更新时间)
                        VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                        """
                        参数 = [关联表ID, 微信号, 联系方式类型, 联系方式表ID, 个人备注, 个人标签JSON]
                        await 游标.execute(插入SQL, 参数)
                    else:
                        # 更新现有记录（只更新允许修改的字段）
                        更新字段 = []
                        更新参数 = []

                        if 个人备注 is not None:
                            更新字段.append("个人备注 = %s")
                            更新参数.append(个人备注)

                        if 个人标签JSON is not None:
                            更新字段.append("个人标签 = %s")
                            更新参数.append(个人标签JSON)

                        if 更新字段:
                            更新字段.append("更新时间 = NOW()")
                            更新参数.append(关联表ID)

                            更新SQL = f"""
                            UPDATE invite.用户达人补充信息表
                            SET {", ".join(更新字段)}
                            WHERE 用户达人关联表id = %s
                            """
                            await 游标.execute(更新SQL, 更新参数)

                    数据库日志器.debug(f"执行操作: 关联表ID={关联表ID}, 创建新记录={是否创建新记录}")

                    # 提交事务
                    await 连接.commit()
                    
                    耗时 = (datetime.now() - 开始时间).total_seconds()
                    数据库日志器.info(f"成功创建或更新用户达人补充信息: 关联表ID={关联表ID}, 耗时={耗时:.3f}秒")

                    return {
                        "状态": "成功",
                        "消息": "编辑达人补充信息成功",
                        "关联表ID": 关联表ID
                    }

                except Exception as transaction_error:
                    # 事务回滚
                    await 连接.rollback()
                    错误日志器.error(f"事务执行失败，已回滚: 关联表ID={关联表ID}, 错误={str(transaction_error)}")
                    raise transaction_error
        
    except Exception as e:
        耗时 = (datetime.now() - 开始时间).total_seconds()
        错误日志器.error(f"创建或更新用户达人补充信息失败: 关联表ID={关联表ID}, 耗时={耗时:.3f}秒, 错误={str(e)}")

        # 根据错误类型返回更具体的错误信息
        错误信息 = str(e).lower()
        if 'timeout' in 错误信息 or 'connection' in 错误信息:
            return {
                "状态": "失败",
                "消息": "网络连接超时，请稍后重试"
            }
        elif 'lock' in 错误信息 or 'deadlock' in 错误信息:
            return {
                "状态": "失败", 
                "消息": "系统繁忙，请稍后重试"
            }
        else:
            return {
                "状态": "失败",
                "消息": f"编辑达人个人信息失败: {str(e)}"
            }
