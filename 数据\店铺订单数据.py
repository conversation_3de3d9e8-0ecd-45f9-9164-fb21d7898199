"""
店铺订单数据层
基于PostgreSQL实现的店铺订单相关数据库操作

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的订单查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的批量操作和事务处理
5. 完整的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class 店铺订单数据层:
    """店铺订单数据访问层"""

    def __init__(self):
        self.数据库连接池 = 异步连接池实例

        # 安全字段白名单 - 防止SQL注入
        self.允许的字段名 = {
            # 基础订单字段
            "订单id", "商品id", "商品名称", "商品规格", "商品数量", "商品单价", "商品总价",
            "订单状态", "下单时间", "支付时间", "发货时间", "收货时间", "退款时间",
            "买家昵称", "买家手机", "收货地址", "物流公司", "物流单号", "备注",
            "店铺id", "店铺名称", "商家备注", "优惠金额", "实付金额", "运费",
            "支付方式", "交易流水号", "退款状态", "退款金额", "退款原因",
            "评价状态", "评价内容", "评价星级", "售后状态", "创建时间", "更新时间",
            # 扩展字段
            "用户订单认领表id", "抖音号", "达人表id", "佣金比例", "佣金金额",
            "结算状态", "结算时间", "推广渠道", "推广链接", "推广素材",
            "客户标签", "订单来源", "设备信息", "IP地址", "用户代理",
            "促销活动id", "优惠券id", "积分抵扣", "会员折扣", "分销商id",
            # 抖音相关字段
            "作者账号", "抖音火山号", "支付金额", "预估佣金支出", "结算金额",
            "实际佣金支出", "定金金额", "预估奖励佣金支出", "结算奖励佣金支出",
            "支付补贴", "平台补贴", "达人补贴", "税费", "运费补贴",
            "推广技术服务费", "预估推广费支出", "结算推广费支出", "佣金率",
            "超时未结算原因", "商品来源", "佣金发票", "是否阶梯佣金",
            "分销来源", "计划类型", "流量细分来源", "流量来源", "订单类型",
            "付款时间", "结算时间"
        }

        # 允许的排序字段
        self.允许的排序字段 = {
            "订单id", "下单时间", "支付时间", "商品总价", "实付金额", "创建时间", "更新时间",
            "付款时间", "结算时间", "支付金额", "预估佣金支出", "实际佣金支出"
        }

        # 允许的订单状态
        self.允许的订单状态 = {
            "待付款", "待发货", "待收货", "已完成", "已取消", "退款中", "已退款", "售后中",
            "已付款", "已结算", "已确认收货", "超时未结算"
        }

    def _验证字段名安全性(self, 字段名: str) -> bool:
        """验证字段名是否在安全白名单中"""
        return 字段名 in self.允许的字段名

    def _验证排序字段安全性(self, 字段名: str) -> bool:
        """验证排序字段是否在安全白名单中"""
        return 字段名 in self.允许的排序字段

    async def 检查订单是否存在(self, 订单id: str) -> bool:
        """
        检查指定订单ID是否存在

        参数:
            订单id: 订单ID

        返回:
            bool: 订单是否存在
        """
        try:
            检查查询 = "SELECT COUNT(*) as count FROM 店铺订单表 WHERE 订单id = $1"
            检查结果 = await self.数据库连接池.执行查询(检查查询, (订单id,))

            return 检查结果 and 检查结果[0]["count"] > 0

        except Exception as e:
            错误日志器.error(f"检查订单是否存在失败: {e}", exc_info=True)
            return False

    async def 插入订单数据(self, 订单数据: Dict[str, Any]) -> bool:
        """
        插入单条订单数据

        参数:
            订单数据: 包含订单信息的字典

        返回:
            bool: 插入是否成功
        """
        try:
            # 验证字段安全性
            字段列表 = list(订单数据.keys())
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    数据库日志器.error(f"插入订单数据失败: 不安全的字段名 {字段名}")
                    return False

            # 构建安全的插入SQL
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])
            占位符 = ", ".join([f"${i+1}" for i in range(len(字段列表))])

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            数值列表 = [订单数据[字段] for 字段 in 字段列表]
            await self.数据库连接池.执行更新(插入查询, 数值列表)

            数据库日志器.info(f"成功插入订单数据: 订单ID={订单数据.get('订单id', 'Unknown')}")
            return True

        except Exception as e:
            错误日志器.error(f"插入订单数据失败: {e}", exc_info=True)
            return False

    async def 获取用户店铺权限(self, 用户ID: int) -> List[int]:
        """
        获取用户有权限访问的店铺ID列表

        参数:
            用户ID: 用户ID

        返回:
            List[int]: 店铺ID列表
        """
        try:
            权限查询 = """
            SELECT 店铺ID FROM 用户_店铺 
            WHERE 用户ID = $1
            """

            权限结果 = await self.数据库连接池.执行查询(权限查询, (用户ID,))

            return [row["店铺ID"] for row in 权限结果] if 权限结果 else []

        except Exception as e:
            错误日志器.error(f"获取用户店铺权限失败: {e}", exc_info=True)
            return []

    async def 查询订单列表(
        self,
        用户ID: int,
        页码: int = 1,
        每页数量: int = 20,
        店铺ID: Optional[str] = None,
        订单状态: Optional[str] = None,
        商品名称: Optional[str] = None,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
        排序字段: str = "创建时间",
        排序方向: str = "DESC"
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询订单列表（支持分页和筛选）

        参数:
            用户ID: 用户ID
            页码: 页码（从1开始）
            每页数量: 每页显示的订单数量
            店铺ID: 可选的店铺ID筛选
            订单状态: 可选的订单状态筛选
            商品名称: 可选的商品名称模糊搜索
            开始时间: 可选的开始时间筛选
            结束时间: 可选的结束时间筛选
            排序字段: 排序字段
            排序方向: 排序方向（ASC/DESC）

        返回:
            Tuple[List[Dict], int]: (订单列表, 总记录数)
        """
        try:
            # 验证排序字段安全性
            if not self._验证排序字段安全性(排序字段):
                排序字段 = "创建时间"

            # 验证排序方向
            if 排序方向.upper() not in ["ASC", "DESC"]:
                排序方向 = "DESC"

            # 构建查询条件
            查询条件 = []
            查询参数 = []
            param_count = 0

            # 基础查询 - 只查询用户有权限的店铺订单
            基础查询 = """
            SELECT 
                o.订单id, o.商品名称, o.商品数量, o.商品单价, o.商品总价,
                o.订单状态, o.下单时间, o.支付时间, o.买家昵称, o.店铺id,
                o.创建时间, o.作者账号, o.支付金额, o.佣金率, o.预估佣金支出,
                o.实际佣金支出, o.付款时间, o.收货时间, o.店铺名称
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = $1
                AND s.shop_id = o.店铺id
            )
            """
            param_count += 1
            查询参数.append(用户ID)

            # 添加筛选条件
            if 店铺ID:
                param_count += 1
                查询条件.append(f"o.店铺id = ${param_count}")
                查询参数.append(str(店铺ID))

            if 订单状态:
                param_count += 1
                查询条件.append(f"o.订单状态 = ${param_count}")
                查询参数.append(订单状态)

            if 商品名称:
                param_count += 1
                查询条件.append(f"o.商品名称 ILIKE ${param_count}")
                查询参数.append(f"%{商品名称}%")

            if 开始时间:
                param_count += 1
                查询条件.append(f"o.付款时间 >= ${param_count}")
                查询参数.append(开始时间)

            if 结束时间:
                param_count += 1
                查询条件.append(f"o.付款时间 <= ${param_count}")
                查询参数.append(结束时间)

            # 组装完整查询
            if 查询条件:
                完整查询 = 基础查询 + " AND " + " AND ".join(查询条件)
            else:
                完整查询 = 基础查询

            # 查询总数
            计数查询 = f"SELECT COUNT(*) as total FROM ({完整查询}) as count_query"
            计数结果 = await self.数据库连接池.执行查询(计数查询, 查询参数)
            总记录数 = 计数结果[0]["total"] if 计数结果 else 0

            # 添加排序和分页
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            分页查询 = f"""
            {完整查询}
            ORDER BY o."{排序字段}" {排序方向}
            LIMIT ${limit_param} OFFSET ${offset_param}
            """

            偏移量 = (页码 - 1) * 每页数量
            查询参数.extend([每页数量, 偏移量])

            # 执行查询
            订单列表 = await self.数据库连接池.执行查询(分页查询, 查询参数)

            数据库日志器.info(f"查询订单列表成功: 用户ID={用户ID}, 总记录数={总记录数}")
            return 订单列表 or [], 总记录数

        except Exception as e:
            错误日志器.error(f"查询订单列表失败: {e}", exc_info=True)
            return [], 0

    async def 查询订单详情(self, 订单id: str, 用户ID: int) -> Optional[Dict[str, Any]]:
        """
        查询订单详情

        参数:
            订单id: 订单ID
            用户ID: 用户ID

        返回:
            Optional[Dict]: 订单详情，如果不存在或无权限返回None
        """
        try:
            详情查询 = """
            SELECT o.*
            FROM 店铺订单表 o
            WHERE o.订单id = $1
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us
                WHERE us.用户ID = $2
                AND (us.店铺ID = CAST(o.店铺id AS INTEGER) OR o.店铺id IS NULL)
            )
            """

            详情结果 = await self.数据库连接池.执行查询(
                详情查询, (订单id, 用户ID)
            )

            if 详情结果:
                数据库日志器.info(f"查询订单详情成功: 订单ID={订单id}")
                return 详情结果[0]
            else:
                数据库日志器.warning(f"订单不存在或无权限: 订单ID={订单id}, 用户ID={用户ID}")
                return None

        except Exception as e:
            错误日志器.error(f"查询订单详情失败: {e}", exc_info=True)
            return None

    async def 获取订单状态选项(self, 用户ID: int) -> List[str]:
        """
        获取用户有权限访问的订单状态选项

        参数:
            用户ID: 用户ID

        返回:
            List[str]: 订单状态列表
        """
        try:
            状态查询 = """
            SELECT DISTINCT o.订单状态
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = $1
                AND s.shop_id = o.店铺id
            )
            AND o.订单状态 IS NOT NULL
            AND o.订单状态 != ''
            ORDER BY o.订单状态
            """

            状态结果 = await self.数据库连接池.执行查询(状态查询, (用户ID,))

            状态列表 = [row["订单状态"] for row in 状态结果] if 状态结果 else []
            数据库日志器.info(f"获取订单状态选项成功: 用户ID={用户ID}, 状态数量={len(状态列表)}")
            return 状态列表

        except Exception as e:
            错误日志器.error(f"获取订单状态选项失败: {e}", exc_info=True)
            return []

    async def 查找达人信息(self, 抖音火山号: str) -> Optional[int]:
        """
        根据抖音火山号查找达人表ID

        参数:
            抖音火山号: 抖音火山号

        返回:
            Optional[int]: 达人表id，如果找不到返回None
        """
        try:
            查找查询 = """
            SELECT id FROM kol.达人表
            WHERE account_douyin = $1
            LIMIT 1
            """

            查找结果 = await self.数据库连接池.执行查询(查找查询, (抖音火山号,))

            if 查找结果:
                达人id = 查找结果[0]["id"]
                数据库日志器.info(f"找到达人信息: 抖音号={抖音火山号}, 达人ID={达人id}")
                return 达人id
            else:
                数据库日志器.debug(f"未找到达人信息: 抖音号={抖音火山号}")
                return None

        except Exception as e:
            错误日志器.error(f"查找达人信息失败: {e}", exc_info=True)
            return None

    async def 检查用户订单认领记录存在(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """
        检查用户订单认领记录是否存在

        参数:
            抖音号: 抖音号
            达人表id: 达人表ID

        返回:
            Optional[int]: 如果存在返回记录id，不存在返回None
        """
        try:
            检查查询 = """
            SELECT id FROM 用户订单认领表
            WHERE 抖音号 = $1 AND 达人表id = $2
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(
                检查查询, (抖音号, 达人表id)
            )

            if 检查结果:
                记录id = 检查结果[0]["id"]
                数据库日志器.debug(f"找到现有认领记录: 抖音号={抖音号}, 记录ID={记录id}")
                return 记录id
            else:
                数据库日志器.debug(f"未找到认领记录: 抖音号={抖音号}, 达人ID={达人表id}")
                return None

        except Exception as e:
            错误日志器.error(f"检查用户订单认领记录失败: {e}", exc_info=True)
            return None

    async def 检查抖音号认领记录存在(self, 抖音号: str) -> Optional[int]:
        """
        检查抖音号的认领记录是否存在（不考虑达人表id）

        参数:
            抖音号: 抖音号

        返回:
            Optional[int]: 如果存在返回记录id，不存在返回None
        """
        try:
            检查查询 = """
            SELECT id FROM 用户订单认领表
            WHERE 抖音号 = $1
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(检查查询, (抖音号,))

            if 检查结果:
                记录id = 检查结果[0]["id"]
                数据库日志器.debug(f"找到抖音号认领记录: 抖音号={抖音号}, 记录ID={记录id}")
                return 记录id
            else:
                数据库日志器.debug(f"未找到抖音号认领记录: 抖音号={抖音号}")
                return None

        except Exception as e:
            错误日志器.error(f"检查抖音号认领记录失败: {e}", exc_info=True)
            return None

    async def 插入用户订单认领记录(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """
        插入用户订单认领记录

        参数:
            抖音号: 抖音号
            达人表id: 达人表ID

        返回:
            Optional[int]: 成功返回记录id，失败返回None
        """
        try:
            # 先检查是否已存在
            现有记录id = await self.检查用户订单认领记录存在(抖音号, 达人表id)
            if 现有记录id:
                数据库日志器.info(f"认领记录已存在，返回现有ID: {现有记录id}")
                return 现有记录id

            # 插入新记录
            插入查询 = """
            INSERT INTO 用户订单认领表 (抖音号, 达人表id, 创建时间)
            VALUES ($1, $2, CURRENT_TIMESTAMP)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(
                插入查询, (抖音号, 达人表id)
            )

            if 插入结果:
                记录id = 插入结果[0]["id"]
                数据库日志器.info(f"插入认领记录成功: 抖音号={抖音号}, 记录ID={记录id}")
                return 记录id
            else:
                错误日志器.error(f"插入认领记录失败: 抖音号={抖音号}")
                return None

        except Exception as e:
            错误日志器.error(f"插入用户订单认领记录失败: {e}", exc_info=True)
            return None

    async def 更新订单认领关联(self, 订单id: str, 认领记录id: int) -> bool:
        """
        更新订单的认领关联

        参数:
            订单id: 订单ID
            认领记录id: 认领记录ID

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单表
            SET 用户订单认领表id = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE 订单id = $2
            """

            更新结果 = await self.数据库连接池.执行更新(
                更新查询, (认领记录id, 订单id)
            )

            if 更新结果:
                数据库日志器.info(f"更新订单认领关联成功: 订单ID={订单id}, 认领ID={认领记录id}")
                return True
            else:
                错误日志器.error(f"更新订单认领关联失败: 订单ID={订单id}")
                return False

        except Exception as e:
            错误日志器.error(f"更新订单认领关联失败: {e}", exc_info=True)
            return False

    async def 批量检查订单存在(self, 订单ids: List[str]) -> set:
        """
        批量检查订单是否存在

        参数:
            订单ids: 订单ID列表

        返回:
            set: 已存在的订单ID集合
        """
        try:
            if not 订单ids:
                return set()

            # 构建批量查询
            占位符 = ", ".join([f"${i+1}" for i in range(len(订单ids))])
            批量查询 = f"""
            SELECT 订单id FROM 店铺订单表
            WHERE 订单id IN ({占位符})
            """

            查询结果 = await self.数据库连接池.执行查询(批量查询, 订单ids)

            已存在集合 = {row["订单id"] for row in 查询结果} if 查询结果 else set()
            数据库日志器.info(f"批量检查订单存在: 检查数量={len(订单ids)}, 已存在数量={len(已存在集合)}")
            return 已存在集合

        except Exception as e:
            错误日志器.error(f"批量检查订单存在失败: {e}", exc_info=True)
            return set()

    async def 批量插入订单数据(self, 订单数据列表: List[Dict[str, Any]]) -> Tuple[int, int]:
        """
        批量插入订单数据

        参数:
            订单数据列表: 订单数据字典列表

        返回:
            Tuple[int, int]: (成功插入数量, 失败数量)
        """
        try:
            if not 订单数据列表:
                return 0, 0

            成功数量 = 0
            失败数量 = 0

            # 获取第一条记录的字段作为模板
            字段列表 = list(订单数据列表[0].keys())

            # 验证字段安全性
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    错误日志器.error(f"批量插入失败: 不安全的字段名 {字段名}")
                    return 0, len(订单数据列表)

            # 构建批量插入SQL
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])
            单行占位符 = ", ".join([f"${i+1}" for i in range(len(字段列表))])

            # 使用事务进行批量插入
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.transaction():
                    for 订单数据 in 订单数据列表:
                        try:
                            插入查询 = f"""
                            INSERT INTO 店铺订单表 ({字段名称})
                            VALUES ({单行占位符})
                            """

                            数值列表 = [订单数据[字段] for 字段 in 字段列表]
                            await 连接.execute(插入查询, *数值列表)
                            成功数量 += 1

                        except Exception as e:
                            错误日志器.error(f"插入单条订单失败: {e}")
                            失败数量 += 1

            数据库日志器.info(f"批量插入订单完成: 成功={成功数量}, 失败={失败数量}")
            return 成功数量, 失败数量

        except Exception as e:
            错误日志器.error(f"批量插入订单数据失败: {e}", exc_info=True)
            return 0, len(订单数据列表) if 订单数据列表 else 0

    async def 创建导入记录(
        self,
        任务ID: str,
        用户ID: int,
        文件名: str,
        文件大小: int,
        文件路径: str,
        文件hash: str
    ) -> Optional[int]:
        """
        创建导入记录

        参数:
            任务ID: 任务ID
            用户ID: 用户ID
            文件名: 文件名
            文件大小: 文件大小（字节）
            文件路径: 文件路径
            文件hash: 文件哈希值

        返回:
            Optional[int]: 成功返回记录ID，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 订单导入记录表 (
                任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash,
                状态, 创建时间, 更新时间
            )
            VALUES ($1, $2, $3, $4, $5, $6, '进行中', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(
                插入查询, (任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash)
            )

            if 插入结果:
                记录ID = 插入结果[0]["id"]
                数据库日志器.info(f"创建导入记录成功: 任务ID={任务ID}, 记录ID={记录ID}")
                return 记录ID
            else:
                错误日志器.error(f"创建导入记录失败: 任务ID={任务ID}")
                return None

        except Exception as e:
            错误日志器.error(f"创建导入记录失败: {e}", exc_info=True)
            return None

    async def 更新导入记录状态(
        self,
        记录ID: int,
        状态: str,
        成功数量: int = 0,
        失败数量: int = 0,
        跳过数量: int = 0,
        错误信息: Optional[str] = None
    ) -> bool:
        """
        更新导入记录状态

        参数:
            记录ID: 记录ID
            状态: 新状态
            成功数量: 成功导入数量
            失败数量: 失败数量
            跳过数量: 跳过数量
            错误信息: 错误信息

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 订单导入记录表
            SET 状态 = $1, 成功数量 = $2, 失败数量 = $3, 跳过数量 = $4,
                错误信息 = $5, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $6
            """

            更新结果 = await self.数据库连接池.执行更新(
                更新查询, (状态, 成功数量, 失败数量, 跳过数量, 错误信息, 记录ID)
            )

            if 更新结果:
                数据库日志器.info(f"更新导入记录状态成功: 记录ID={记录ID}, 状态={状态}")
                return True
            else:
                错误日志器.error(f"更新导入记录状态失败: 记录ID={记录ID}")
                return False

        except Exception as e:
            错误日志器.error(f"更新导入记录状态失败: {e}", exc_info=True)
            return False

    async def 查询导入记录列表(
        self,
        用户ID: int,
        页码: int = 1,
        每页数量: int = 20,
        状态筛选: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询导入记录列表

        参数:
            用户ID: 用户ID
            页码: 页码
            每页数量: 每页数量
            状态筛选: 状态筛选

        返回:
            Tuple[List[Dict], int]: (记录列表, 总数)
        """
        try:
            # 构建查询条件
            查询条件 = ["用户ID = $1"]
            查询参数 = [用户ID]
            param_count = 1

            if 状态筛选:
                param_count += 1
                查询条件.append(f"状态 = ${param_count}")
                查询参数.append(状态筛选)

            条件字符串 = " AND ".join(查询条件)

            # 查询总数
            计数查询 = f"""
            SELECT COUNT(*) as total
            FROM 订单导入记录表
            WHERE {条件字符串}
            """

            计数结果 = await self.数据库连接池.执行查询(计数查询, 查询参数)
            总记录数 = 计数结果[0]["total"] if 计数结果 else 0

            # 查询列表
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            列表查询 = f"""
            SELECT
                id, 任务ID, 文件名, 文件大小, 状态, 成功数量, 失败数量, 跳过数量,
                错误信息, 创建时间, 更新时间
            FROM 订单导入记录表
            WHERE {条件字符串}
            ORDER BY 创建时间 DESC
            LIMIT ${limit_param} OFFSET ${offset_param}
            """

            偏移量 = (页码 - 1) * 每页数量
            查询参数.extend([每页数量, 偏移量])

            记录列表 = await self.数据库连接池.执行查询(列表查询, 查询参数)

            数据库日志器.info(f"查询导入记录列表成功: 用户ID={用户ID}, 总数={总记录数}")
            return 记录列表 or [], 总记录数

        except Exception as e:
            错误日志器.error(f"查询导入记录列表失败: {e}", exc_info=True)
            return [], 0


# 创建全局实例
店铺订单数据层实例 = 店铺订单数据层()
