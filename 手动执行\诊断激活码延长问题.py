#!/usr/bin/env python3
"""
诊断激活码延长时间问题
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.异步数据库函数 import 异步设置用户指定权限时间

async def 诊断激活码延长问题():
    """诊断激活码延长时间问题"""
    try:
        print("=== 激活码延长时间问题诊断 ===")
        
        # 1. 查看激活码类型表数据
        print("\n1. 查看激活码类型表数据:")
        激活码类型查询 = """
        SELECT id, 名称, 描述, 价格, 会员表id, 会员天数 
        FROM 激活码类型表 
        ORDER BY id
        """
        激活码类型列表 = await 异步连接池实例.执行查询(激活码类型查询)
        
        if 激活码类型列表:
            for 类型 in 激活码类型列表:
                print(f"  类型ID: {类型['id']}, 名称: {类型['名称']}, 会员表id: {类型['会员表id']}, 会员天数: {类型['会员天数']}")
        else:
            print("  激活码类型表为空")
            return
        
        # 2. 查看用户3当前的会员状态
        用户id = 3
        print(f"\n2. 查看用户{用户id}当前会员状态:")
        用户会员查询 = """
        SELECT um.用户id, um.会员id, um.开通时间, um.到期时间, m.名称 as 会员名称
        FROM 用户_会员_关联表 um
        LEFT JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = %s
        ORDER BY um.到期时间 DESC
        """
        用户会员记录 = await 异步连接池实例.执行查询(用户会员查询, (用户id,))
        
        if 用户会员记录:
            for 记录 in 用户会员记录:
                到期时间 = 记录['到期时间']
                当前时间 = datetime.now()
                if isinstance(到期时间, str):
                    到期时间_dt = datetime.strptime(到期时间, "%Y-%m-%d %H:%M:%S")
                else:
                    到期时间_dt = 到期时间
                
                是否过期 = 到期时间_dt <= 当前时间
                print(f"  会员ID: {记录['会员id']}, 会员名称: {记录['会员名称']}")
                print(f"  开通时间: {记录['开通时间']}")
                print(f"  到期时间: {记录['到期时间']}")
                print(f"  当前时间: {当前时间}")
                print(f"  是否过期: {是否过期}")
        else:
            print("  用户无会员记录")
        
        # 3. 测试激活码延长逻辑
        if 激活码类型列表 and 用户会员记录:
            测试类型 = 激活码类型列表[0]  # 使用第一个激活码类型
            print("\n3. 测试激活码延长逻辑:")
            print(f"  使用激活码类型: {测试类型['名称']}")
            print(f"  会员表id: {测试类型['会员表id']}")
            print(f"  会员天数: {测试类型['会员天数']}")
            
            if 测试类型['会员表id'] and 测试类型['会员天数']:
                # 记录延长前的时间
                延长前查询 = """
                SELECT 到期时间 FROM 用户_会员_关联表 
                WHERE 用户id = %s AND 会员id = %s
                """
                延长前记录 = await 异步连接池实例.执行查询(延长前查询, (用户id, 测试类型['会员表id']))
                
                if 延长前记录:
                    延长前时间 = 延长前记录[0]['到期时间']
                    print(f"  延长前到期时间: {延长前时间}")
                    
                    # 执行延长
                    增加秒数 = 测试类型['会员天数'] * 24 * 60 * 60
                    延长结果 = await 异步设置用户指定权限时间(用户id, 测试类型['会员表id'], 增加秒数)
                    print(f"  延长操作结果: {延长结果}")
                    
                    # 查看延长后的时间
                    延长后记录 = await 异步连接池实例.执行查询(延长前查询, (用户id, 测试类型['会员表id']))
                    if 延长后记录:
                        延长后时间 = 延长后记录[0]['到期时间']
                        print(f"  延长后到期时间: {延长后时间}")
                        
                        # 计算实际延长的时间
                        if isinstance(延长前时间, str):
                            延长前_dt = datetime.strptime(延长前时间, "%Y-%m-%d %H:%M:%S")
                        else:
                            延长前_dt = 延长前时间
                            
                        if isinstance(延长后时间, str):
                            延长后_dt = datetime.strptime(延长后时间, "%Y-%m-%d %H:%M:%S")
                        else:
                            延长后_dt = 延长后时间
                        
                        实际延长 = 延长后_dt - 延长前_dt
                        预期延长 = timedelta(seconds=增加秒数)
                        
                        print(f"  预期延长: {预期延长}")
                        print(f"  实际延长: {实际延长}")
                        print(f"  延长是否正确: {实际延长 == 预期延长}")
                else:
                    print("  用户没有对应的会员记录")
            else:
                print("  激活码类型配置不完整")
        
    except Exception as e:
        print(f"诊断失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(诊断激活码延长问题())
