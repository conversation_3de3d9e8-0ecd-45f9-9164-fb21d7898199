"""
用户账号相关路由
包含用户登录、注册、密码管理等账号相关功能
"""

from datetime import datetime

from fastapi import APIRouter, Depends, Request, Response

# 导入认证依赖项
from 依赖项.认证 import 获取当前用户

# 导入用户相关函数
from 数据.用户 import (
    获取JWT认证信息 as 异步获取用户JWT认证信息,  # 新增：获取完整认证信息用于JWT
    根据手机号获取用户信息 as 异步获取用户_电话,
)

# 导入非用户相关函数
from 数据.异步数据库函数 import (
    异步更新用户最后登录时间,
)
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入响应模型和状态码
from 数据模型.响应模型 import 统一响应模型
from 数据模型.用户模型 import 用户登录请求
from 日志 import 安全日志器, 错误日志器

# 导入日志系统
from 日志 import 应用日志器 as 接口日志器

# 导入服务层
from 服务.异步用户服务 import 异步处理权限状态, 异步认证服务
from 状态 import 状态

# 创建路由器
用户账号路由 = APIRouter(tags=["用户账号管理"])


# -----------------------------
# 路由处理函数
# -----------------------------
@用户账号路由.post("/login")
async def 登录(接口数据: 用户登录请求, 响应: Response, request: Request):
    """
    用户登录，返回 JWT 并设置Cookie
    :param 接口数据: 用户登录信息
    :param 响应: FastAPI响应对象，用于设置Cookie
    :param request: FastAPI请求对象，用于获取客户端信息
    :return: 登录结果，包含JWT令牌
    """
    try:
        # 接口调用日志
        接口日志器.info(
            f"开始登录流程: 手机号={接口数据.手机号}, IP={(request.client.host if request.client else '未知IP')}"
        )

        # 检查用户是否存在
        用户数据 = await 异步获取用户_电话(接口数据.手机号)
        if not 用户数据:
            安全日志器.warning(f"登录失败: 用户不存在 - {接口数据.手机号}")
            return 统一响应模型.失败(
                状态码=状态.用户.用户不存在,
                消息="用户不存在，请检查手机号或先注册账户。",
            )

        # 检查用户是否为未注册状态（半注册用户不允许登录）
        if 用户数据.get("状态") == "未注册":
            安全日志器.warning(f"登录失败: 用户为未注册状态 - {接口数据.手机号}")
            return 统一响应模型.失败(
                状态码=状态.用户.账户未激活, 消息="账户尚未完成注册，请先完成注册流程。"
            )

        # 验证密码
        if 接口数据.密码 != 用户数据["password"]:
            安全日志器.warning(f"登录失败: 密码错误 - {接口数据.手机号}")
            return 统一响应模型.失败(
                状态码=状态.用户.密码错误,
                消息="您输入的密码不正确，请重试。如果忘记密码，可以尝试找回。",
            )

        # 检查用户权限
        权限数据 = await 异步处理权限状态(用户数据["id"])
        if 权限数据 is None:
            安全日志器.warning(f"登录失败: 权限不足 - 用户ID: {用户数据['id']}")
            return 统一响应模型.失败(
                状态码=状态.用户.没有权限, 消息="抱歉，您的账户目前没有足够的权限。"
            )

        # 获取用户完整认证信息（包括会员信息和权限信息）
        完整认证信息 = await 异步获取用户JWT认证信息(用户数据["id"])

        # 如果获取完整信息失败，使用基本信息作为备用
        if not 完整认证信息:
            完整认证信息 = {
                "id": 用户数据["id"],
                "手机号": 用户数据["手机号"],
                "昵称": 用户数据.get("昵称", ""),
                "is_admin": 用户数据.get("is_admin", False),
                "会员信息": {
                    "会员id": None,
                    "会员名称": "免费用户",
                    "到期时间": None,
                    "算力点": 0,
                    "可创建团队数": 0,
                    "创建团队默认人数上限": 0,
                },
                "权限信息": [],
            }

        认证服务实例 = 异步认证服务()
        token = await 认证服务实例.生成令牌(完整认证信息)
        接口日志器.debug(f"令牌生成成功: {token[:20]}...")

        # 更新用户最后登录时间
        try:
            await 异步更新用户最后登录时间(用户数据["id"], request)
        except Exception as e:
            客户端IP = request.client.host if request.client else "未知IP"
            错误日志器.warning(
                f"更新用户登录时间失败: 用户ID={用户数据['id']}, IP={客户端IP}, 错误={str(e)}"
            )

        # 设置Cookie - 统一格式和安全设置
        响应.set_cookie(
            key="access_token",
            value=f"Bearer {token}",
            httponly=True,
            max_age=3600 * 24 * 7,  # 7天
            samesite="lax",
        )

        安全日志器.info(f"用户登录成功: {接口数据.手机号}, 用户ID: {用户数据['id']}")

        # 使用统一响应模型
        return 统一响应模型.成功(
            数据={"access_token": token, "token_type": "bearer", "权限": 权限数据},
            消息="登录成功",
        )

    except Exception as e:
        错误日志器.error(f"登录接口失败: {str(e)}", exc_info=True)
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误,
            消息="登录服务暂时遇到问题，请稍后重试。如果问题持续，请联系客服支持。",
        )


@用户账号路由.post("/update_custom_invite_code")
async def 更新用户自定义邀请码接口(
    请求数据: dict, 当前用户: dict = Depends(获取当前用户)
):
    """
    更新用户自定义邀请码
    """
    try:
        # 获取请求参数
        自定义邀请码 = 请求数据.get("用户自定义邀请码", "").strip()

        # 参数验证
        if 自定义邀请码 and len(自定义邀请码) > 200:
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误, 消息="自定义邀请码长度不能超过200个字符"
            )

        接口日志器.info(f"用户 {当前用户['id']} 请求更新自定义邀请码")

        # 调用数据库更新
        from 数据.异步连接池 import 异步连接池实例

        # 如果邀请码不为空，检查是否已被其他用户使用
        if 自定义邀请码:
            检查查询 = "SELECT id FROM 用户表 WHERE 用户自定义邀请码 = %s AND id != %s"
            检查结果 = await 异步连接池实例.执行查询(
                检查查询, (自定义邀请码, 当前用户["id"])
            )

            if 检查结果:
                接口日志器.warning(
                    f"用户 {当前用户['id']} 尝试使用已存在的邀请码: {自定义邀请码}"
                )
                return 统一响应模型.失败(
                    状态码=状态.通用.参数错误,
                    消息="该邀请码已被其他用户使用，请选择其他邀请码",
                )

        更新SQL = "UPDATE 用户表 SET 用户自定义邀请码 = $1 WHERE id = $2"
        影响行数 = await 异步连接池实例.执行更新(
            更新SQL, (自定义邀请码 if 自定义邀请码 else None, 当前用户["id"])
        )

        if 影响行数 > 0:
            接口日志器.info(f"用户 {当前用户['id']} 自定义邀请码更新成功")
            return 统一响应模型.成功(
                数据={"用户自定义邀请码": 自定义邀请码}, 消息="自定义邀请码更新成功"
            )
        else:
            return 统一响应模型.失败(状态码=状态.通用.失败, 消息="更新自定义邀请码失败")

    except Exception as e:
        错误日志器.error(
            f"更新用户自定义邀请码失败，用户ID: {当前用户['id']}, 错误: {str(e)}"
        )
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="服务器内部错误，请稍后重试"
        )


@用户账号路由.post("/generate_promotion_link")
async def 生成推广链接接口(当前用户: dict = Depends(获取当前用户)):
    """
    生成用户专属推广链接
    基于用户的自定义邀请码生成推广链接
    """
    try:
        接口日志器.info(f"用户 {当前用户['id']} 请求生成推广链接")

        # 调用数据库获取用户信息
        # 获取用户的自定义邀请码和代理类型
        用户查询 = """
        SELECT 用户自定义邀请码, 代理类型表id
        FROM 用户表
        WHERE id = %s
        """
        用户结果 = await 异步连接池实例.执行查询(用户查询, (当前用户["id"],))

        if not 用户结果:
            return 统一响应模型.失败(状态码=状态.用户.用户不存在, 消息="用户信息不存在")

        用户信息 = 用户结果[0]
        自定义邀请码 = 用户信息.get("用户自定义邀请码")
        代理类型表id = 用户信息.get("代理类型表id")

        # 检查用户是否设置了自定义邀请码
        if not 自定义邀请码:
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误, 消息="请先设置自定义邀请码后再生成推广链接"
            )

        # 检查用户是否为代理用户
        if not 代理类型表id:
            return 统一响应模型.失败(
                状态码=状态.通用.权限不足, 消息="仅代理用户可以生成推广链接"
            )

        # 生成推广链接
        import os

        前端域名 = os.getenv("FRONTEND_URL", "https://crm.limob.cn")
        推广链接 = f"{前端域名}/register?customInviteCode={自定义邀请码}"

        接口日志器.info(f"用户 {当前用户['id']} 推广链接生成成功")

        return 统一响应模型.成功(
            数据={
                "推广链接": 推广链接,
                "自定义邀请码": 自定义邀请码,
                "生成时间": datetime.now().isoformat(),
            },
            消息="推广链接生成成功",
        )

    except Exception as e:
        错误日志器.error(f"生成推广链接失败，用户ID: {当前用户['id']}, 错误: {str(e)}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="生成推广链接失败，请稍后重试"
        )


@用户账号路由.post("/validate_custom_invite_code")
async def 验证自定义邀请码接口(请求数据: dict):
    """
    验证自定义邀请码并返回推广用户信息
    用于注册页面验证邀请码有效性和推广用户会员状态
    """
    try:
        # 记录请求信息用于调试
        接口日志器.info(f"验证自定义邀请码接口被调用，请求数据: {请求数据}")

        # 检查请求数据
        if not 请求数据:
            return 统一响应模型.失败(状态码=状态.通用.参数错误, 消息="请求数据不能为空")

        # 获取请求参数
        自定义邀请码 = 请求数据.get("自定义邀请码", "").strip()

        if not 自定义邀请码:
            return 统一响应模型.失败(状态码=状态.通用.参数错误, 消息="邀请码不能为空")

        接口日志器.info(f"验证自定义邀请码: {自定义邀请码}")

        # 调用数据库查询推广用户信息
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 查询推广用户信息和会员状态
        推广用户查询 = """
        SELECT
            u.id as 用户ID,
            u.昵称,
            u.手机号,
            u.代理类型表id,
            u.用户自定义邀请码,
            (SELECT m.名称
             FROM 用户_会员_关联表 um
             LEFT JOIN 会员表 m ON um.会员id = m.id
             WHERE um.用户id = u.id
             AND TO_TIMESTAMP(um.到期时间, 'YYYY-MM-DD HH24:MI:SS') > CURRENT_TIMESTAMP
             ORDER BY m.id DESC
             LIMIT 1) as 会员名称
        FROM 用户表 u
        WHERE u.用户自定义邀请码 = $1
        """

        推广用户结果 = await 异步连接池实例.执行查询(推广用户查询, (自定义邀请码,))

        if not 推广用户结果:
            return 统一响应模型.失败(
                状态码=状态.通用.未找到, 消息="邀请码无效，请检查邀请码是否正确"
            )

        推广用户信息 = 推广用户结果[0]

        # 检查推广用户是否为代理用户
        if not 推广用户信息.get("代理类型表id"):
            return 统一响应模型.失败(
                状态码=状态.通用.权限不足, 消息="推广用户不是代理用户，无法使用此邀请码"
            )

        # 移除推广用户会员身份验证逻辑 - 允许所有代理用户进行推广绑定
        是否会员 = bool(推广用户信息.get("会员名称"))  # 保留字段用于信息展示

        接口日志器.info(
            f"邀请码验证成功: {自定义邀请码}, 推广用户ID: {推广用户信息['用户ID']}, 是否会员: {是否会员}"
        )

        return 统一响应模型.成功(
            数据={
                "推广用户ID": 推广用户信息["用户ID"],
                "推广用户昵称": 推广用户信息.get("昵称", ""),
                "推广用户手机号": 推广用户信息.get("手机号", ""),
                "是否会员": 是否会员,
                "会员名称": 推广用户信息.get("会员名称", ""),
                "可以绑定": True,  # 移除会员限制，允许所有代理用户绑定
                "验证时间": datetime.now().isoformat(),
            },
            消息="邀请码验证成功",  # 统一成功消息
        )

    except Exception as e:
        错误日志器.error(
            f"验证自定义邀请码失败，邀请码: {自定义邀请码}, 错误: {str(e)}"
        )
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="验证邀请码失败，请稍后重试"
        )


@用户账号路由.post("/process_custom_invite_registration")
async def 处理自定义邀请码注册关联接口(请求数据: dict):
    """
    处理基于自定义邀请码的注册关联
    在用户通过自定义邀请码注册成功后调用，建立推广关系
    """
    try:
        # 获取请求参数
        手机号 = 请求数据.get("手机号", "").strip()
        用户ID = 请求数据.get("用户ID")
        自定义邀请码 = 请求数据.get("自定义邀请码", "").strip()

        if not all([手机号, 用户ID, 自定义邀请码]):
            return 统一响应模型.失败(
                状态码=状态.通用.参数错误, 消息="手机号、用户ID和自定义邀请码不能为空"
            )

        接口日志器.info(
            f"处理自定义邀请码注册关联: 手机号={手机号}, 用户ID={用户ID}, 邀请码={自定义邀请码}"
        )

        # 调用数据库查询推广用户信息

        # 查询推广用户信息和会员状态
        推广用户查询 = """
        SELECT
            u.id as 推广用户ID,
            u.昵称 as 推广用户昵称,
            u.代理类型表id,
            (SELECT m.名称
             FROM 用户_会员_关联表 um
             LEFT JOIN 会员表 m ON um.会员id = m.id
             WHERE um.用户id = u.id
             AND STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
             ORDER BY m.id DESC
             LIMIT 1) as 会员名称
        FROM 用户表 u
        WHERE u.用户自定义邀请码 = %s
        """

        推广用户结果 = await 异步连接池实例.执行查询(推广用户查询, (自定义邀请码,))

        if not 推广用户结果:
            return 统一响应模型.失败(状态码=状态.通用.未找到, 消息="邀请码无效")

        推广用户信息 = 推广用户结果[0]
        推广用户ID = 推广用户信息["推广用户ID"]

        # 检查推广用户是否为代理用户
        if not 推广用户信息.get("代理类型表id"):
            接口日志器.warning(f"推广用户 {推广用户ID} 不是代理用户")
            return 统一响应模型.失败(
                状态码=状态.通用.权限不足, 消息="推广用户不是代理用户，无法建立推广关系"
            )

        # 移除推广用户会员身份验证逻辑 - 允许所有代理用户建立推广关系
        是否会员 = bool(推广用户信息.get("会员名称"))  # 保留字段用于日志记录

        接口日志器.info(f"推广用户 {推广用户ID} 会员状态: {是否会员}, 允许建立推广关系")

        # 开始数据库事务处理
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                try:
                    # 1. 更新新用户的邀请人字段
                    更新用户SQL = """
                    UPDATE 用户表
                    SET 邀请人 = %s
                    WHERE id = %s
                    """

                    await 游标.execute(更新用户SQL, (推广用户ID, 用户ID))
                    用户更新行数 = 游标.rowcount

                    if 用户更新行数 == 0:
                        await 连接.rollback()
                        return 统一响应模型.失败(
                            状态码=状态.通用.失败, 消息="更新用户邀请人字段失败"
                        )

                    # 2. 在用户客户邀请记录表中创建记录
                    插入邀请记录SQL = """
                    INSERT INTO 用户客户邀请记录表(
                        邀请人ID, 被邀请人ID, 注册时间, 邀请来源, 奖励状态, 创建时间
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                    """

                    当前时间 = datetime.now()
                    await 游标.execute(
                        插入邀请记录SQL,
                        (
                            推广用户ID,  # 邀请人ID
                            用户ID,  # 被邀请人ID
                            当前时间,  # 注册时间
                            "custom_invite_link",  # 邀请来源
                            "未发放",  # 奖励状态
                            当前时间,  # 创建时间
                        ),
                    )

                    邀请记录ID = 游标.lastrowid

                    # 提交事务
                    await 连接.commit()

                    接口日志器.info(
                        f"自定义邀请码注册关联成功: 新用户ID={用户ID}, 推广用户ID={推广用户ID}, 邀请记录ID={邀请记录ID}"
                    )

                    return 统一响应模型.成功(
                        数据={
                            "推广用户ID": 推广用户ID,
                            "推广用户昵称": 推广用户信息.get("推广用户昵称", ""),
                            "会员名称": 推广用户信息.get("会员名称", ""),
                            "邀请记录ID": 邀请记录ID,
                            "关联时间": 当前时间.isoformat(),
                        },
                        消息="推广关系建立成功",
                    )

                except Exception as db_error:
                    await 连接.rollback()
                    错误日志器.error(f"数据库事务失败: {str(db_error)}")
                    return 统一响应模型.失败(
                        状态码=状态.通用.失败, 消息="建立推广关系失败，数据库操作异常"
                    )

    except Exception as e:
        错误日志器.error(f"处理自定义邀请码注册关联失败: {str(e)}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="处理推广关系失败，请稍后重试"
        )


@用户账号路由.post("/get_promotion_stats")
async def 获取推广统计数据接口(当前用户: dict = Depends(获取当前用户)):
    """
    获取用户的推广统计数据
    包括通过推广链接注册的用户数量等统计信息
    """
    try:
        接口日志器.info(f"用户 {当前用户['id']} 请求获取推广统计数据")

        # 调用数据库查询推广统计
        # 修改为直接查询用户表的邀请人字段，更准确反映推广绑定关系
        统计查询 = """
        SELECT
            COUNT(*) as 总推广注册数,
            COUNT(CASE WHEN DATE(created_at) >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as 本月推广注册数,
            COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as 今日推广注册数
        FROM 用户表
        WHERE 邀请人 = $1
        AND 邀请人 IS NOT NULL
        """

        统计结果 = await 异步连接池实例.执行查询(统计查询, (当前用户["id"],))

        if 统计结果:
            统计数据 = 统计结果[0]
        else:
            统计数据 = {"总推广注册数": 0, "本月推广注册数": 0, "今日推广注册数": 0}

        # 修改为直接查询用户表，获取最近通过推广注册的用户
        最近注册查询 = """
        SELECT
            昵称 as 用户昵称,
            手机号,
            created_at as 注册时间
        FROM 用户表
        WHERE 邀请人 = %s
        AND 邀请人 IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 5
        """

        最近注册结果 = await 异步连接池实例.执行查询(最近注册查询, (当前用户["id"],))

        接口日志器.info(f"用户 {当前用户['id']} 推广统计查询成功")

        return 统一响应模型.成功(
            数据={
                "统计数据": {
                    "总推广注册数": 统计数据.get("总推广注册数", 0),
                    "本月推广注册数": 统计数据.get("本月推广注册数", 0),
                    "今日推广注册数": 统计数据.get("今日推广注册数", 0),
                },
                "最近注册用户": [
                    {
                        "用户昵称": 记录.get("用户昵称", ""),
                        "手机号": 记录.get("手机号", ""),
                        "注册时间": 记录.get("注册时间").isoformat()
                        if 记录.get("注册时间")
                        else "",
                    }
                    for 记录 in (最近注册结果 or [])
                ],
                "查询时间": datetime.now().isoformat(),
            },
            消息="推广统计数据获取成功",
        )

    except Exception as e:
        错误日志器.error(
            f"获取推广统计数据失败，用户ID: {当前用户['id']}, 错误: {str(e)}"
        )
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="获取推广统计数据失败，请稍后重试"
        )


@用户账号路由.post("/sync_member_agent_types")
async def 同步会员代理类型接口(当前用户: dict = Depends(获取当前用户)):
    """
    同步已有会员用户的代理类型
    为已开通会员但代理类型表id为空的用户补充设置代理类型
    """
    try:
        接口日志器.info(f"管理员 {当前用户['id']} 请求同步会员代理类型")

        # 检查管理员权限（可以根据需要调整权限检查逻辑）
        if 当前用户.get("手机号") != "15258387413":  # 仅允许特定管理员执行
            return 统一响应模型.失败(
                状态码=状态.通用.权限不足, 消息="权限不足，仅管理员可执行此操作"
            )

        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 开始数据库事务处理
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                try:
                    # 1. 查询需要同步的用户（有会员但代理类型为空的用户）
                    查询SQL = """
                    SELECT DISTINCT
                        u.id as 用户ID,
                        u.昵称,
                        u.代理类型表id as 当前代理类型id,
                        um.会员id,
                        m.名称 as 会员名称
                    FROM 用户表 u
                    INNER JOIN 用户_会员_关联表 um ON u.id = um.用户id
                    INNER JOIN 会员表 m ON um.会员id = m.id
                    WHERE STR_TO_DATE(um.到期时间, '%Y-%m-%d %H:%i:%s') > NOW()
                    AND (u.代理类型表id IS NULL OR u.代理类型表id = 0)
                    ORDER BY u.id
                    """

                    await 游标.execute(查询SQL)
                    需要同步的用户列表 = await 游标.fetchall()

                    if not 需要同步的用户列表:
                        return 统一响应模型.成功(
                            数据={"同步用户数": 0, "消息": "没有需要同步的用户"},
                            消息="同步完成",
                        )

                    # 2. 会员类型与代理类型的映射关系
                    # 根据业务逻辑，所有会员用户都获得代理权限（代理类型1）
                    def 获取代理类型id(会员id):
                        return 1  # 当前统一设置为代理类型1

                    # 3. 批量更新用户代理类型
                    更新成功数量 = 0
                    更新详情 = []

                    for 用户 in 需要同步的用户列表:
                        用户ID = 用户["用户ID"]
                        会员id = 用户["会员id"]
                        会员名称 = 用户["会员名称"]
                        当前代理类型id = 用户["当前代理类型id"]

                        # 获取对应的代理类型ID
                        代理类型id = 获取代理类型id(会员id)

                        # 更新用户代理类型
                        更新SQL = "UPDATE 用户表 SET 代理类型表id = %s WHERE id = %s"
                        await 游标.execute(更新SQL, (代理类型id, 用户ID))

                        更新成功数量 += 1
                        更新详情.append(
                            {
                                "用户ID": 用户ID,
                                "用户昵称": 用户["昵称"],
                                "会员名称": 会员名称,
                                "原代理类型": 当前代理类型id,
                                "新代理类型": 代理类型id,
                            }
                        )

                        接口日志器.info(
                            f"✅ 同步用户代理类型: 用户ID={用户ID}, 会员={会员名称}, "
                            f"代理类型: {当前代理类型id} → {代理类型id}"
                        )

                    # 提交事务
                    await 连接.commit()

                    接口日志器.info(
                        f"🎉 会员代理类型同步完成: 共更新 {更新成功数量} 个用户"
                    )

                    return 统一响应模型.成功(
                        数据={
                            "同步用户数": 更新成功数量,
                            "更新详情": 更新详情[:10],  # 只返回前10条详情，避免数据过多
                            "总数量": len(更新详情),
                        },
                        消息=f"同步完成，共更新 {更新成功数量} 个用户的代理类型",
                    )

                except Exception as db_error:
                    await 连接.rollback()
                    错误日志器.error(f"同步会员代理类型数据库操作失败: {str(db_error)}")
                    return 统一响应模型.失败(
                        状态码=状态.通用.失败, 消息="同步失败，数据库操作异常"
                    )

    except Exception as e:
        错误日志器.error(f"同步会员代理类型失败: {str(e)}")
        return 统一响应模型.失败(
            状态码=状态.通用.服务器错误, 消息="同步失败，请稍后重试"
        )
