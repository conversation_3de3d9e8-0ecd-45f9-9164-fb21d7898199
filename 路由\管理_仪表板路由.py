"""
管理员仪表板路由模块 - 用户注册统计
提供管理员专用的仪表板统计接口
"""

import traceback
from typing import Optional

from fastapi import APIRouter
from pydantic import BaseModel, Field

from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器, 错误日志器

# 创建管理员仪表板路由器
管理仪表板路由 = APIRouter(tags=["管理员仪表板"])


class 用户注册统计请求模型(BaseModel):
    """用户注册统计请求模型"""

    时间范围: str = Field(
        "today", description="时间范围: today/week/month/lastMonth/recent30"
    )


# 使用现有的异常处理和日志系统
def 处理接口异常(异常, 接口名称, 用户ID=None):
    """简化的异常处理函数"""
    错误信息 = f"{接口名称}失败: {str(异常)}"
    错误日志器.error(f"[{接口名称}] 用户ID: {用户ID} - {错误信息}")
    return 统一响应模型.失败(500, 错误信息)


@管理仪表板路由.post(
    "/stats/user-registration",
    summary="获取用户注册统计数据",
    description="获取用户注册时间统计图表数据",
)
async def 获取用户注册统计数据(请求: 用户注册统计请求模型):
    """
    获取用户注册统计数据

    参数:
    - **时间范围**: 时间范围 ('today'=今天24小时, 'week'=本周7天, 'month'=本月每天, 'lastMonth'=上月每天, 'recent30'=近30天)

    返回:
    - **status**: 状态码，100表示成功
    - **message**: 状态信息
    - **data**: 用户注册统计数据，包含:
        - 时间轴: 时间标签数组
        - 注册数据: 对应时间的注册人数数组
        - 总注册数: 时间范围内总注册人数
        - 日均注册: 平均每日注册人数
        - 最高单日: 单日最高注册人数
        - 增长率: 相比上一周期的增长率
    """
    接口名称 = "获取用户注册统计数据"

    try:
        应用日志器.info(f"[{接口名称}] 开始处理请求，时间范围: {请求.时间范围}")

        # 调用数据层获取统计数据
        from 数据.用户 import 异步获取用户注册统计

        统计数据 = await 异步获取用户注册统计(请求.时间范围)

        应用日志器.info(f"[{接口名称}] 成功获取用户注册统计数据")
        return 统一响应模型.成功(统计数据, "获取用户注册统计数据成功")

    except Exception as e:
        return await 处理接口异常(e, 接口名称, {"时间范围": 请求.时间范围})
