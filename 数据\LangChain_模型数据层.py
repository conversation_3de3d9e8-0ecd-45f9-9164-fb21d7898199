"""
LangChain模型数据层

功能：
1. 模型配置数据操作
2. 模型统计信息管理
3. 模型验证和查询
4. 模型状态管理
"""

from typing import Dict, List, Optional, Any
from 日志 import 应用日志器 as 模型数据日志器
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

class LangChain模型数据层:
    """LangChain模型配置数据层"""

    def __init__(self):
        self.已初始化 = False
        # 直接初始化数据库连接池实例 - 避免None类型问题
        self.数据库连接池 = 异步连接池实例

    async def 初始化(self):
        """初始化模型数据层"""
        try:
            # 数据库连接池已在__init__中初始化，这里只需要标记为已初始化
            self.已初始化 = True
            模型数据日志器.info("LangChain模型数据层初始化成功")
        except Exception as e:
            模型数据日志器.error(f"LangChain模型数据层初始化失败: {str(e)}")
            raise

    # ==================== 模型配置基础操作 ====================

    async def 创建模型配置(self, 模型数据: Dict[str, Any]) -> Optional[int]:
        """创建模型配置"""
        try:
            插入SQL = """
            INSERT INTO langchain_模型配置表 (
                模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL,
                最大令牌数, 算力消耗, 启用状态
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
            """

            参数 = (
                模型数据["模型名称"],
                模型数据.get("模型类型", "chat"),
                模型数据.get("显示名称", 模型数据["模型名称"]),
                模型数据.get("提供商", "openai"),
                模型数据.get("API密钥", ""),
                模型数据.get("API基础URL", ""),
                模型数据.get("最大令牌数", 4000),
                模型数据.get("算力消耗", 1.0),
                模型数据.get("启用状态", True)
            )

            结果 = await self.数据库连接池.执行查询(插入SQL, 参数)
            模型ID = 结果[0]['id'] if 结果 else None

            模型数据日志器.info(f"模型配置创建成功: {模型数据['模型名称']} (ID: {模型ID})")
            return 模型ID

        except Exception as e:
            模型数据日志器.error(f"创建模型配置失败: {str(e)}")
            raise

    async def 检查模型名称是否存在(self, 模型名称: str) -> bool:
        """检查模型名称是否已存在"""
        try:
            查询SQL = "SELECT COUNT(*) as count FROM langchain_模型配置表 WHERE 模型名称 = $1"
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return bool(结果 and len(结果) > 0 and 结果[0].get('count', 0) > 0)
        except Exception as e:
            模型数据日志器.error(f"检查模型名称存在失败: {str(e)}")
            raise

    async def 获取模型配置详情(self, 模型ID: int) -> Optional[Dict[str, Any]]:
        """获取模型配置详情"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL,
                   最大令牌数, 算力消耗, 启用状态, 调用次数, 成功次数,
                   失败次数, 创建时间, 更新时间
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型ID,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"获取模型配置详情失败: {str(e)}")
            raise

    async def 根据模型名称获取配置(self, 模型名称: str) -> Optional[Dict[str, Any]]:
        """根据模型名称获取配置"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL,
                   最大令牌数, 算力消耗, 启用状态, 调用次数, 成功次数,
                   失败次数, 创建时间, 更新时间
            FROM langchain_模型配置表
            WHERE 模型名称 = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据模型名称获取配置失败: {str(e)}")
            raise

    async def 更新模型配置(self, 模型ID: int, 更新数据: Dict[str, Any]) -> bool:
        """更新模型配置"""
        try:
            # 构建动态更新SQL
            更新字段 = []
            参数值 = []

            允许更新字段 = [
                "模型名称", "模型类型", "显示名称", "提供商", "API密钥",
                "API基础URL", "最大令牌数", "算力消耗", "启用状态"
            ]

            参数索引 = 1
            for 字段名, 字段值 in 更新数据.items():
                if 字段名 in 允许更新字段:
                    更新字段.append(f"{字段名} = ${参数索引}")
                    参数值.append(字段值)
                    参数索引 += 1

            if not 更新字段:
                模型数据日志器.warning(f"没有有效的更新字段: {模型ID}")
                return False

            # 添加更新时间
            更新字段.append("更新时间 = CURRENT_TIMESTAMP")
            参数值.append(模型ID)

            更新SQL = f"""
            UPDATE langchain_模型配置表
            SET {', '.join(更新字段)}
            WHERE id = ${参数索引}
            """

            await self.数据库连接池.执行更新(更新SQL, tuple(参数值))
            模型数据日志器.info(f"模型配置更新成功: {模型ID}")
            return True

        except Exception as e:
            模型数据日志器.error(f"更新模型配置失败: {str(e)}")
            raise

    async def 删除模型配置(self, 模型ID: int) -> bool:
        """删除模型配置"""
        try:
            删除SQL = "DELETE FROM langchain_模型配置表 WHERE id = $1"
            await self.数据库连接池.执行更新(删除SQL, (模型ID,))
            模型数据日志器.info(f"模型配置删除成功: {模型ID}")
            return True
        except Exception as e:
            模型数据日志器.error(f"删除模型配置失败: {str(e)}")
            raise

    # ==================== 模型统计信息管理 ====================

    async def 更新模型调用统计(self, 模型ID: int, 是否成功: bool = True) -> bool:
        """更新模型调用统计"""
        try:
            if 是否成功:
                更新SQL = """
                UPDATE langchain_模型配置表
                SET 调用次数 = 调用次数 + 1, 成功次数 = 成功次数 + 1, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $1
                """
            else:
                更新SQL = """
                UPDATE langchain_模型配置表
                SET 调用次数 = 调用次数 + 1, 失败次数 = 失败次数 + 1, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $1
                """

            await self.数据库连接池.执行更新(更新SQL, (模型ID,))
            模型数据日志器.debug(f"模型调用统计更新成功: {模型ID}, 成功: {是否成功}")
            return True
        except Exception as e:
            模型数据日志器.error(f"更新模型调用统计失败: {str(e)}")
            return False

    async def 获取模型统计信息(self, 模型ID: int) -> Dict[str, Any]:
        """获取模型统计信息"""
        try:
            统计SQL = """
            SELECT 调用次数, 成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(统计SQL, (模型ID,))

            if 结果:
                统计数据 = 结果[0]
                # 计算成功率
                调用次数 = 统计数据.get('调用次数', 0)
                成功次数 = 统计数据.get('成功次数', 0)
                成功率 = (成功次数 / 调用次数 * 100) if 调用次数 > 0 else 0

                return {
                    "调用次数": 调用次数,
                    "成功次数": 成功次数,
                    "失败次数": 统计数据.get('失败次数', 0),
                    "成功率": round(成功率, 2),
                    "创建时间": 统计数据.get('创建时间'),
                    "更新时间": 统计数据.get('更新时间')
                }
            else:
                return {
                    "调用次数": 0,
                    "成功次数": 0,
                    "失败次数": 0,
                    "成功率": 0,
                    "创建时间": None,
                    "更新时间": None
                }
        except Exception as e:
            模型数据日志器.error(f"获取模型统计信息失败: {str(e)}")
            return {}

    async def 获取所有模型统计概览(self) -> List[Dict[str, Any]]:
        """获取所有模型的统计概览"""
        try:
            概览SQL = """
            SELECT id, 模型名称, 显示名称, 模型类型, 提供商, 启用状态,
                   调用次数, 成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_模型配置表
            ORDER BY 调用次数 DESC, 创建时间 DESC
            """
            结果 = await self.数据库连接池.执行查询(概览SQL)

            统计列表 = []
            for 模型数据 in 结果:
                调用次数 = 模型数据.get('调用次数', 0)
                成功次数 = 模型数据.get('成功次数', 0)
                成功率 = (成功次数 / 调用次数 * 100) if 调用次数 > 0 else 0

                统计列表.append({
                    "id": 模型数据['id'],
                    "模型名称": 模型数据['模型名称'],
                    "显示名称": 模型数据['显示名称'],
                    "模型类型": 模型数据['模型类型'],
                    "提供商": 模型数据['提供商'],
                    "启用状态": 模型数据['启用状态'],
                    "调用次数": 调用次数,
                    "成功次数": 成功次数,
                    "失败次数": 模型数据.get('失败次数', 0),
                    "成功率": round(成功率, 2),
                    "创建时间": 模型数据.get('创建时间'),
                    "更新时间": 模型数据.get('更新时间')
                })

            return 统计列表
        except Exception as e:
            模型数据日志器.error(f"获取所有模型统计概览失败: {str(e)}")
            return []

    # ==================== 模型查询和验证 ====================

    async def 根据ID获取模型基本信息(self, 模型ID: int) -> Optional[Dict[str, Any]]:
        """根据模型ID获取基本信息"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, 启用状态
            FROM langchain_模型配置表
            WHERE id = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型ID,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据ID获取模型基本信息失败: {str(e)}")
            return None

    async def 根据名称获取模型基本信息(self, 模型名称: str) -> Optional[Dict[str, Any]]:
        """根据模型名称获取基本信息"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, 启用状态
            FROM langchain_模型配置表
            WHERE 模型名称 = $1
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型名称,))
            return 结果[0] if 结果 else None
        except Exception as e:
            模型数据日志器.error(f"根据名称获取模型基本信息失败: {str(e)}")
            return None

    async def 获取模型列表(self, 启用状态: Optional[bool] = None, 模型类型: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取模型列表（支持启用状态和模型类型过滤）"""
        try:
            # 构建基础查询
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, API基础URL, 最大令牌数,
                   启用状态, 调用次数, 成功次数, 失败次数, 创建时间, 更新时间
            FROM langchain_模型配置表
            """

            # 构建WHERE条件
            条件列表 = []
            参数列表 = []
            参数索引 = 1

            if 启用状态 is not None:
                条件列表.append(f"启用状态 = ${参数索引}")
                参数列表.append(启用状态)
                参数索引 += 1

            if 模型类型:
                条件列表.append(f"模型类型 = ${参数索引}")
                参数列表.append(模型类型)
                参数索引 += 1

            if 条件列表:
                查询SQL += " WHERE " + " AND ".join(条件列表)

            查询SQL += " ORDER BY 模型类型, 显示名称"

            # 执行查询
            if 参数列表:
                结果 = await self.数据库连接池.执行查询(查询SQL, tuple(参数列表))
            else:
                结果 = await self.数据库连接池.执行查询(查询SQL)

            # 处理结果，添加状态字段
            处理后结果 = []
            for 模型 in (结果 if 结果 else []):
                模型_副本 = dict(模型)
                模型_副本['状态'] = 'active' if 模型.get('启用状态') else 'inactive'
                处理后结果.append(模型_副本)

            return 处理后结果
        except Exception as e:
            模型数据日志器.error(f"获取模型列表失败: {str(e)}")
            return []

    async def 获取启用的模型列表(self, 模型类型: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取启用的模型列表"""
        return await self.获取模型列表(启用状态=True, 模型类型=模型类型)

    async def 验证模型存在且启用(self, 模型ID: int) -> bool:
        """验证模型是否存在且启用"""
        try:
            查询SQL = """
            SELECT COUNT(*) as count
            FROM langchain_模型配置表
            WHERE id = $1 AND 启用状态 = TRUE
            """
            结果 = await self.数据库连接池.执行查询(查询SQL, (模型ID,))
            return bool(结果 and len(结果) > 0 and 结果[0].get('count', 0) > 0)
        except Exception as e:
            模型数据日志器.error(f"验证模型存在且启用失败: {str(e)}")
            return False


# 创建全局模型数据层实例
LangChain模型数据层实例 = LangChain模型数据层()