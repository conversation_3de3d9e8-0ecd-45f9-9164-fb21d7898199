#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LangChain智能体系统 LCEL功能使用示例

本文件展示了如何使用新实现的LCEL功能，包括：
1. 基础对话智能体
2. RAG增强智能体
3. 工具调用智能体
4. 流式输出功能
"""

import asyncio
import json
from datetime import datetime

# 假设的导入（实际使用时需要根据项目结构调整）
# from 服务.LangChain_智能体服务 import LangChain智能体服务实例


class LCEL使用示例:
    """LCEL功能使用示例类"""

    def __init__(self):
        # 在实际使用中，这里应该是真实的服务实例
        # self.智能体服务 = LangChain智能体服务实例
        pass

    async def 基础对话示例(self):
        """基础对话智能体使用示例"""
        print("=" * 50)
        print("基础对话智能体示例")
        print("=" * 50)

        try:
            # 普通对话
            结果 = await self.智能体服务.智能体对话(
                智能体id=1,  # 基础对话型智能体ID
                用户表id=123,
                用户消息="你好，请介绍一下LCEL的优势",
                会话id=f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

            if 结果.get("status") == 100:
                print("✅ 对话成功")
                print(f"智能体回复: {结果['data']['智能体回复']}")
                print(f"处理时长: {结果['data']['处理时长']:.2f}秒")
                print(f"输出模式: {结果['data']['输出模式']}")
            else:
                print(f"❌ 对话失败: {结果.get('message')}")

        except Exception as e:
            print(f"❌ 示例执行失败: {e}")

    async def RAG智能体示例(self):
        """RAG增强智能体使用示例"""
        print("=" * 50)
        print("RAG增强智能体示例")
        print("=" * 50)

        try:
            # RAG增强对话
            结果 = await self.智能体服务.智能体对话(
                智能体id=2,  # RAG增强型智能体ID
                用户表id=123,
                用户消息="根据知识库内容，解释什么是并行执行优化？",
                会话id=f"rag_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

            if 结果.get("status") == 100:
                print("✅ RAG对话成功")
                print(f"智能体回复: {结果['data']['智能体回复']}")
                print(f"知识库使用: {结果['data']['知识库使用']}")
                print(f"处理时长: {结果['data']['处理时长']:.2f}秒")
            else:
                print(f"❌ RAG对话失败: {结果.get('message')}")

        except Exception as e:
            print(f"❌ RAG示例执行失败: {e}")

    async def 工具智能体示例(self):
        """工具调用智能体使用示例"""
        print("=" * 50)
        print("工具调用智能体示例")
        print("=" * 50)

        try:
            # 工具调用对话
            结果 = await self.智能体服务.智能体对话(
                智能体id=3,  # 工具调用型智能体ID
                用户表id=123,
                用户消息="请搜索并获取最新的AI技术发展趋势",
                会话id=f"tool_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

            if 结果.get("status") == 100:
                print("✅ 工具调用成功")
                print(f"智能体回复: {结果['data']['智能体回复']}")
                print(f"处理时长: {结果['data']['处理时长']:.2f}秒")
            else:
                print(f"❌ 工具调用失败: {结果.get('message')}")

        except Exception as e:
            print(f"❌ 工具示例执行失败: {e}")

    async def 流式对话示例(self):
        """流式对话使用示例"""
        print("=" * 50)
        print("流式对话示例")
        print("=" * 50)

        try:
            print("🌊 开始流式对话...")
            完整响应 = ""
            开始时间 = datetime.now()

            async for 流式块 in self.智能体服务.智能体流式对话(
                智能体id=1,
                用户表id=123,
                用户消息="请详细解释LCEL链式组合的工作原理和优势",
                会话id=f"stream_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            ):
                if 流式块["type"] == "chunk":
                    # 实时输出内容块
                    内容 = 流式块["content"]
                    print(内容, end="", flush=True)
                    完整响应 += 内容

                elif 流式块["type"] == "complete":
                    # 对话完成
                    处理时长 = (datetime.now() - 开始时间).total_seconds()
                    print("\n\n✅ 流式对话完成！")
                    print(f"总响应长度: {len(完整响应)} 字符")
                    print(f"总处理时长: {处理时长:.2f}秒")
                    print(f"平均速度: {len(完整响应) / 处理时长:.1f} 字符/秒")

                    # 显示元数据
                    元数据 = 流式块.get("metadata", {})
                    if 元数据:
                        print(f"Token消耗: {元数据.get('token_count', 0)}")
                        print(f"链类型: {元数据.get('chain_type', 'unknown')}")

                elif 流式块["type"] == "error":
                    # 处理错误
                    print(f"\n❌ 流式对话出错: {流式块['content']}")
                    break

        except Exception as e:
            print(f"❌ 流式对话示例执行失败: {e}")

    async def 结构化输出示例(self):
        """结构化输出使用示例"""
        print("=" * 50)
        print("结构化输出示例")
        print("=" * 50)

        try:
            # 假设智能体配置了JSON输出格式
            结果 = await self.智能体服务.智能体对话(
                智能体id=4,  # 配置了JSON输出的智能体ID
                用户表id=123,
                用户消息="请以JSON格式总结LCEL的三个核心功能",
                会话id=f"json_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            )

            if 结果.get("status") == 100:
                print("✅ 结构化输出成功")
                回复内容 = 结果["data"]["智能体回复"]

                # 尝试解析JSON
                try:
                    json_数据 = json.loads(回复内容)
                    print("📋 解析后的JSON数据:")
                    print(json.dumps(json_数据, ensure_ascii=False, indent=2))
                except json.JSONDecodeError:
                    print(f"📝 原始回复: {回复内容}")

                print(f"结构化输出: {结果['data']['结构化输出']}")
                print(f"输出格式: {结果['data']['输出格式']}")
            else:
                print(f"❌ 结构化输出失败: {结果.get('message')}")

        except Exception as e:
            print(f"❌ 结构化输出示例执行失败: {e}")

    async def 性能对比示例(self):
        """性能对比示例"""
        print("=" * 50)
        print("性能对比示例")
        print("=" * 50)

        问题 = "请详细解释人工智能的发展历程和未来趋势"

        # 普通对话性能测试
        print("🔄 测试普通对话性能...")
        开始时间 = datetime.now()

        _ = await self.智能体服务.智能体对话(
            智能体id=1,
            用户表id=123,
            用户消息=问题,
            会话id=f"perf_normal_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        )

        普通耗时 = (datetime.now() - 开始时间).total_seconds()
        print(f"普通对话耗时: {普通耗时:.2f}秒")

        # 流式对话性能测试
        print("🌊 测试流式对话性能...")
        开始时间 = datetime.now()
        首字节时间 = None

        async for 流式块 in self.智能体服务.智能体流式对话(
            智能体id=1,
            用户表id=123,
            用户消息=问题,
            会话id=f"perf_stream_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        ):
            if 流式块["type"] == "chunk" and 首字节时间 is None:
                首字节时间 = (datetime.now() - 开始时间).total_seconds()
            elif 流式块["type"] == "complete":
                总耗时 = (datetime.now() - 开始时间).total_seconds()
                break

        print(f"流式对话首字节时间: {首字节时间:.2f}秒")
        print(f"流式对话总耗时: {总耗时:.2f}秒")
        print(f"首字节时间改善: {((普通耗时 - 首字节时间) / 普通耗时 * 100):.1f}%")

    async def 运行所有示例(self):
        """运行所有示例"""
        print("🚀 开始运行LCEL功能示例")
        print("=" * 60)

        示例方法列表 = [
            self.基础对话示例,
            self.RAG智能体示例,
            self.工具智能体示例,
            self.流式对话示例,
            self.结构化输出示例,
            self.性能对比示例,
        ]

        for 示例方法 in 示例方法列表:
            try:
                await 示例方法()
                print("\n" + "-" * 30 + "\n")
                await asyncio.sleep(1)  # 短暂延迟
            except Exception as e:
                print(f"❌ 示例 {示例方法.__name__} 执行失败: {e}")
                continue

        print("🎉 所有示例执行完成！")


async def main():
    """主函数"""
    示例 = LCEL使用示例()

    # 注意：在实际使用中，需要先初始化智能体服务
    # 示例.智能体服务 = LangChain智能体服务实例

    # 运行所有示例
    await 示例.运行所有示例()


if __name__ == "__main__":
    # 运行示例
    print("LangChain智能体系统 LCEL功能使用示例")
    print("注意：本示例需要配置真实的智能体服务实例才能运行")

    # 取消注释以下行来运行示例
    # asyncio.run(main())
