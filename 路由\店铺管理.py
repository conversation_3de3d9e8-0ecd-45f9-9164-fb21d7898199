from typing import Any, Dict, Optional

from fastapi import APIRouter, Body, Depends, File, UploadFile
from pydantic import BaseModel

# 导入认证依赖
from 依赖项.认证 import 获取当前用户
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型

# 导入统一日志系统
from 日志 import 接口日志器, 错误日志器

# 导入状态码
from 状态 import 通用

# 创建店铺管理路由器
店铺管理路由 = APIRouter(
    tags=["店铺管理"],
    responses={404: {"description": "接口未找到"}},
)


# Pydantic模型
class 店铺详情请求(BaseModel):
    店铺ID: int


class 店铺绑定请求(BaseModel):
    shop_id: str
    shop_name: str
    avatar: str


class 店铺解绑请求(BaseModel):
    店铺ID: int


class 店铺更新请求(BaseModel):
    店铺ID: int
    店铺名称: Optional[str] = None
    店铺描述: Optional[str] = None
    店铺信息: Optional[Dict[str, Any]] = None


class 店铺统计请求(BaseModel):
    店铺ID: Optional[int] = None


# 导入记录列表查询请求模型
class 导入记录列表请求(BaseModel):
    页码: Optional[int] = 1
    每页数量: Optional[int] = 20
    状态筛选: Optional[str] = None


@店铺管理路由.post(
    "/detail", summary="获取店铺详情", description="获取指定店铺的详细信息"
)
async def 获取店铺详情(
    请求: 店铺详情请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取店铺详情

    参数:
        店铺ID: 店铺ID（必填）

    返回:
        店铺详细信息
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取店铺详情: 用户ID={用户ID}, 店铺ID={请求.店铺ID}")

        # 验证用户是否有权限访问该店铺
        权限查询 = """
        SELECT COUNT(*) as count
        FROM 用户_店铺 us
        WHERE us.用户ID = %s AND us.店铺ID = %s
        """

        权限结果 = await 异步连接池实例.执行查询(权限查询, (用户ID, 请求.店铺ID))

        if not 权限结果 or 权限结果[0]["count"] == 0:
            return 统一响应模型.失败(通用.无权限, "您没有权限访问该店铺信息")

        # 获取店铺详细信息
        店铺查询 = """
        SELECT
            s.id as 店铺ID,
            s.shop_id as 店铺标识,
            s.shop_name as 店铺名称,
            s.avatar as 店铺头像,
            s.创建时间,
            s.更新时间,
            COUNT(DISTINCT p.id) as 产品数量,
            COUNT(DISTINCT dy.id) as 抖音商品数量
        FROM 店铺 s
        LEFT JOIN 用户产品表 p ON p.用户ID = %s
        LEFT JOIN 抖音商品表 dy ON dy.用户ID = %s
        WHERE s.id = %s
        GROUP BY s.id
        """

        店铺详情 = await 异步连接池实例.执行查询(
            店铺查询, (用户ID, 用户ID, 请求.店铺ID)
        )

        if not 店铺详情:
            return 统一响应模型.失败(通用.未找到, "店铺信息不存在")

        店铺信息 = 店铺详情[0]

        # 格式化返回数据
        结果数据 = {
            "店铺ID": 店铺信息["店铺ID"],
            "店铺标识": 店铺信息["店铺标识"] or "",
            "店铺名称": 店铺信息["店铺名称"] or "",
            "店铺头像": 店铺信息["店铺头像"] or "",
            "创建时间": 店铺信息["创建时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 店铺信息["创建时间"]
            else "",
            "更新时间": 店铺信息["更新时间"].strftime("%Y-%m-%d %H:%M:%S")
            if 店铺信息["更新时间"]
            else "",
            "产品数量": 店铺信息["产品数量"] or 0,
            "抖音商品数量": 店铺信息["抖音商品数量"] or 0,
        }

        return 统一响应模型.成功(结果数据, "获取店铺详情成功")

    except Exception as e:
        错误日志器.error(f"获取店铺详情失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取店铺详情失败: {str(e)}")


@店铺管理路由.post("/bind", summary="绑定店铺", description="用户绑定新店铺")
async def 绑定店铺(
    请求: 店铺绑定请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    绑定店铺到用户账户

    参数:
        shop_id: 店铺标识（必填）
        shop_name: 店铺名称（必填）
        avatar: 店铺头像（必填）

    返回:
        绑定结果
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"绑定店铺: 用户ID={用户ID}, 店铺标识={请求.shop_id}")

        # 调用现有的店铺绑定服务
        from 服务.异步用户服务 import 异步收到店铺信息

        处理结果 = await 异步收到店铺信息(
            用户ID, 请求.shop_id, 请求.shop_name, 请求.avatar
        )

        # 转换为统一格式
        if isinstance(处理结果, dict) and "status" in 处理结果:
            if "message" not in 处理结果 and "msg" in 处理结果:
                处理结果["message"] = 处理结果["msg"]
                del 处理结果["msg"]
            if "data" not in 处理结果:
                处理结果["data"] = None
            return 处理结果

        return 统一响应模型.成功(处理结果, "店铺绑定成功")

    except Exception as e:
        错误日志器.error(f"绑定店铺失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"绑定店铺失败: {str(e)}")


@店铺管理路由.post("/unbind", summary="解绑店铺", description="用户解绑店铺")
async def 解绑店铺(
    请求: 店铺解绑请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    解绑用户店铺

    参数:
        店铺ID: 店铺ID（必填）

    返回:
        解绑结果
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"解绑店铺: 用户ID={用户ID}, 店铺ID={请求.店铺ID}")

        from 数据.异步连接池 import 异步连接池实例

        # 验证用户是否有权限操作该店铺
        权限查询 = """
        SELECT COUNT(*) as count
        FROM 用户_店铺 us
        WHERE us.用户ID = %s AND us.店铺ID = %s
        """

        权限结果 = await 异步连接池实例.执行查询(权限查询, (用户ID, 请求.店铺ID))

        if not 权限结果 or 权限结果[0]["count"] == 0:
            return 统一响应模型.失败(通用.无权限, "您没有权限操作该店铺")

        # 删除用户店铺关联
        删除查询 = """
        DELETE FROM 用户_店铺
        WHERE 用户ID = %s AND 店铺ID = %s
        """

        await 异步连接池实例.执行数据库删除(删除查询, (用户ID, 请求.店铺ID))

        return 统一响应模型.成功(None, "店铺解绑成功")

    except Exception as e:
        错误日志器.error(f"解绑店铺失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"解绑店铺失败: {str(e)}")


@店铺管理路由.post("/stats", summary="获取店铺统计", description="获取店铺统计数据")
async def 获取店铺统计(
    请求: 店铺统计请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取店铺统计数据

    参数:
        店铺ID: 店铺ID（可选，不传则获取用户所有店铺统计）

    返回:
        店铺统计信息
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取店铺统计: 用户ID={用户ID}")

        # 获取用户店铺统计
        if 请求.店铺ID:
            # 获取特定店铺统计
            统计查询 = """
            SELECT
                COUNT(DISTINCT s.id) as 总数,
                COUNT(DISTINCT CASE WHEN s.shop_name IS NOT NULL AND s.shop_name != '' THEN s.id END) as 活跃数量,
                COUNT(DISTINCT CASE WHEN s.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN s.id END) as 本月新增
            FROM 用户_店铺 us
            JOIN 店铺 s ON us.店铺ID = s.id
            WHERE us.用户ID = $1 AND s.id = $2
            """
            统计结果 = await 异步连接池实例.执行查询(
                统计查询, (用户ID, 请求.店铺ID)
            )
        else:
            # 获取用户所有店铺统计
            统计查询 = """
            SELECT
                COUNT(DISTINCT s.id) as 总数,
                COUNT(DISTINCT CASE WHEN s.shop_name IS NOT NULL AND s.shop_name != '' THEN s.id END) as 活跃数量,
                COUNT(DISTINCT CASE WHEN s.创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN s.id END) as 本月新增
            FROM 用户_店铺 us
            JOIN 店铺 s ON us.店铺ID = s.id
            WHERE us.用户ID = $1
            """
            统计结果 = await 异步连接池实例.执行查询(统计查询, (用户ID,))

        if 统计结果:
            统计数据 = 统计结果[0]
            结果数据 = {
                "总数": 统计数据["总数"] or 0,
                "活跃数量": 统计数据["活跃数量"] or 0,
                "本月新增": 统计数据["本月新增"] or 0,
            }
        else:
            结果数据 = {"总数": 0, "活跃数量": 0, "本月新增": 0}

        return 统一响应模型.成功(结果数据, "获取店铺统计成功")

    except Exception as e:
        错误日志器.error(f"获取店铺统计失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取店铺统计失败: {str(e)}")


@店铺管理路由.post("/update", summary="更新店铺信息", description="更新店铺信息")
async def 更新店铺信息(
    请求: 店铺更新请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    更新店铺信息

    参数:
        店铺ID: 店铺ID（必填）
        店铺名称: 店铺名称（可选）
        店铺描述: 店铺描述（可选）
        店铺信息: 店铺详细信息（可选）

    返回:
        更新结果
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"更新店铺信息: 用户ID={用户ID}, 店铺ID={请求.店铺ID}")

        from 数据.异步连接池 import 异步连接池实例

        # 验证用户是否有权限操作该店铺
        权限查询 = """
        SELECT COUNT(*) as count
        FROM 用户_店铺 us
        WHERE us.用户ID = %s AND us.店铺ID = %s
        """

        权限结果 = await 异步连接池实例.执行查询(权限查询, (用户ID, 请求.店铺ID))

        if not 权限结果 or 权限结果[0]["count"] == 0:
            return 统一响应模型.失败(通用.无权限, "您没有权限操作该店铺")

        # 构建更新语句
        更新字段 = []
        更新参数 = []

        if 请求.店铺名称 is not None:
            更新字段.append("shop_name = %s")
            更新参数.append(请求.店铺名称)

        if 更新字段:
            更新字段.append("更新时间 = NOW()")
            更新参数.append(请求.店铺ID)

            更新查询 = f"""
            UPDATE 店铺
            SET {", ".join(更新字段)}
            WHERE id = %s
            """

            await 异步连接池实例.执行更新(更新查询, tuple(更新参数))

        return 统一响应模型.成功(None, "店铺信息更新成功")

    except Exception as e:
        错误日志器.error(f"更新店铺信息失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"更新店铺信息失败: {str(e)}")


# 店铺订单管理相关接口


def _验证Excel文件安全性(file_content: bytes, filename: str) -> tuple[bool, str]:
    """
    验证Excel文件的安全性

    参数:
        file_content: 文件内容
        filename: 文件名

    返回:
        tuple[bool, str]: (是否安全, 错误信息)
    """
    # 1. 文件扩展名验证
    if not filename.lower().endswith((".xlsx", ".xls")):
        return False, "只支持Excel格式文件(.xlsx, .xls)"

    # 2. 文件大小验证 (35MB限制)
    if len(file_content) > 35 * 1024 * 1024:
        return False, "文件大小不能超过35MB"

    # 3. 文件魔数验证
    if len(file_content) < 8:
        return False, "文件内容不完整"

    # Excel文件魔数检查
    excel_signatures = [
        b"\x50\x4b\x03\x04",  # .xlsx (ZIP格式)
        b"\x50\x4b\x05\x06",  # .xlsx (空ZIP)
        b"\x50\x4b\x07\x08",  # .xlsx (ZIP变体)
        b"\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1",  # .xls (OLE2格式)
    ]

    file_header = file_content[:8]
    is_valid_excel = any(file_header.startswith(sig) for sig in excel_signatures)

    if not is_valid_excel:
        return False, "文件格式不正确，请上传有效的Excel文件"

    # 4. 检查是否为空文件
    if len(file_content) < 100:  # Excel文件至少应该有100字节
        return False, "文件内容过小，可能不是有效的Excel文件"

    return True, ""


@店铺管理路由.post(
    "/orders/import",
    summary="异步导入商户订单",
    description="通过Excel文件异步批量导入商户订单数据",
)
async def 导入商户订单(
    file: UploadFile = File(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    异步导入商户订单Excel文件 - 轻量级实现

    参数:
        file: Excel文件（支持.xlsx, .xls格式）

    返回:
        任务ID和导入记录ID
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"异步导入商户订单: 用户ID={用户ID}, 文件名={file.filename}")

        # 读取文件内容
        file_content = await file.read()

        # 验证文件名
        文件名 = file.filename or "未知文件.xlsx"

        # 安全验证
        is_safe, error_msg = _验证Excel文件安全性(file_content, 文件名)
        if not is_safe:
            return 统一响应模型.失败(通用.参数错误, error_msg)

        # 调用异步导入服务
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.异步导入Excel订单(
            file_content, 文件名, 用户ID
        )

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"异步导入商户订单失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"导入失败: {str(e)}")


@店铺管理路由.post(
    "/orders/import/progress",
    summary="查询导入进度",
    description="查询异步导入任务的进度状态",
)
async def 查询导入进度(请求: dict = Body(...), 当前用户: dict = Depends(获取当前用户)):
    """
    查询导入任务进度

    参数:
        任务ID: 导入任务的唯一标识（通过请求体传递）

    返回:
        导入进度信息
    """
    try:
        用户ID = 当前用户["id"]
        任务ID = 请求.get("任务ID")

        if not 任务ID:
            return 统一响应模型.失败(通用.参数错误, "任务ID不能为空")

        接口日志器.info(f"查询导入进度: 任务ID={任务ID}, 用户ID={用户ID}")

        # 调用服务层查询进度
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.查询导入进度(任务ID, 用户ID)

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"查询导入进度失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"查询进度失败: {str(e)}")


@店铺管理路由.post(
    "/orders/continue-import",
    summary="续传导入任务",
    description="续传超时的导入任务，从上次中断的位置继续处理",
)
async def 续传导入任务(请求: dict = Body(...), 当前用户: dict = Depends(获取当前用户)):
    """
    续传导入任务

    参数:
        导入记录ID: 导入记录的ID

    返回:
        续传结果
    """
    try:
        用户ID = 当前用户["id"]
        导入记录ID = 请求.get("导入记录ID")

        if not 导入记录ID:
            return 统一响应模型.失败(通用.参数错误, "导入记录ID不能为空")

        接口日志器.info(f"续传导入任务: 导入记录ID={导入记录ID}, 用户ID={用户ID}")

        # 调用服务层续传任务
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.续传导入任务(导入记录ID, 用户ID)

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"续传导入任务失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"续传失败: {str(e)}")


@店铺管理路由.post(
    "/orders/import/records",
    summary="获取导入记录列表",
    description="获取用户的所有导入记录列表",
)
async def 获取导入记录列表(
    请求: 导入记录列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取导入记录列表

    参数:
        页码: 页码（默认1）
        每页数量: 每页数量（默认20）
        状态筛选: 状态筛选（可选）

    返回:
        导入记录列表数据
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取导入记录列表: 用户ID={用户ID}")

        # 调用服务层处理业务逻辑
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.获取导入记录列表(
            用户ID, 请求.页码 or 1, 请求.每页数量 or 20, 请求.状态筛选 or ""
        )

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取导入记录列表失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取导入记录列表失败: {str(e)}")


# 店铺订单列表查询请求模型
class 订单列表请求(BaseModel):
    页码: Optional[int] = 1
    每页数量: Optional[int] = 20
    店铺ID: Optional[int] = None
    订单状态: Optional[str] = None
    商品名称: Optional[str] = None
    开始时间: Optional[str] = None
    结束时间: Optional[str] = None


@店铺管理路由.post(
    "/orders/list", summary="获取订单列表", description="分页获取商户订单列表"
)
async def 获取订单列表(
    请求: 订单列表请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取订单列表

    参数:
        页码: 页码（默认1）
        每页数量: 每页数量（默认20）
        店铺ID: 店铺ID（可选）
        订单状态: 订单状态（可选）
        开始时间: 开始时间（可选）
        结束时间: 结束时间（可选）

    返回:
        订单列表数据
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取订单列表: 用户ID={用户ID}")

        # 调用服务层处理业务逻辑
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.获取订单列表(
            用户ID=用户ID,
            页码=请求.页码 or 1,
            每页数量=请求.每页数量 or 20,
            店铺ID=请求.店铺ID,
            订单状态=请求.订单状态,
            商品名称=请求.商品名称,
            开始时间=请求.开始时间,
            结束时间=请求.结束时间,
        )

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取订单列表失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取订单列表失败: {str(e)}")


# 订单详情查询请求模型
class 订单详情请求(BaseModel):
    订单id: str  # 使用字符串避免大数精度问题


@店铺管理路由.post(
    "/orders/detail", summary="获取订单详情", description="获取单个订单的详细信息"
)
async def 获取订单详情(
    请求: 订单详情请求 = Body(...), 当前用户: dict = Depends(获取当前用户)
):
    """
    获取订单详情

    参数:
        订单id: 订单ID

    返回:
        订单详细信息
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取订单详情: 订单ID={请求.订单id}, 用户ID={用户ID}")

        # 调用服务层处理业务逻辑
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.查询订单详情(请求.订单id, 用户ID)

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取订单详情失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取订单详情失败: {str(e)}")


@店铺管理路由.get(
    "/orders/status-options",
    summary="获取订单状态选项",
    description="获取数据库中实际存在的订单状态选项",
)
async def 获取订单状态选项(当前用户: dict = Depends(获取当前用户)):
    """
    获取订单状态选项

    返回:
        订单状态选项列表
    """
    try:
        用户ID = 当前用户["id"]
        接口日志器.info(f"获取订单状态选项: 用户ID={用户ID}")

        # 调用服务层处理业务逻辑
        from 服务.店铺订单服务 import 店铺订单服务实例

        结果 = await 店铺订单服务实例.获取订单状态选项(用户ID)

        if 结果["status"] == 通用.成功:
            return 统一响应模型.成功(结果["data"], 结果["message"])
        else:
            return 统一响应模型.失败(结果["status"], 结果["message"])

    except Exception as e:
        错误日志器.error(f"获取订单状态选项失败: {str(e)}")
        return 统一响应模型.失败(通用.服务器错误, f"获取订单状态选项失败: {str(e)}")
