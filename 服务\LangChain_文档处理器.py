"""
LangChain文档处理器 - 支持多种文件格式的文档加载和处理
"""

import hashlib
import mimetypes
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from langchain.schema import Document

# LangChain文档加载器导入
from langchain_community.document_loaders import (
    # 数据文件
    CSVLoader,
    # Office文档
    Docx2txtLoader,
    JSONLoader,
    OutlookMessageLoader,
    # PDF文档
    PyMuPDFLoader,
    # 文本文档
    TextLoader,
    # 邮件文件
    UnstructuredEmailLoader,
    UnstructuredExcelLoader,
    UnstructuredHTMLLoader,
    # 图像文件
    UnstructuredImageLoader,
    UnstructuredMarkdownLoader,
    UnstructuredODTLoader,
    UnstructuredPowerPointLoader,
    UnstructuredRSTLoader,
    UnstructuredRTFLoader,
    UnstructuredTSVLoader,
    UnstructuredXMLLoader,
)

# LangChain导入
from langchain_text_splitters import (
    CharacterTextSplitter,
    RecursiveCharacterTextSplitter,
)

# 日志配置
from 日志 import 应用日志器 as 文档处理日志器

# 数据层导入
from 数据.LangChain_数据层 import LangChain数据层实例


class LineTextSplitter:
    """行级分块器 - 每行作为独立分块，专为列表数据设计"""

    def __init__(self, chunk_overlap=0):
        self.chunk_overlap = chunk_overlap

    def split_documents(self, documents):
        """将文档按行分割"""
        result_chunks = []
        for doc in documents:
            lines = doc.page_content.split("\n")
            for i, line in enumerate(lines):
                line = line.strip()
                if line:  # 跳过空行
                    chunk_doc = Document(
                        page_content=line,
                        metadata={
                            **doc.metadata,
                            "chunk_index": i,
                            "line_number": i + 1,
                            "chunk_size": len(line),
                            "chunk_type": "line",
                        },
                    )
                    result_chunks.append(chunk_doc)
        return result_chunks


class LangChain文档处理器:
    """LangChain文档处理器 - 支持多种文件格式"""

    def __init__(self):
        self.已初始化 = False
        self.支持的文件格式 = {}
        self.文本分割器 = None

    async def 初始化(self):
        """初始化文档处理器"""
        if self.已初始化:
            return

        try:
            # 初始化默认文本分割器（递归分块）
            self.文本分割器 = self._创建文本分割器("recursive", 1000, 200)

            # 配置支持的文件格式
            self._配置支持的文件格式()

            self.已初始化 = True
            文档处理日志器.info("LangChain文档处理器初始化成功")

        except Exception as e:
            文档处理日志器.error(f"LangChain文档处理器初始化失败: {str(e)}")
            raise

    def _创建文本分割器(
        self, 策略: str = "recursive", 分块大小: int = 1000, 分块重叠: int = 200
    ):
        """创建指定策略的文本分割器 - 基于Context7最佳实践优化"""
        try:
            文档处理日志器.debug(
                f"🔧 创建文本分割器: 策略={策略}, 大小={分块大小}, 重叠={分块重叠}"
            )

            # 🔧 精简配置：统一分隔符配置
            基础分隔符 = [
                "\n\n",
                "\n",
                "。",
                "！",
                "？",
                "；",
                ".",
                "!",
                "?",
                ";",
                " ",
                "",
            ]
            语义分隔符 = [
                "\n\n",
                "\n",
                "。",
                "！",
                "？",
                "；",
                "，",
                "、",
                ".",
                "!",
                "?",
                ";",
                ",",
                " ",
                "",
            ]

            if 策略 == "智能递归分块":
                # 智能递归分块 - 通用策略，按段落、句子递归分割
                return RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=基础分隔符,
                    keep_separator=False,
                )
            elif 策略 == "语义优化分块":
                # 语义优化分块 - 中文优化，适合列表和结构化数据
                return RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=语义分隔符,
                    keep_separator=False,
                )
            elif 策略 == "固定大小分块":
                # 固定大小分块 - 严格按字符数分割，不考虑语义
                return CharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    separator=" ",
                )
            elif 策略 == "行级精准分块":
                return LineTextSplitter(chunk_overlap=分块重叠)
            else:
                文档处理日志器.warning(f"⚠️ 未知分块策略: {策略}，使用默认智能递归分块")
                return RecursiveCharacterTextSplitter(
                    chunk_size=分块大小,
                    chunk_overlap=分块重叠,
                    length_function=len,
                    is_separator_regex=False,
                    separators=["\n\n", "\n", "。", "！", "？", " ", ""],
                    keep_separator=False,
                )

        except Exception as e:
            文档处理日志器.error(f"❌ 创建文本分割器失败: {str(e)}")
            # 返回最基础的默认分割器
            return RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
                separators=["\n\n", "\n", " ", ""],
            )

    def _配置支持的文件格式(self):
        """配置支持的文件格式和对应的加载器"""
        self.支持的文件格式 = {
            # 文本文档
            ".txt": {
                "loader_class": TextLoader,
                "type": "text",
                "description": "纯文本文件",
                "mime_types": ["text/plain"],
            },
            ".md": {
                "loader_class": UnstructuredMarkdownLoader,
                "type": "markdown",
                "description": "Markdown文档",
                "mime_types": ["text/markdown", "text/x-markdown"],
            },
            ".html": {
                "loader_class": UnstructuredHTMLLoader,
                "type": "html",
                "description": "HTML网页文件",
                "mime_types": ["text/html"],
            },
            ".htm": {
                "loader_class": UnstructuredHTMLLoader,
                "type": "html",
                "description": "HTML网页文件",
                "mime_types": ["text/html"],
            },
            ".xml": {
                "loader_class": UnstructuredXMLLoader,
                "type": "xml",
                "description": "XML文档",
                "mime_types": ["application/xml", "text/xml"],
            },
            ".rst": {
                "loader_class": UnstructuredRSTLoader,
                "type": "rst",
                "description": "reStructuredText文档",
                "mime_types": ["text/x-rst"],
            },
            # PDF文档
            ".pdf": {
                "loader_class": PyMuPDFLoader,
                "type": "pdf",
                "description": "PDF文档",
                "mime_types": ["application/pdf"],
            },
            # Office文档
            ".docx": {
                "loader_class": Docx2txtLoader,
                "type": "docx",
                "description": "Word文档",
                "mime_types": [
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ],
            },
            ".doc": {
                "loader_class": Docx2txtLoader,
                "type": "word",
                "description": "Word文档",
                "mime_types": ["application/msword"],
            },
            ".xlsx": {
                "loader_class": UnstructuredExcelLoader,
                "type": "xlsx",
                "description": "Excel电子表格",
                "mime_types": [
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                ],
            },
            ".xls": {
                "loader_class": UnstructuredExcelLoader,
                "type": "excel",
                "description": "Excel电子表格",
                "mime_types": ["application/vnd.ms-excel"],
            },
            ".pptx": {
                "loader_class": UnstructuredPowerPointLoader,
                "type": "pptx",
                "description": "PowerPoint演示文稿",
                "mime_types": [
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                ],
            },
            ".odt": {
                "loader_class": UnstructuredODTLoader,
                "type": "odt",
                "description": "OpenDocument文本文档",
                "mime_types": ["application/vnd.oasis.opendocument.text"],
            },
            ".rtf": {
                "loader_class": UnstructuredRTFLoader,
                "type": "rtf",
                "description": "富文本格式文档",
                "mime_types": ["application/rtf"],
            },
            # 数据文件
            ".csv": {
                "loader_class": CSVLoader,
                "type": "csv",
                "description": "CSV数据文件",
                "mime_types": ["text/csv"],
            },
            ".tsv": {
                "loader_class": UnstructuredTSVLoader,
                "type": "tsv",
                "description": "TSV数据文件",
                "mime_types": ["text/tab-separated-values"],
            },
            ".json": {
                "loader_class": JSONLoader,
                "type": "json",
                "description": "JSON数据文件",
                "mime_types": ["application/json"],
            },
            # 邮件文件
            ".eml": {
                "loader_class": UnstructuredEmailLoader,
                "type": "eml",
                "description": "电子邮件文件",
                "mime_types": ["message/rfc822"],
            },
            ".msg": {
                "loader_class": OutlookMessageLoader,
                "type": "msg",
                "description": "Outlook邮件文件",
                "mime_types": ["application/vnd.ms-outlook"],
            },
            # 图像文件（支持OCR）
            ".png": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "PNG图像文件",
                "mime_types": ["image/png"],
            },
            ".jpg": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "JPEG图像文件",
                "mime_types": ["image/jpeg"],
            },
            ".jpeg": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "JPEG图像文件",
                "mime_types": ["image/jpeg"],
            },
            ".gif": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "GIF图像文件",
                "mime_types": ["image/gif"],
            },
            ".bmp": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "BMP图像文件",
                "mime_types": ["image/bmp"],
            },
            ".tiff": {
                "loader_class": UnstructuredImageLoader,
                "type": "image",
                "description": "TIFF图像文件",
                "mime_types": ["image/tiff"],
            },
        }

    def 获取支持的文件格式列表(self) -> List[Dict[str, Any]]:
        """获取支持的文件格式列表"""
        格式列表 = []
        for 扩展名, 配置 in self.支持的文件格式.items():
            格式列表.append(
                {
                    "扩展名": 扩展名,
                    "类型": 配置["type"],
                    "描述": 配置["description"],
                    "MIME类型": 配置["mime_types"],
                }
            )
        return 格式列表

    def 检测文件类型(self, 文件路径: str) -> Tuple[str, str]:
        """检测文件类型"""
        try:
            文件路径对象 = Path(文件路径)
            扩展名 = 文件路径对象.suffix.lower()

            # 检查是否支持该扩展名
            if 扩展名 in self.支持的文件格式:
                配置 = self.支持的文件格式[扩展名]
                return 配置["type"], 配置["description"]

            # 尝试通过MIME类型检测
            mime_type, _ = mimetypes.guess_type(文件路径)
            if mime_type:
                for 扩展名, 配置 in self.支持的文件格式.items():
                    if mime_type in 配置["mime_types"]:
                        return 配置["type"], 配置["description"]

            return "other", "未知文件类型"

        except Exception as e:
            文档处理日志器.error(f"检测文件类型失败: {str(e)}")
            return "other", "文件类型检测失败"

    def 是否支持文件格式(self, 文件路径: str) -> bool:
        """检查是否支持该文件格式"""
        扩展名 = Path(文件路径).suffix.lower()
        return 扩展名 in self.支持的文件格式

    async def 处理文档文件(
        self,
        文件路径: str,
        知识库ID: int,
        文档名称: Optional[str] = None,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ) -> Dict[str, Any]:
        """处理文档文件并返回处理结果"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 检查文件是否存在
            if not os.path.exists(文件路径):
                return {"success": False, "error": "文件不存在"}

            # 检测文件类型
            文件类型, 类型描述 = self.检测文件类型(文件路径)

            if not self.是否支持文件格式(文件路径):
                return {"success": False, "error": f"不支持的文件格式: {文件类型}"}

            # 获取文件信息
            文件信息 = self._获取文件信息(文件路径)

            # 生成文档UUID
            文档UUID = self._生成文档UUID(文件路径, 文件信息["大小"])

            # 加载文档内容
            文档内容 = await self._加载文档内容(文件路径)

            if not 文档内容:
                return {"success": False, "error": "文档内容加载失败"}

            # 分割文档
            文档分块 = await self._分割文档(文档内容, 分块策略, 分块大小, 分块重叠)

            # 准备返回数据
            处理结果 = {
                "success": True,
                "文档UUID": 文档UUID,
                "文档名称": 文档名称 or 文件信息["名称"],
                "文档类型": 文件类型,
                "文档大小": 文件信息["大小"],
                "文档内容": 文档内容,
                "分块数量": len(文档分块),
                "文档分块": 文档分块,
                "元数据": {
                    "知识库ID": 知识库ID,
                    "文件路径": 文件路径,
                    "文件类型": 文件类型,
                    "类型描述": 类型描述,
                    "文件大小": 文件信息["大小"],
                    "处理时间": datetime.now().isoformat(),
                    "分块配置": {
                        "分块策略": 分块策略,
                        "分块大小": 分块大小,
                        "分块重叠": 分块重叠,
                    },
                    # 移除冗余的分块数量字段，使用数据库表字段
                },
            }

            文档处理日志器.info(
                f"文档处理成功: {文档名称 or 文件信息['名称']} ({文件类型}), "
                f"分块策略: {分块策略}, 分块数量: {len(文档分块)}"
            )
            return 处理结果

        except Exception as e:
            文档处理日志器.error(f"处理文档文件失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _获取文件信息(self, 文件路径: str) -> Dict[str, Any]:
        """获取文件基本信息"""
        文件路径对象 = Path(文件路径)
        文件统计 = os.stat(文件路径)

        return {
            "名称": 文件路径对象.name,
            "扩展名": 文件路径对象.suffix.lower(),
            "大小": 文件统计.st_size,
            "修改时间": datetime.fromtimestamp(文件统计.st_mtime),
            "创建时间": datetime.fromtimestamp(文件统计.st_mtime),
        }

    def _生成文档UUID(self, 文件路径: str, 文件大小: int) -> str:
        """生成唯一的文档UUID"""
        # 使用文件路径和大小生成哈希
        内容 = f"{文件路径}_{文件大小}_{datetime.now().timestamp()}"
        return hashlib.md5(内容.encode()).hexdigest()

    async def _加载文档内容(self, 文件路径: str) -> Optional[str]:
        """加载文档内容"""
        try:
            扩展名 = Path(文件路径).suffix.lower()

            if 扩展名 not in self.支持的文件格式:
                return None

            配置 = self.支持的文件格式[扩展名]
            加载器类 = 配置["loader_class"]

            # 创建加载器实例
            if 扩展名 == ".txt":
                # TXT文件需要特殊的编码处理
                加载器 = self._创建文本加载器(文件路径)
            elif 扩展名 == ".csv":
                # CSV需要特殊处理
                加载器 = 加载器类(file_path=文件路径)
            elif 扩展名 == ".json":
                # JSON需要特殊处理
                加载器 = 加载器类(file_path=文件路径, jq_schema=".")
            elif 扩展名 in [".xlsx", ".xls", ".pptx", ".odt", ".rtf"]:
                # Office文档使用elements模式
                加载器 = 加载器类(文件路径, mode="elements")
            else:
                # 其他文档类型
                加载器 = 加载器类(文件路径)

            # 加载文档
            文档列表 = 加载器.load()

            # 合并所有文档内容
            if 文档列表:
                内容 = "\n\n".join(
                    [doc.page_content for doc in 文档列表 if doc.page_content]
                )
                return 内容.strip()

            return None

        except Exception as e:
            文档处理日志器.error(f"加载文档内容失败: {str(e)}")

            # 如果是txt文件，尝试直接读取
            if 扩展名 == ".txt":
                return self._直接读取文本文件(文件路径)

            return None

    def _创建文本加载器(self, 文件路径: str):
        """创建文本加载器，支持多种编码"""
        # 尝试不同的编码方式
        编码列表 = ["utf-8", "gbk", "gb2312", "utf-16", "latin-1"]

        for 编码 in 编码列表:
            try:
                # 先尝试读取文件来验证编码
                with open(文件路径, "r", encoding=编码) as f:
                    f.read(100)  # 读取前100个字符来测试编码

                # 如果成功，创建TextLoader
                文档处理日志器.info(f"使用编码 {编码} 加载文本文件: {文件路径}")
                return TextLoader(文件路径, encoding=编码)

            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                文档处理日志器.warning(f"编码 {编码} 测试失败: {str(e)}")
                continue

        # 如果所有编码都失败，使用默认的TextLoader
        文档处理日志器.warning(f"无法确定文件编码，使用默认加载器: {文件路径}")
        return TextLoader(文件路径)

    def _直接读取文本文件(self, 文件路径: str) -> Optional[str]:
        """直接读取文本文件内容（备用方案）"""
        编码列表 = ["utf-8", "gbk", "gb2312", "utf-16", "latin-1", "cp1252"]

        for 编码 in 编码列表:
            try:
                with open(文件路径, "r", encoding=编码) as f:
                    内容 = f.read()
                    if 内容.strip():  # 确保内容不为空
                        文档处理日志器.info(f"直接读取成功，使用编码: {编码}")
                        return 内容.strip()
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                文档处理日志器.warning(f"直接读取失败 (编码: {编码}): {str(e)}")
                continue

        # 最后尝试二进制模式读取并尝试解码
        try:
            with open(文件路径, "rb") as f:
                原始内容 = f.read()

            # 尝试检测编码
            import chardet

            检测结果 = chardet.detect(原始内容)
            if 检测结果["encoding"]:
                try:
                    内容 = 原始内容.decode(检测结果["encoding"])
                    文档处理日志器.info(
                        f"使用chardet检测的编码成功: {检测结果['encoding']}"
                    )
                    return 内容.strip()
                except Exception:
                    pass
        except ImportError:
            # chardet不可用
            pass
        except Exception as e:
            文档处理日志器.warning(f"二进制读取失败: {str(e)}")

        文档处理日志器.error(f"无法读取文本文件: {文件路径}")
        return None

    async def _分割文档(
        self,
        文档内容: str,
        分块策略: str = "智能递归分块",
        分块大小: int = 1000,
        分块重叠: int = 200,
    ) -> List[Dict[str, Any]]:
        """分割文档为块 - 基于Context7最佳实践优化"""
        try:
            if not 文档内容 or not 文档内容.strip():
                文档处理日志器.warning("文档内容为空，无法分割")
                return []

            文档处理日志器.info(
                f"📄 开始文档分割: 策略={分块策略}, 大小={分块大小}, 重叠={分块重叠}"
            )

            # 根据参数创建文本分割器
            文本分割器 = self._创建文本分割器(分块策略, 分块大小, 分块重叠)

            # 创建文档对象并分割
            文档对象 = Document(
                page_content=文档内容,
                metadata={
                    "分块策略": 分块策略,
                    "分块大小": 分块大小,
                    "分块重叠": 分块重叠,
                    "原始长度": len(文档内容),
                },
            )

            分块列表 = 文本分割器.split_documents([文档对象])

            # 转换为优化的字典格式
            结果分块 = []
            for i, 分块 in enumerate(分块列表):
                # 清理分块内容
                清理后内容 = 分块.page_content.strip()
                if not 清理后内容:  # 跳过空分块
                    continue

                分块信息 = {
                    "分块索引": i,
                    "分块内容": 清理后内容,
                    "分块长度": len(清理后内容),
                    "字符数": len(清理后内容),
                    "词数估计": len(清理后内容.split()),
                    "元数据": {
                        **分块.metadata,
                        "分块位置": f"{i + 1}/{len(分块列表)}",
                        "内容预览": 清理后内容[:100] + "..."
                        if len(清理后内容) > 100
                        else 清理后内容,
                    },
                }
                结果分块.append(分块信息)

            文档处理日志器.info(f"✅ 文档分割完成: {len(结果分块)} 个有效分块")

            # 输出分块统计信息
            if 结果分块:
                平均长度 = sum(分块["分块长度"] for 分块 in 结果分块) / len(结果分块)
                最大长度 = max(分块["分块长度"] for 分块 in 结果分块)
                最小长度 = min(分块["分块长度"] for 分块 in 结果分块)

                文档处理日志器.info(
                    f"📊 分块统计: 平均长度={平均长度:.0f}, 最大长度={最大长度}, 最小长度={最小长度}"
                )

            return 结果分块

        except Exception as e:
            文档处理日志器.error(f"❌ 文档分割失败: {str(e)}")
            return []

    def 获取文件格式统计(self) -> Dict[str, Any]:
        """获取支持的文件格式统计信息"""
        类型统计 = {}

        for 扩展名, 配置 in self.支持的文件格式.items():
            文件类型 = 配置["type"]
            if 文件类型 not in 类型统计:
                类型统计[文件类型] = {
                    "类型名称": 文件类型,
                    "扩展名列表": [],
                    "描述": 配置["description"],
                }
            类型统计[文件类型]["扩展名列表"].append(扩展名)

        return {
            "支持的文件类型数量": len(类型统计),
            "支持的扩展名数量": len(self.支持的文件格式),
            "文件类型详情": list(类型统计.values()),
        }

    async def 手动向量化文档(self, 文档记录ID: int) -> Dict[str, Any]:
        """手动向量化单个文档"""
        try:
            # 导入PostgreSQL连接池
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 获取文档详情
            查询SQL = """
            SELECT id, 文档uuid, langchain_知识库表id, 文档名称, 用户文件路径, 文档类型,
                   文档大小, 文档内容, 向量分块数量, 元数据, 文档状态,
                   创建时间, 更新时间
            FROM langchain_知识库文档表
            WHERE id = $1
            """
            async with 异步连接池实例.获取连接() as 连接:
                文档详情 = await 连接.fetchrow(查询SQL, 文档记录ID)

            if not 文档详情:
                return {"success": False, "error": "文档不存在"}

            # 检查文档是否有内容
            文档内容 = 文档详情.get("文档内容")
            if not 文档内容:
                return {"success": False, "error": "文档内容为空"}

            # 从文档元数据中获取分块配置
            元数据 = 文档详情.get("元数据", {})
            if isinstance(元数据, str):
                import json

                元数据 = json.loads(元数据) if 元数据 else {}

            分块配置 = 元数据.get("分块配置", {})
            分块策略 = 分块配置.get("分块策略", "recursive")
            分块大小 = 分块配置.get("分块大小", 1000)
            分块重叠 = 分块配置.get("分块重叠", 200)

            # 分割文档
            文档分块 = await self._分割文档(文档内容, 分块策略, 分块大小, 分块重叠)
            if not 文档分块:
                return {"success": False, "error": "文档分块失败"}

            # 使用RAG引擎进行向量化
            from 服务.LangChain_RAG引擎 import RAG引擎实例

            if not RAG引擎实例.已初始化:
                await RAG引擎实例.初始化()

            # 更新文档状态为向量化中
            await LangChain数据层实例.更新文档向量化状态(文档记录ID, "处理中")

            向量化成功数量 = 0
            向量化失败数量 = 0

            # 检查嵌入模型是否可用
            if not RAG引擎实例.嵌入模型:
                文档处理日志器.error("嵌入模型不可用，无法进行向量化")
                向量化失败数量 = len(文档分块)
                await LangChain数据层实例.更新文档向量化状态(文档记录ID, "失败")
                return {
                    "success": False,
                    "error": "嵌入模型不可用，无法进行向量化",
                    "向量化成功数量": 向量化成功数量,
                    "向量化失败数量": 向量化失败数量,
                }

            # 对每个分块进行向量化
            for 分块信息 in 文档分块:
                try:
                    # 生成向量
                    向量数据 = RAG引擎实例.嵌入模型.embed_query(分块信息["分块内容"])

                    # 保存向量到数据库
                    向量记录 = {
                        "langchain_知识库文档表id": 文档记录ID,
                        "分块序号": 分块信息["分块索引"],
                        "分块内容": 分块信息["分块内容"],
                        "向量维度": len(向量数据),
                        "元数据": 分块信息.get("元数据", {}),
                    }

                    向量ID = await LangChain数据层实例.创建文档向量(向量记录)
                    if 向量ID:
                        向量化成功数量 += 1
                    else:
                        向量化失败数量 += 1

                except Exception as e:
                    文档处理日志器.error(f"向量化分块失败: {str(e)}")
                    向量化失败数量 += 1

            # 更新文档状态
            if 向量化失败数量 == 0:
                await LangChain数据层实例.更新文档向量化状态(文档记录ID, "已完成")
                await LangChain数据层实例._更新文档分块统计(文档记录ID)  # 同步分块数量
                状态 = "已完成"
            elif 向量化成功数量 > 0:
                # 部分成功的情况，标记为已完成（因为有部分向量可用于检索）
                await LangChain数据层实例.更新文档向量化状态(文档记录ID, "已完成")
                await LangChain数据层实例._更新文档分块统计(文档记录ID)  # 同步分块数量
                状态 = "已完成"
            else:
                await LangChain数据层实例.更新文档向量化状态(文档记录ID, "失败")
                状态 = "失败"

            文档处理日志器.info(
                f"手动向量化文档完成: {文档记录ID}, 成功: {向量化成功数量}, 失败: {向量化失败数量}"
            )

            return {
                "success": True,
                "message": "文档向量化完成",
                "统计信息": {
                    "文档记录ID": 文档记录ID,
                    "总分块数": len(文档分块),
                    "成功数量": 向量化成功数量,
                    "失败数量": 向量化失败数量,
                    "最终状态": 状态,
                },
            }

        except Exception as e:
            文档处理日志器.error(f"手动向量化文档失败: {str(e)}")
            # 更新文档状态为失败
            try:
                await LangChain数据层实例.更新文档向量化状态(文档记录ID, "失败")
            except Exception:
                pass

            return {"success": False, "error": f"手动向量化文档失败: {str(e)}"}


# 创建全局文档处理器实例
LangChain文档处理器实例 = LangChain文档处理器()
