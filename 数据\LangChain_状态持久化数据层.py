"""
LangChain状态持久化数据层
支持LangGraph状态管理和工具调用历史
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例, Postgre_异步数据库连接池

# 日志配置
状态数据日志器 = logging.getLogger("LangChain状态持久化数据层")


class LangChain状态持久化数据层:
    """LangGraph状态持久化数据层 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于状态持久化操作
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据库连接池: Optional[Postgre_异步连接池] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据库连接池: PostgreSQL连接池实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据库连接池永远不为None
        self.数据库连接池: Postgre_异步连接池 = 数据库连接池 or 异步连接池实例
        self.已初始化 = True  # 简化初始化逻辑

        状态数据日志器.info("LangChain状态持久化数据层创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain状态持久化数据层":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 创建数据层实例
        return cls(异步连接池实例)

    # ==================== 对话线程管理 ====================

    async def 创建对话线程(self, 用户ID: int, 智能体ID: int, 线程名称: str = None) -> str:
        """创建新的对话线程"""
        try:
            线程ID = f"thread_{uuid4().hex[:16]}"

            插入SQL = """
            INSERT INTO langchain_对话线程表
            (线程ID, 用户ID, 智能体ID, 线程名称, 线程元数据)
            VALUES ($1, $2, $3, $4, $5)
            """

            元数据 = {
                "created_by": "system",
                "version": "1.0"
            }

            await self.数据库连接池.执行更新(
                插入SQL,
                (线程ID, 用户ID, 智能体ID, 线程名称, json.dumps(元数据))
            )

            状态数据日志器.info(f"✅ 创建对话线程成功: {线程ID}")
            return 线程ID

        except Exception as e:
            状态数据日志器.error(f"❌ 创建对话线程失败: {str(e)}")
            raise

    async def 获取对话线程(self, 线程ID: str) -> Optional[Dict[str, Any]]:
        """获取对话线程信息"""
        try:
            查询SQL = """
            SELECT 线程ID, 用户ID, 智能体ID, 线程名称, 线程元数据,
                   创建时间, 更新时间
            FROM langchain_对话线程表
            WHERE 线程ID = $1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (线程ID,))

            if 结果:
                线程信息 = 结果[0]
                # 解析JSON字段
                if 线程信息.get('线程元数据'):
                    try:
                        线程信息['线程元数据'] = json.loads(线程信息['线程元数据'])
                    except json.JSONDecodeError:
                        线程信息['线程元数据'] = {}

                return 线程信息

            return None

        except Exception as e:
            状态数据日志器.error(f"❌ 获取对话线程失败: {str(e)}")
            return None

    async def 获取用户对话线程列表(self, 用户ID: int, 智能体ID: int = None, 限制数量: int = 50) -> List[Dict[str, Any]]:
        """获取用户的对话线程列表"""
        try:
            if 智能体ID:
                查询SQL = """
                SELECT ct.线程ID, ct.线程名称, ct.智能体ID, ac.智能体名称,
                       cm.总消息数, cm.最后活动时间, ct.创建时间
                FROM langchain_对话线程表 ct
                LEFT JOIN langchain_对话性能统计表 cm ON ct.线程ID = cm.线程ID
                LEFT JOIN langchain_智能体配置表 ac ON ct.智能体ID = ac.id
                WHERE ct.用户ID = $1 AND ct.智能体ID = $2
                ORDER BY cm.最后活动时间 DESC, ct.创建时间 DESC
                LIMIT $3
                """
                参数 = (用户ID, 智能体ID, 限制数量)
            else:
                查询SQL = """
                SELECT ct.线程ID, ct.线程名称, ct.智能体ID, ac.智能体名称,
                       cm.总消息数, cm.最后活动时间, ct.创建时间
                FROM langchain_对话线程表 ct
                LEFT JOIN langchain_对话性能统计表 cm ON ct.线程ID = cm.线程ID
                LEFT JOIN langchain_智能体配置表 ac ON ct.智能体ID = ac.id
                WHERE ct.用户ID = $1
                ORDER BY cm.最后活动时间 DESC, ct.创建时间 DESC
                LIMIT $2
                """
                参数 = (用户ID, 限制数量)

            结果 = await self.数据库连接池.执行查询(查询SQL, 参数)
            return 结果 if 结果 else []

        except Exception as e:
            状态数据日志器.error(f"❌ 获取用户对话线程列表失败: {str(e)}")
            return []

    # ==================== 状态检查点管理 ====================

    async def 保存检查点(self, 线程ID: str, 检查点ID: str, 状态数据: Dict[str, Any],
                        父检查点ID: str = None, 步骤编号: int = 0) -> bool:
        """保存LangGraph状态检查点"""
        try:
            插入SQL = """
            INSERT INTO langchain_状态检查点表
            (线程ID, 检查点ID, 父检查点ID, 状态数据,
             检查点元数据, 步骤编号)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (线程ID, 检查点ID) DO UPDATE SET
            状态数据 = EXCLUDED.状态数据,
            检查点元数据 = EXCLUDED.检查点元数据,
            步骤编号 = EXCLUDED.步骤编号
            """

            # 构建元数据
            元数据 = {
                "saved_at": datetime.now().isoformat(),
                "data_size": len(json.dumps(状态数据)),
                "step_number": 步骤编号
            }

            await self.数据库连接池.执行更新(
                插入SQL,
                (线程ID, 检查点ID, 父检查点ID,
                 json.dumps(状态数据, ensure_ascii=False),
                 json.dumps(元数据, ensure_ascii=False),
                 步骤编号)
            )

            状态数据日志器.debug(f"✅ 保存检查点成功: {检查点ID}")
            return True

        except Exception as e:
            状态数据日志器.error(f"❌ 保存检查点失败: {str(e)}")
            return False

    async def 获取检查点(self, 线程ID: str, 检查点ID: str = None) -> Optional[Dict[str, Any]]:
        """获取状态检查点"""
        try:
            if 检查点ID:
                查询SQL = """
                SELECT 检查点ID, 父检查点ID, 状态数据,
                       检查点元数据, 步骤编号, 创建时间
                FROM langchain_状态检查点表
                WHERE 线程ID = $1 AND 检查点ID = $2
                """
                参数 = (线程ID, 检查点ID)
            else:
                # 获取最新检查点
                查询SQL = """
                SELECT 检查点ID, 父检查点ID, 状态数据,
                       检查点元数据, 步骤编号, 创建时间
                FROM langchain_状态检查点表
                WHERE 线程ID = $1
                ORDER BY 步骤编号 DESC, 创建时间 DESC
                LIMIT 1
                """
                参数 = (线程ID,)

            结果 = await self.数据库连接池.执行查询(查询SQL, 参数)

            if 结果:
                检查点 = 结果[0]
                # 解析JSON字段
                try:
                    检查点['状态数据'] = json.loads(检查点['状态数据'])
                except json.JSONDecodeError:
                    检查点['状态数据'] = {}

                try:
                    检查点['检查点元数据'] = json.loads(检查点['检查点元数据'] or '{}')
                except json.JSONDecodeError:
                    检查点['检查点元数据'] = {}

                return 检查点

            return None

        except Exception as e:
            状态数据日志器.error(f"❌ 获取检查点失败: {str(e)}")
            return None

    async def 获取检查点历史(self, 线程ID: str, 限制数量: int = 20) -> List[Dict[str, Any]]:
        """获取检查点历史"""
        try:
            查询SQL = """
            SELECT checkpoint_id, parent_checkpoint_id, checkpoint_metadata,
                   step_number, created_at
            FROM langchain_conversation_checkpoints
            WHERE thread_id = $1
            ORDER BY step_number DESC, created_at DESC
            LIMIT $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (线程ID, 限制数量))
            
            if 结果:
                for 检查点 in 结果:
                    try:
                        检查点['checkpoint_metadata'] = json.loads(检查点['checkpoint_metadata'] or '{}')
                    except json.JSONDecodeError:
                        检查点['checkpoint_metadata'] = {}
            
            return 结果 if 结果 else []

        except Exception as e:
            状态数据日志器.error(f"❌ 获取检查点历史失败: {str(e)}")
            return []

    # ==================== 工具调用记录 ====================

    async def 记录工具调用开始(self, 线程ID: str, 工具名称: str, 工具参数: Dict[str, Any],
                           检查点ID: str = None) -> int:
        """记录工具调用开始"""
        try:
            插入SQL = """
            INSERT INTO langchain_工具执行记录表
            (线程ID, 检查点ID, 工具名称, 工具参数, 执行状态)
            VALUES ($1, $2, $3, $4, 'running')
            RETURNING id
            """

            结果 = await self.数据库连接池.执行查询(
                插入SQL,
                (线程ID, 检查点ID, 工具名称, json.dumps(工具参数, ensure_ascii=False))
            )
            执行ID = 结果[0]['id'] if 结果 else 0

            状态数据日志器.debug(f"✅ 记录工具调用开始: {工具名称} (ID: {执行ID})")
            return 执行ID

        except Exception as e:
            状态数据日志器.error(f"❌ 记录工具调用开始失败: {str(e)}")
            return 0

    async def 更新工具调用结果(self, 执行ID: int, 执行状态: str, 工具结果: Optional[str] = None,
                           错误信息: Optional[str] = None, 执行时间: Optional[float] = None) -> bool:
        """更新工具调用结果"""
        try:
            更新SQL = """
            UPDATE langchain_工具执行记录表
            SET 执行状态 = $1, 工具结果 = $2, 错误信息 = $3,
                执行时间 = $4, 完成时间 = CURRENT_TIMESTAMP
            WHERE id = $5
            """

            await self.数据库连接池.执行更新(
                更新SQL,
                (执行状态, 工具结果, 错误信息, 执行时间, 执行ID)
            )

            状态数据日志器.debug(f"✅ 更新工具调用结果: {执行ID} -> {执行状态}")
            return True

        except Exception as e:
            状态数据日志器.error(f"❌ 更新工具调用结果失败: {str(e)}")
            return False


    async def 获取工具调用历史(self, 线程ID: str, 限制数量: int = 50) -> List[Dict[str, Any]]:
        """获取工具调用历史"""
        try:
            查询SQL = """
            SELECT id, 工具名称, 工具参数, 工具结果, 执行状态,
                   执行时间, 错误信息, 开始时间, 完成时间
            FROM langchain_工具执行记录表
            WHERE 线程ID = $1
            ORDER BY 开始时间 DESC
            LIMIT $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (线程ID, 限制数量))

            if 结果:
                for 记录 in 结果:
                    try:
                        记录['工具参数'] = json.loads(记录['工具参数'] or '{}')
                    except json.JSONDecodeError:
                        记录['工具参数'] = {}

            return 结果 if 结果 else []

        except Exception as e:
            状态数据日志器.error(f"❌ 获取工具调用历史失败: {str(e)}")
            return []

    # ==================== 执行步骤记录 ====================

    async def 记录执行步骤(self, 线程ID: str, 步骤名称: str, 步骤类型: str,
                        检查点ID: str = None, 步骤输入: Dict[str, Any] = None,
                        步骤顺序: int = 0) -> int:
        """记录智能体执行步骤"""
        try:
            插入SQL = """
            INSERT INTO langchain_智能体执行步骤表
            (线程ID, 检查点ID, 步骤名称, 步骤类型, 步骤输入,
             步骤状态, 步骤顺序)
            VALUES ($1, $2, $3, $4, $5, 'running', $6)
            RETURNING id
            """

            结果 = await self.数据库连接池.执行查询(
                插入SQL,
                (线程ID, 检查点ID, 步骤名称, 步骤类型,
                 json.dumps(步骤输入 or {}, ensure_ascii=False), 步骤顺序)
            )
            步骤ID = 结果[0]['id'] if 结果 else 0

            状态数据日志器.debug(f"✅ 记录执行步骤: {步骤名称} (ID: {步骤ID})")
            return 步骤ID

        except Exception as e:
            状态数据日志器.error(f"❌ 记录执行步骤失败: {str(e)}")
            return 0

    async def 更新执行步骤(self, 步骤ID: int, 步骤状态: str, 步骤输出: Optional[Dict[str, Any]] = None,
                        错误信息: Optional[str] = None, 执行时间: Optional[float] = None) -> bool:
        """更新执行步骤状态"""
        try:
            更新SQL = """
            UPDATE langchain_智能体执行步骤表
            SET 步骤状态 = $1, 步骤输出 = $2, 错误信息 = $3,
                执行时间 = $4, 完成时间 = CURRENT_TIMESTAMP
            WHERE id = $5
            """

            await self.数据库连接池.执行更新(
                更新SQL,
                (步骤状态, json.dumps(步骤输出 or {}, ensure_ascii=False),
                 错误信息, 执行时间, 步骤ID)
            )

            状态数据日志器.debug(f"✅ 更新执行步骤: {步骤ID} -> {步骤状态}")
            return True

        except Exception as e:
            状态数据日志器.error(f"❌ 更新执行步骤失败: {str(e)}")
            return False

    async def 获取执行步骤历史(self, 线程ID: str, 限制数量: int = 100) -> List[Dict[str, Any]]:
        """获取执行步骤历史"""
        try:
            查询SQL = """
            SELECT id, step_name, step_type, step_input, step_output, step_status,
                   execution_time, error_message, step_order, started_at, completed_at
            FROM langchain_agent_execution_steps
            WHERE thread_id = $1
            ORDER BY step_order ASC, started_at ASC
            LIMIT $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (线程ID, 限制数量))

            if 结果:
                for 步骤 in 结果:
                    try:
                        步骤['step_input'] = json.loads(步骤['step_input'] or '{}')
                        步骤['step_output'] = json.loads(步骤['step_output'] or '{}')
                    except json.JSONDecodeError:
                        步骤['step_input'] = {}
                        步骤['step_output'] = {}

            return 结果 if 结果 else []

        except Exception as e:
            状态数据日志器.error(f"❌ 获取执行步骤历史失败: {str(e)}")
            return []

    # ==================== 性能统计 ====================

    async def 更新对话统计(self, 线程ID: str, 智能体ID: int, 消息数增量: int = 1,
                        工具调用数增量: int = 0, Token使用量: int = 0, 响应时间: float = 0) -> bool:
        """更新对话性能统计"""
        try:
            更新SQL = """
            INSERT INTO langchain_对话性能统计表
            (线程ID, 智能体ID, 总消息数, 总工具调用数, 总Token使用量,
             平均响应时间, 总执行时间, 最后活动时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
            ON CONFLICT (线程ID) DO UPDATE SET
            总消息数 = langchain_对话性能统计表.总消息数 + EXCLUDED.总消息数,
            总工具调用数 = langchain_对话性能统计表.总工具调用数 + EXCLUDED.总工具调用数,
            总Token使用量 = langchain_对话性能统计表.总Token使用量 + EXCLUDED.总Token使用量,
            平均响应时间 = (langchain_对话性能统计表.平均响应时间 * langchain_对话性能统计表.总消息数 + EXCLUDED.平均响应时间) / (langchain_对话性能统计表.总消息数 + EXCLUDED.总消息数),
            总执行时间 = langchain_对话性能统计表.总执行时间 + EXCLUDED.总执行时间,
            最后活动时间 = CURRENT_TIMESTAMP
            """

            await self.数据库连接池.执行更新(
                更新SQL,
                (线程ID, 智能体ID, 消息数增量, 工具调用数增量, Token使用量, 响应时间, 响应时间)
            )

            return True

        except Exception as e:
            状态数据日志器.error(f"❌ 更新对话统计失败: {str(e)}")
            return False


# 创建全局实例
LangChain状态持久化数据层实例 = LangChain状态持久化数据层()
