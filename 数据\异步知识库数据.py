"""
异步知识库数据操作模块

提供知识库相关的数据库操作函数，包括：
- 知识库表操作
- 知识库文档表操作  
- 知识库_知识库文档_关联表操作
"""

from typing import Optional, List, Dict, Any
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 系统日志器


class 异步知识库数据操作:
    """异步知识库数据操作类"""
    
    def __init__(self):
        self.连接池 = 异步连接池实例
        self.日志器 = 系统日志器
    
    # ==================== 知识库表操作 ====================
    
    async def 创建知识库记录(self, coze知识库id: str, 名称: str, 描述: str, 用户id: int) -> Optional[int]:
        """
        在知识库表中创建记录
        
        参数:
            coze知识库id: Coze平台的知识库ID
            名称: 知识库名称
            描述: 知识库描述
            用户id: 用户ID
            
        返回:
            创建成功返回知识库表ID，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 知识库表 (Coze知识库ID, 名称, 描述, 用户id, 创建时间, 更新时间)
            VALUES (%s, %s, %s, %s, NOW(), NOW())
            """
            
            async with self.连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    await 游标.execute(插入查询, (coze知识库id, 名称, 描述, 用户id))
                    await 连接.commit()
                    知识库表id = 游标.lastrowid
            
            self.日志器.info(f"知识库记录创建成功: ID={知识库表id}, Coze知识库ID={coze知识库id}, 用户ID={用户id}")
            return 知识库表id
            
        except Exception as e:
            self.日志器.error(f"创建知识库记录失败: {str(e)}")
            return None
    
    async def 根据coze知识库id获取知识库表id(self, coze知识库id: str) -> Optional[int]:
        """
        根据Coze知识库ID获取知识库表ID
        
        参数:
            coze知识库id: Coze平台的知识库ID
            
        返回:
            知识库表ID，未找到返回None
        """
        try:
            查询 = "SELECT id FROM 知识库表 WHERE Coze知识库ID = %s"
            结果 = await self.连接池.执行查询(查询, (coze知识库id,))
            
            if 结果:
                return 结果[0]['id'] if isinstance(结果[0], dict) else 结果[0][0]
            return None
            
        except Exception as e:
            self.日志器.error(f"根据Coze知识库ID获取知识库表ID失败: {str(e)}")
            return None
    
    async def 更新用户ai信息表知识库关联(self, 用户id: int, 知识库表id: int, ai类型: str = "默认") -> bool:
        """
        更新用户AI信息表中的知识库id字段（现在存储知识库表的ID）

        参数:
            用户id: 用户ID
            知识库表id: 知识库表的ID（整数）
            ai类型: AI类型，默认为"默认"

        返回:
            更新成功返回True，失败返回False
        """
        try:
            更新查询 = """
            UPDATE 用户ai信息表
            SET 知识库id = %s, 更新时间 = NOW()
            WHERE 用户ID = %s AND AI类型 = %s
            """

            async with self.连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    await 游标.execute(更新查询, (知识库表id, 用户id, ai类型))
                    await 连接.commit()
                    影响行数 = 游标.rowcount

            if 影响行数 > 0:
                self.日志器.info(f"用户AI信息表知识库关联更新成功: 用户ID={用户id}, 知识库表ID={知识库表id}")
                return True
            else:
                self.日志器.warning(f"用户AI信息表知识库关联更新失败: 未找到匹配记录, 用户ID={用户id}")
                return False

        except Exception as e:
            self.日志器.error(f"更新用户AI信息表知识库关联失败: {str(e)}")
            return False

    async def 获取用户默认知识库表id(self, 用户id: int) -> Optional[int]:
        """
        获取用户默认AI的知识库表ID

        参数:
            用户id: 用户ID

        返回:
            知识库表ID，未找到返回None
        """
        try:
            查询 = """
            SELECT 知识库id
            FROM 用户ai信息表
            WHERE 用户ID = %s AND AI类型 = '默认'
            ORDER BY 创建时间 DESC
            LIMIT 1
            """

            结果 = await self.连接池.执行查询(查询, (用户id,))

            if 结果:
                知识库id = 结果[0]['知识库id'] if isinstance(结果[0], dict) else 结果[0][0]
                return 知识库id if 知识库id > 0 else None
            return None

        except Exception as e:
            self.日志器.error(f"获取用户默认知识库表ID失败: {str(e)}")
            return None

    async def 获取用户知识库列表(self, 用户id: int) -> List[Dict[str, Any]]:
        """
        获取用户的知识库列表
        
        参数:
            用户id: 用户ID
            
        返回:
            知识库列表
        """
        try:
            查询 = """
            SELECT id, Coze知识库ID, 名称, 描述, 创建时间
            FROM 知识库表 
            WHERE 用户id = %s
            ORDER BY 创建时间 DESC
            """
            
            结果 = await self.连接池.执行查询(查询, (用户id,))
            
            知识库列表 = []
            if 结果:
                for 知识库 in 结果:
                    if isinstance(知识库, dict):
                        知识库列表.append({
                            "id": 知识库['id'],
                            "coze知识库id": 知识库['Coze知识库ID'],
                            "名称": 知识库['名称'],
                            "描述": 知识库['描述'],
                            "创建时间": 知识库['创建时间']
                        })
                    else:
                        知识库列表.append({
                            "id": 知识库[0],
                            "coze知识库id": 知识库[1],
                            "名称": 知识库[2],
                            "描述": 知识库[3],
                            "创建时间": 知识库[4]
                        })
            
            return 知识库列表
            
        except Exception as e:
            self.日志器.error(f"获取用户知识库列表失败: {str(e)}")
            return []
    
    # ==================== 知识库文档表操作 ====================
    
    async def 创建知识库文档记录(self, coze文档id: str, 文档名称: str, 产品id: int) -> Optional[int]:
        """
        在知识库文档表中创建记录
        
        参数:
            coze文档id: Coze平台的文档ID
            文档名称: 文档名称
            产品id: 产品ID
            
        返回:
            创建成功返回知识库文档表ID，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 知识库文档表 (coze_文档ID, 文档名称, 产品id, 创建时间, 更新时间)
            VALUES (%s, %s, %s, NOW(), NOW())
            """
            
            文档表id = await self.连接池.执行插入(插入查询, (coze文档id, 文档名称, 产品id))
            
            self.日志器.info(f"知识库文档记录创建成功: ID={文档表id}, Coze文档ID={coze文档id}, 产品ID={产品id}")
            return 文档表id
            
        except Exception as e:
            self.日志器.error(f"创建知识库文档记录失败: {str(e)}")
            return None
    
    async def 检查产品是否已提交到知识库(self, 产品id: int) -> bool:
        """
        检查产品是否已经提交到知识库
        
        参数:
            产品id: 产品ID
            
        返回:
            已提交返回True，未提交返回False
        """
        try:
            查询 = """
            SELECT COUNT(*) as count
            FROM 知识库文档表 kd
            INNER JOIN 知识库_知识库文档_关联表 ka ON kd.id = ka.知识库文档表id
            WHERE kd.产品id = %s AND ka.状态 = 1
            """
            
            结果 = await self.连接池.执行查询(查询, (产品id,))
            
            if 结果:
                数量 = 结果[0]['count'] if isinstance(结果[0], dict) else 结果[0][0]
                return 数量 > 0
            
            return False
            
        except Exception as e:
            self.日志器.error(f"检查产品知识库状态失败: {str(e)}")
            return False

    async def 软删除产品知识库文档(self, 产品id: int) -> bool:
        """
        软删除产品的知识库文档记录（将状态设为0）

        参数:
            产品id: 产品ID

        返回:
            删除成功返回True，失败返回False
        """
        try:
            更新查询 = """
            UPDATE 知识库_知识库文档_关联表 ka
            INNER JOIN 知识库文档表 kd ON ka.知识库文档表id = kd.id
            SET ka.状态 = 0, ka.更新时间 = NOW()
            WHERE kd.产品id = %s AND ka.状态 = 1
            """

            影响行数 = await self.连接池.执行更新(更新查询, (产品id,))

            if 影响行数 > 0:
                self.日志器.info(f"成功软删除产品知识库文档: 产品ID={产品id}, 影响行数={影响行数}")
                return True
            else:
                self.日志器.warning(f"未找到需要删除的产品知识库文档: 产品ID={产品id}")
                return True  # 没有记录也算成功

        except Exception as e:
            self.日志器.error(f"软删除产品知识库文档失败: {str(e)}")
            return False
    
    # ==================== 知识库_知识库文档_关联表操作 ====================
    
    async def 创建知识库文档关联(self, 知识库表id: int, 知识库文档表id: int) -> Optional[int]:
        """
        创建知识库与文档的关联记录
        
        参数:
            知识库表id: 知识库表ID
            知识库文档表id: 知识库文档表ID
            
        返回:
            创建成功返回关联表ID，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 知识库_知识库文档_关联表 (知识库表id, 知识库文档表id, 状态, 创建时间, 更新时间)
            VALUES (%s, %s, %s, NOW(), NOW())
            """
            
            关联id = await self.连接池.执行插入(插入查询, (知识库表id, 知识库文档表id, 1))
            
            self.日志器.info(f"知识库文档关联创建成功: ID={关联id}, 知识库表ID={知识库表id}, 文档表ID={知识库文档表id}")
            return 关联id
            
        except Exception as e:
            self.日志器.error(f"创建知识库文档关联失败: {str(e)}")
            return None
    
    # ==================== 综合查询操作 ====================
    
    async def 获取产品知识库状态(self, 产品id: int) -> Dict[str, Any]:
        """
        获取产品的知识库状态信息
        
        参数:
            产品id: 产品ID
            
        返回:
            包含知识库状态的字典
        """
        try:
            查询 = """
            SELECT 
                COUNT(DISTINCT kd.id) as 文档数量,
                MAX(kd.创建时间) as 最新提交时间,
                GROUP_CONCAT(DISTINCT kd.文档名称) as 文档名称列表,
                GROUP_CONCAT(DISTINCT kb.名称) as 知识库名称列表
            FROM 知识库文档表 kd
            LEFT JOIN 知识库_知识库文档_关联表 ka ON kd.id = ka.知识库文档表id
            LEFT JOIN 知识库表 kb ON ka.知识库表id = kb.id
            WHERE kd.产品id = %s AND ka.状态 = 1
            """
            
            结果 = await self.连接池.执行查询(查询, (产品id,))
            
            if 结果:
                数据 = 结果[0]
                if isinstance(数据, dict):
                    文档数量 = 数据['文档数量']
                    最新提交时间 = 数据['最新提交时间']
                    文档名称列表 = 数据['文档名称列表']
                    知识库名称列表 = 数据['知识库名称列表']
                else:
                    文档数量 = 数据[0]
                    最新提交时间 = 数据[1]
                    文档名称列表 = 数据[2]
                    知识库名称列表 = 数据[3]
                
                return {
                    "已提交": 文档数量 > 0,
                    "文档数量": 文档数量,
                    "最新提交时间": 最新提交时间.strftime("%Y-%m-%d %H:%M:%S") if 最新提交时间 else None,
                    "文档名称列表": 文档名称列表.split(',') if 文档名称列表 else [],
                    "知识库名称列表": 知识库名称列表.split(',') if 知识库名称列表 else []
                }
            
            return {
                "已提交": False,
                "文档数量": 0,
                "最新提交时间": None,
                "文档名称列表": [],
                "知识库名称列表": []
            }
            
        except Exception as e:
            self.日志器.error(f"获取产品知识库状态失败: {str(e)}")
            return {
                "已提交": False,
                "文档数量": 0,
                "最新提交时间": None,
                "文档名称列表": [],
                "知识库名称列表": []
            }
    
    async def 获取知识库信息(self, 知识库表id: int) -> Optional[Dict[str, Any]]:
        """获取知识库信息"""
        try:
            查询 = "SELECT Coze知识库ID, 名称, 描述 FROM 知识库表 WHERE id = %s"
            结果 = await self.连接池.执行查询(查询, (知识库表id,))
            if 结果:
                知识库信息 = 结果[0]
                return {
                    "coze知识库id": 知识库信息['Coze知识库ID'] if isinstance(知识库信息, dict) else 知识库信息[0],
                    "名称": 知识库信息['名称'] if isinstance(知识库信息, dict) else 知识库信息[1],
                    "描述": 知识库信息['描述'] if isinstance(知识库信息, dict) else 知识库信息[2]
                }
            return None
        except Exception as e:
            self.日志器.error(f"获取知识库信息失败: {str(e)}")
            return None

    async def 获取产品信息(self, 产品id: int) -> Optional[Dict[str, Any]]:
        """获取产品信息"""
        try:
            查询 = "SELECT 产品名称, 产品描述, 产品分类 FROM 用户产品表 WHERE id = %s"
            结果 = await self.连接池.执行查询(查询, (产品id,))
            if 结果:
                产品信息 = 结果[0]
                return {
                    "产品名称": 产品信息['产品名称'] if isinstance(产品信息, dict) else 产品信息[0],
                    "产品描述": 产品信息['产品描述'] if isinstance(产品信息, dict) else 产品信息[1],
                    "产品分类": 产品信息['产品分类'] if isinstance(产品信息, dict) else 产品信息[2]
                }
            return None
        except Exception as e:
            self.日志器.error(f"获取产品信息失败: {str(e)}")
            return None

    async def 保存知识库文档记录(self, coze文档id: str, 文档名称: str, 产品id: int, 知识库表id: int) -> bool:
        """保存知识库文档记录到数据库"""
        try:
            # 1. 创建知识库文档记录
            知识库文档表id = await self.创建知识库文档记录(coze文档id, 文档名称, 产品id)
            if not 知识库文档表id:
                return False

            # 2. 创建知识库与文档的关联
            关联id = await self.创建知识库文档关联(知识库表id, 知识库文档表id)
            if not 关联id:
                return False

            self.日志器.info(f"保存知识库文档记录成功: 产品ID={产品id}, 知识库表ID={知识库表id}, Coze文档ID={coze文档id}")
            return True

        except Exception as e:
            self.日志器.error(f"保存知识库文档记录失败: {str(e)}")
            return False




class 异步用户AI数据操作:
    """异步用户AI数据操作类"""

    def __init__(self):
        self.连接池 = 异步连接池实例
        self.日志器 = 系统日志器

    async def 检查用户是否有默认AI(self, 用户id: int) -> bool:
        """检查用户是否已经有默认类型的AI智能体"""
        try:
            查询 = "SELECT id FROM 用户ai信息表 WHERE 用户ID = %s AND AI类型 = '默认' LIMIT 1"
            结果 = await self.连接池.执行查询(查询, (用户id,))
            return len(结果) > 0
        except Exception as e:
            self.日志器.error(f"检查用户默认AI失败: {str(e)}")
            return False

    async def 获取默认推荐模型信息(self) -> Optional[Dict[str, Any]]:
        """获取默认推荐模型信息"""
        try:
            查询 = """
            SELECT id, coze模型id, 名称, 算力消耗
            FROM ai模型表
            WHERE 是否推荐 = 1 AND 是否启用 = 1
            ORDER BY id LIMIT 1
            """
            结果 = await self.连接池.执行查询(查询)

            if 结果:
                模型 = 结果[0]
                return {
                    "id": 模型['id'] if isinstance(模型, dict) else 模型[0],
                    "coze模型id": 模型['coze模型id'] if isinstance(模型, dict) else 模型[1],
                    "名称": 模型['名称'] if isinstance(模型, dict) else 模型[2],
                    "算力消耗": 模型['算力消耗'] if isinstance(模型, dict) else 模型[3]
                }
            return None
        except Exception as e:
            self.日志器.error(f"获取默认推荐模型失败: {str(e)}")
            return None

    async def 创建用户AI信息(self, 用户id: int, 组织id: int, 模型id: int, 员工名称: str,
                          员工性格: str, 店铺名称: str, 知识库id: int, 智能体id: str) -> Optional[int]:
        """创建用户AI信息记录"""
        try:
            插入查询 = """
            INSERT INTO 用户ai信息表
            (用户ID, 组织ID, 模型ID, 员工名称, 员工性格, 店铺名称, 知识库id, 智能体ID, AI类型, 创建时间, 更新时间)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """

            用户ai信息id = await self.连接池.执行插入(
                插入查询,
                (用户id, 组织id, 模型id, 员工名称, 员工性格, 店铺名称, 知识库id, 智能体id, '默认')
            )

            self.日志器.info(f"用户AI信息创建成功: ID={用户ai信息id}, 用户ID={用户id}")
            return 用户ai信息id

        except Exception as e:
            self.日志器.error(f"创建用户AI信息失败: {str(e)}")
            return None

    async def 获取用户默认AI信息(self, 用户id: int) -> Optional[Dict[str, Any]]:
        """获取用户默认AI信息"""
        try:
            查询 = """
            SELECT
                uai.id, uai.用户ID, uai.组织ID, uai.模型ID,
                uai.智能体ID, uai.知识库id, uai.知识库文档IDs,
                uai.员工名称, uai.员工性格, uai.店铺名称, uai.AI类型,
                am.名称 as 模型名称, am.描述 as 模型描述, am.算力消耗, am.头像,
                am.coze模型id as Coze模型ID,
                kb.Coze知识库ID as 知识库Coze_ID, kb.名称 as 知识库名称,
                uai.创建时间, uai.更新时间
            FROM 用户ai信息表 uai
            LEFT JOIN ai模型表 am ON uai.模型ID = am.id
            LEFT JOIN 知识库表 kb ON uai.知识库id = kb.id
            WHERE uai.用户ID = %s AND uai.AI类型 = '默认'
            LIMIT 1
            """

            结果 = await self.连接池.执行查询(查询, (用户id,))

            if 结果:
                数据 = 结果[0]
                if isinstance(数据, dict):
                    return 数据
                else:
                    # 处理元组格式
                    字段名 = [
                        'id', '用户ID', '组织ID', '模型ID', '智能体ID', '知识库id', '知识库文档IDs',
                        '员工名称', '员工性格', '店铺名称', 'AI类型', '模型名称', '模型描述',
                        '算力消耗', '头像', 'Coze模型ID', '知识库Coze_ID', '知识库名称', '创建时间', '更新时间'
                    ]
                    return dict(zip(字段名, 数据))
            return None

        except Exception as e:
            self.日志器.error(f"获取用户默认AI信息失败: {str(e)}")
            return None

    async def 更新用户AI基础信息(self, 用户ai信息id: int, 用户id: int, 员工名称: Optional[str] = None,
                             员工性格: Optional[str] = None, 店铺名称: Optional[str] = None) -> bool:
        """更新用户AI基础信息"""
        try:
            更新字段 = []
            更新值 = []

            if 员工名称:
                更新字段.append("员工名称 = %s")
                更新值.append(员工名称)
            if 员工性格:
                更新字段.append("员工性格 = %s")
                更新值.append(员工性格)
            if 店铺名称:
                更新字段.append("店铺名称 = %s")
                更新值.append(店铺名称)

            if not 更新字段:
                return True

            更新查询 = f"""
            UPDATE 用户ai信息表
            SET {', '.join(更新字段)}, 更新时间 = NOW()
            WHERE id = %s AND 用户ID = %s AND AI类型 = '默认'
            """
            更新值.extend([用户ai信息id, 用户id])

            async with self.连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    await 游标.execute(更新查询, 更新值)
                    await 连接.commit()
                    影响行数 = 游标.rowcount

            return 影响行数 > 0

        except Exception as e:
            self.日志器.error(f"更新用户AI基础信息失败: {str(e)}")
            return False

    async def 更新用户AI模型(self, 用户ai信息id: int, 用户id: int, 模型id: int) -> bool:
        """更新用户AI模型"""
        try:
            更新查询 = """
            UPDATE 用户ai信息表
            SET 模型ID = %s, 更新时间 = NOW()
            WHERE id = %s AND 用户ID = %s AND AI类型 = '默认'
            """

            async with self.连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    await 游标.execute(更新查询, (模型id, 用户ai信息id, 用户id))
                    await 连接.commit()
                    影响行数 = 游标.rowcount

            return 影响行数 > 0

        except Exception as e:
            self.日志器.error(f"更新用户AI模型失败: {str(e)}")
            return False

    async def 检查用户AI信息是否存在(self, 用户ai信息id: int) -> bool:
        """检查指定的用户AI信息ID是否存在"""
        try:
            查询 = "SELECT 1 FROM 用户ai信息表 WHERE id = %s LIMIT 1"
            结果 = await self.连接池.执行查询(查询, (用户ai信息id,))
            return len(结果) > 0
        except Exception as e:
            self.日志器.error(f"检查用户AI信息是否存在失败: {str(e)}")
            return False


class 异步AI模型数据操作:
    """异步AI模型数据操作类"""

    def __init__(self):
        self.连接池 = 异步连接池实例
        self.日志器 = 系统日志器

    async def 获取AI模型列表(self, 页码: int = 1, 每页数量: int = 10) -> Dict[str, Any]:
        """获取AI模型列表"""
        try:
            偏移量 = (页码 - 1) * 每页数量

            # 获取总数
            总数查询 = "SELECT COUNT(*) as total FROM ai模型表 WHERE 是否启用 = 1"
            总数结果 = await self.连接池.执行查询(总数查询)
            总数 = 总数结果[0]['total'] if 总数结果 else 0

            # 获取模型列表
            列表查询 = """
            SELECT
                id, 名称, 描述, 头像, coze模型id, 算力消耗,
                提示词模板, 是否启用, 排序权重, 创建时间
            FROM ai模型表
            WHERE 是否启用 = 1
            ORDER BY 排序权重 DESC, id ASC
            LIMIT %s OFFSET %s
            """

            列表结果 = await self.连接池.执行查询(列表查询, (每页数量, 偏移量))

            模型列表 = []
            for 模型 in 列表结果:
                if isinstance(模型, dict):
                    模型列表.append(模型)
                else:
                    字段名 = ['id', '名称', '描述', '头像', 'coze模型id', '算力消耗', '提示词模板', '是否启用', '排序权重', '创建时间']
                    模型列表.append(dict(zip(字段名, 模型)))

            return {
                "列表": 模型列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量
            }

        except Exception as e:
            self.日志器.error(f"获取AI模型列表失败: {str(e)}")
            return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量}

    async def 获取模型信息(self, 模型id: int) -> Optional[Dict[str, Any]]:
        """获取指定模型信息"""
        try:
            查询 = """
            SELECT id, 名称, 算力消耗, coze模型id, 描述, 头像
            FROM ai模型表
            WHERE id = %s AND 是否启用 = 1
            """

            结果 = await self.连接池.执行查询(查询, (模型id,))

            if 结果:
                模型 = 结果[0]
                if isinstance(模型, dict):
                    return 模型
                else:
                    字段名 = ['id', '名称', '算力消耗', 'coze模型id', '描述', '头像']
                    return dict(zip(字段名, 模型))
            return None

        except Exception as e:
            self.日志器.error(f"获取模型信息失败: {str(e)}")
            return None


# 创建全局实例
异步知识库数据实例 = 异步知识库数据操作()
异步用户AI数据实例 = 异步用户AI数据操作()
异步AI模型数据实例 = 异步AI模型数据操作()
