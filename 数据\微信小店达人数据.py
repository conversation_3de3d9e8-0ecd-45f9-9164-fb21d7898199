import json
from datetime import datetime
from typing import Dict, Any, List, Tuple

from pydantic import ValidationError

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.微信小店模型 import 微信小店达人写入模型, 微信达人公海筛选模型
from 日志.日志配置 import logger


async def 写入或更新微信小店达人(达人数据: 微信小店达人写入模型) -> Dict[str, Any]:
    """
    将微信小店达人信息写入数据库，并处理更新时无变化的场景。
    如果达人已存在（通过finderUsername判断），则更新信息；否则，插入新记录。

    Args:
        达人数据 (微信小店达人写入模型): 包含达人信息的Pydantic模型。

    Returns:
        Dict[str, Any]: 包含操作结果（插入、更新、无变化）和达人ID的字典。
    
    Raises:
        ValueError: 数据校验失败时抛出。
        IOError: 数据库操作失败时抛出。
    """
    try:
        async with 异步连接池实例.获取连接() as conn:
            async with conn.cursor() as cursor:
                # 检查达人是否已存在
                await cursor.execute(
                    "SELECT * FROM kol.微信达人表 WHERE finderUsername = %s", 
                    (达人数据.finderUsername,)
                )
                existing_talent_dict = await cursor.fetchone()

                # 准备要写入的数据，排除未设置的字段
                update_data = 达人数据.dict(exclude_unset=True)

                # 将列表转换为JSON字符串以便比较和存储
                for key in ['内容类型', '带货类目']:
                    if key in update_data and isinstance(update_data[key], list):
                        update_data[key] = json.dumps(update_data[key], ensure_ascii=False)

                if existing_talent_dict:
                    # 更新逻辑
                    talent_id = existing_talent_dict['id']
                    
                    # 检查是否有实际变化
                    has_changes = False
                    for key, value in update_data.items():
                        # 数据库返回的json是str，需要统一比较
                        existing_value = existing_talent_dict.get(key)
                        if isinstance(existing_value, list):
                             existing_value = json.dumps(existing_value, ensure_ascii=False)

                        if str(value) != str(existing_value):
                            has_changes = True
                            break
                    
                    if not has_changes:
                        logger.info(f"微信小店达人 '{达人数据.finderUsername}' 信息无变化，无需更新。")
                        return {"操作": "无变化", "id": talent_id}

                    # 有变化，执行更新
                    update_data['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    update_data.pop('finderUsername', None) # 不更新唯一标识

                    set_clause = ", ".join([f"`{key}` = %s" for key in update_data])
                    values = list(update_data.values())
                    values.append(talent_id)
                    
                    update_sql = f"UPDATE kol.微信达人表 SET {set_clause} WHERE id = %s"
                    
                    await cursor.execute(update_sql, tuple(values))
                    await conn.commit()
                    logger.info(f"成功更新微信小店达人: {达人数据.finderUsername}")
                    return {"操作": "更新", "id": talent_id}
                else:
                    # 插入新的达人记录
                    update_data['创建时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    update_data['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    columns = ", ".join([f"`{key}`" for key in update_data.keys()])
                    placeholders = ", ".join(["%s"] * len(update_data))
                    values = tuple(update_data.values())

                    insert_sql = f"INSERT INTO kol.微信达人表 ({columns}) VALUES ({placeholders})"
                    
                    await cursor.execute(insert_sql, values)
                    await conn.commit()
                    new_id = cursor.lastrowid
                    logger.info(f"成功插入新的微信小店达人: {达人数据.finderUsername}, ID: {new_id}")
                    return {"操作": "插入", "id": new_id}
    except ValidationError as e:
        logger.error(f"微信小店达人数据校验失败: {e}")
        raise ValueError(f"数据格式错误: {e}")
    except Exception as e:
        logger.error(f"写入或更新微信小店达人时发生数据库错误: {e}", exc_info=True)
        # 统一抛出IOError，由服务层处理
        raise IOError(f"数据库操作失败: {e}")


async def 根据finderUsername获取微信小店达人(finderUsername: str) -> Dict[str, Any] | None:
    """根据finderUsername从kol.微信达人表获取达人信息"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT * FROM kol.微信达人表 WHERE finderUsername = %s",
                (finderUsername,)
            )
            return await cursor.fetchone()


async def 获取或创建联系方式(联系方式: str, 类型: str) -> int:
    """
    在kol.联系方式表中获取或创建一条联系方式记录，并返回其ID。
    如果联系方式已存在，直接返回ID；否则，插入新记录再返回ID。
    """
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            # 检查联系方式是否存在
            await cursor.execute(
                "SELECT id FROM kol.联系方式表 WHERE 联系方式 = %s AND 类型 = %s",
                (联系方式, 类型)
            )
            record = await cursor.fetchone()
            if record:
                return record['id']

            # 不存在则插入新记录
            await cursor.execute(
                "INSERT INTO kol.联系方式表 (联系方式, 类型, 创建时间, 更新时间) VALUES (%s, %s, %s, %s)",
                (联系方式, 类型, datetime.now(), datetime.now())
            )
            await conn.commit()
            return cursor.lastrowid


async def 关联微信小店达人与联系方式(微信达人id: int, 联系方式id: int):
    """在kol.微信达人联系方式关联表中创建关联记录，如果关联已存在则不进行任何操作。"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            try:
                await cursor.execute(
                    "INSERT INTO kol.微信达人联系方式关联表 (微信达人id, 联系方式id) VALUES (%s, %s)",
                    (微信达人id, 联系方式id)
                )
                await conn.commit()
            except Exception:
                # 假设发生主键冲突（即关联已存在），则静默处理
                await conn.rollback()


async def 检查用户是否已认领微信小店达人(用户ID: int, 微信达人ID: int) -> bool:
    """在invite.用户达人关联表中检查用户是否已认领该达人"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT 1 FROM invite.用户达人关联表 WHERE 用户ID = %s AND 达人ID = %s AND 平台 = '微信' AND 状态 = 1",
                (用户ID, 微信达人ID)
            )
            return await cursor.fetchone() is not None


async def 为用户认领微信小店达人(用户ID: int, 微信达人ID: int):
    """在invite.用户达人关联表中为用户创建认领记录"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "INSERT INTO invite.用户达人关联表 (用户ID, 达人ID, 平台, 认领时间, 状态) VALUES (%s, %s, '微信', %s, 1)",
                (用户ID, 微信达人ID, datetime.now())
            )
            await conn.commit()


async def 获取微信小店达人公海列表(筛选条件: 微信达人公海筛选模型) -> List[Dict[str, Any]]:
    """
    高效获取未被认领的微信小店达人列表（公海），并支持多种筛选条件。
    使用keyset分页（基于ID）以提高性能。
    """
    # PostgreSQL连接池导入
    from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            # 基础查询语句
            query_parts = [
                "SELECT t1.id, t1.昵称, t1.内容类型, t1.带货类目, t1.粉丝数文本, t1.GMV文本, t1.有无联系方式",
                "FROM kol.微信达人表 t1",
                "LEFT JOIN invite.用户达人关联表 t2 ON t1.id = t2.达人ID AND t2.平台 = '微信' AND t2.状态 = 1",
                "WHERE t2.达人ID IS NULL"
            ]
            params = []

            # --- 动态构建筛选条件 ---
            if 筛选条件.昵称:
                query_parts.append("AND t1.昵称 LIKE %s")
                params.append(f"%{筛选条件.昵称}%")
            
            # 高效筛选：使用JSON_SEARCH在指定的路径下查找值
            if 筛选条件.内容类型:
                query_parts.append("AND JSON_SEARCH(t1.内容类型, 'one', %s, NULL, '$[*].name') IS NOT NULL")
                params.append(筛选条件.内容类型)

            if 筛选条件.带货类目:
                query_parts.append("AND JSON_SEARCH(t1.带货类目, 'one', %s, NULL, '$[*].topCatName') IS NOT NULL")
                params.append(筛选条件.带货类目)

            if 筛选条件.粉丝数文本:
                query_parts.append("AND t1.粉丝数文本 = %s")
                params.append(筛选条件.粉丝数文本)

            if 筛选条件.GMV文本:
                query_parts.append("AND t1.GMV文本 = %s")
                params.append(筛选条件.GMV文本)

            if 筛选条件.有无联系方式 is not None:
                query_parts.append("AND t1.有无联系方式 = %s")
                params.append(1 if 筛选条件.有无联系方式 else 0)

            # --- 分页逻辑 ---
            if 筛选条件.lastId:
                query_parts.append("AND t1.id > %s")
                params.append(筛选条件.lastId)
            
            query_parts.append("ORDER BY t1.id ASC LIMIT %s")
            params.append(筛选条件.每页条数)

            # 组合并执行查询
            final_query = " ".join(query_parts)
            await cursor.execute(final_query, tuple(params))
            records = await cursor.fetchall()

            # 将JSON字符串字段转换为Python对象
            for record in records:
                if record.get('内容类型'):
                    try:
                        record['内容类型'] = json.loads(record['内容类型'])
                    except (json.JSONDecodeError, TypeError):
                        record['内容类型'] = [] # 或者其他默认值
                if record.get('带货类目'):
                    try:
                        record['带货类目'] = json.loads(record['带货类目'])
                    except (json.JSONDecodeError, TypeError):
                        record['带货类目'] = [] # 或者其他默认值

            return records


async def 获取用户认领的微信小店达人列表(用户ID: int, 页码: int, 每页条数: int) -> Tuple[List[Dict[str, Any]], int]:
    """获取指定用户已认领的微信小店达人列表"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            # 计算总数
            await cursor.execute(
                "SELECT COUNT(*) FROM invite.用户达人关联表 WHERE 用户ID = %s AND 平台 = '微信' AND 状态 = 1",
                (用户ID,)
            )
            total = (await cursor.fetchone())['COUNT(*)']

            # 获取分页数据
            offset = (页码 - 1) * 每页条数
            await cursor.execute("""
                SELECT t1.*
                FROM kol.微信达人表 t1
                JOIN invite.用户达人关联表 t2 ON t1.id = t2.达人ID
                WHERE t2.用户ID = %s AND t2.平台 = '微信' AND t2.状态 = 1
                LIMIT %s OFFSET %s
            """, (用户ID, 每页条数, offset))
            records = await cursor.fetchall()
            return records, total


async def 根据id获取微信小店达人(id: int) -> Dict[str, Any] | None:
    """根据id从kol.微信达人表获取达人信息"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "SELECT * FROM kol.微信达人表 WHERE id = %s",
                (id,)
            )
            return await cursor.fetchone()


async def 获取微信小店达人关联的联系方式(微信达人id: int) -> List[Dict[str, Any]]:
    """获取达人所有关联的联系方式"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute("""
                SELECT c.类型, c.联系方式
                FROM kol.联系方式表 c
                JOIN kol.微信达人联系方式关联表 r ON c.id = r.联系方式id
                WHERE r.微信达人id = %s
            """, (微信达人id,))
            return await cursor.fetchall()


async def 取消认领微信小店达人(用户ID: int, 微信达人ID: int) -> int:
    """从invite.用户达人关联表中更新用户的认领记录状态"""
    async with 异步连接池实例.获取连接() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(
                "UPDATE invite.用户达人关联表 SET 状态 = 0 WHERE 用户ID = %s AND 达人ID = %s AND 平台 = '微信' AND 状态 = 1",
                (用户ID, 微信达人ID)
            )
            await conn.commit()
            return cursor.rowcount
 