"""
异步产品知识库服务
负责处理产品提交到知识库的业务逻辑，包括调用Coze API
"""

from typing import Optional, Dict, Any
import base64
from AI.COZE import Coze客户端
from cozepy import DocumentBase, DocumentSourceInfo, DocumentChunkStrategy

# PostgreSQL数据操作导入
from 数据.LangChain数据操作 import (
    创建智能体配置,
    获取智能体配置,
    删除智能体配置
)

# 保留部分MySQL函数（暂时兼容，后续逐步迁移）
from 数据.异步知识库数据 import 异步知识库数据实例

from 日志 import 系统日志器


class 异步产品知识库服务:
    """异步产品知识库服务类 - 负责产品与知识库的业务逻辑，包括调用Coze API"""

    def __init__(self):
        self.coze客户端 = Coze客户端()
        self.知识库数据实例 = 异步知识库数据实例
        self.日志器 = 系统日志器

    async def 提交产品到coze知识库(self, 产品id: int, 知识库表id: int, 文档名称: str) -> bool:
        """
        完整的产品提交到Coze知识库流程

        参数:
            产品id: 产品ID
            知识库表id: 知识库表ID（数据库中的知识库记录ID）
            文档名称: 文档名称

        返回:
            提交成功返回True，失败返回False
        """
        try:
            # 1. 从数据库获取知识库信息
            知识库信息 = await self.知识库数据实例.获取知识库信息(知识库表id)
            if not 知识库信息:
                self.日志器.error(f"数据库中知识库不存在: {知识库表id}")
                return False

            coze知识库id = 知识库信息["coze知识库id"]

            # 2. 从数据库获取产品信息
            产品信息 = await self.知识库数据实例.获取产品信息(产品id)
            if not 产品信息:
                self.日志器.error(f"数据库中产品不存在: {产品id}")
                return False

            # 3. 准备文档内容
            文档内容 = self._生成产品文档内容(产品信息, 产品id)

            # 4. 通过Coze API创建文档
            coze文档id = await self._通过coze接口创建文档(coze知识库id, 文档名称, 文档内容)
            if not coze文档id:
                return False

            # 5. 保存文档记录到数据库
            保存成功 = await self.知识库数据实例.保存知识库文档记录(
                coze文档id, 文档名称, 产品id, 知识库表id
            )

            if 保存成功:
                self.日志器.info(f"产品提交到Coze知识库成功: 产品ID={产品id}, 知识库ID={知识库表id}, Coze文档ID={coze文档id}")
                return True
            else:
                # 如果数据库保存失败，应该考虑删除已创建的Coze文档
                self.日志器.error(f"数据库保存失败，但Coze文档已创建: {coze文档id}")
                return False

        except Exception as e:
            self.日志器.error(f"提交产品到Coze知识库失败: {str(e)}")
            return False
    
    def _生成产品文档内容(self, 产品信息: Dict[str, Any], 产品id: int) -> str:
        """生成产品文档内容"""
        产品名称 = 产品信息.get("产品名称", "")
        产品描述 = 产品信息.get("产品描述", "")
        产品分类 = 产品信息.get("产品分类", "")
        
        文档内容 = f"""产品名称: {产品名称}

产品分类: {产品分类}

产品描述: {产品描述}

产品ID: {产品id}

这是一个来自用户产品库的产品信息，可以用于AI助手回答相关问题。"""
        
        return 文档内容
    
    async def _通过coze接口创建文档(self, coze知识库id: str, 文档名称: str, 文档内容: str) -> Optional[str]:
        """通过Coze API创建文档"""
        try:
            # 将文档内容转换为base64
            文档内容_base64 = base64.b64encode(文档内容.encode('utf-8')).decode('utf-8')
            
            # 创建文档数据
            文档数据 = DocumentBase(
                name=文档名称,
                source_info=DocumentSourceInfo(
                    file_type="txt",
                    file_base64=文档内容_base64
                )
            )
            
            # 创建分块策略
            区块策略 = DocumentChunkStrategy(chunk_type=0)
            
            # 使用增强版文档创建方法
            创建结果 = await self.coze客户端.创建知识库文档_增强版(
                知识库ID=coze知识库id,
                文档列表=[文档数据],
                分块策略=区块策略
            )
            
            # 检查创建结果
            if "错误" in 创建结果:
                self.日志器.error(f"Coze API创建文档失败: {创建结果['错误']}")
                return None

            if not 创建结果.get("成功") or not 创建结果.get("文档列表"):
                self.日志器.error(f"Coze API创建文档失败: {创建结果}")
                return None

            # 获取Coze文档ID
            文档信息 = 创建结果["文档列表"][0]
            coze文档id = 文档信息.get("文档ID")
            if not coze文档id:
                self.日志器.error("未获取到Coze文档ID")
                return None
            
            self.日志器.info(f"Coze文档创建成功: 文档ID={coze文档id}, 知识库ID={coze知识库id}")
            return coze文档id
            
        except Exception as e:
            self.日志器.error(f"创建Coze文档失败: {str(e)}")
            return None


# 创建全局实例
异步产品知识库服务实例 = 异步产品知识库服务()
