"""
LangGraph智能体核心
现代化的智能体实现，替换传统LCEL链
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import uuid4

from 服务.LangGraph_状态定义 import 智能体状态, 创建初始状态, 更新状态消息, 添加工具调用记录, 更新执行步骤, 添加RAG上下文
from 服务.LangGraph_数据库检查点 import MySQL检查点管理器实例
from 数据.LangChain_状态持久化数据层 import LangChain状态持久化数据层实例, LangChain状态持久化数据层

# 尝试导入LangGraph组件
try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolNode, tools_condition
    from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
    from langchain_core.tools import tool
    from langchain_core.runnables import RunnableConfig
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    # 提供兼容性定义
    class StateGraph:
        def __init__(self, state_schema):
            self.state_schema = state_schema
            self.nodes = {}
            self.edges = {}
            self.entry_point = None
        
        def add_node(self, name, func):
            self.nodes[name] = func
        
        def add_edge(self, from_node, to_node):
            if from_node not in self.edges:
                self.edges[from_node] = []
            self.edges[from_node].append(to_node)
        
        def add_conditional_edges(self, from_node, condition_func):
            self.edges[from_node] = condition_func
        
        def set_entry_point(self, node):
            self.entry_point = node
        
        def compile(self, checkpointer=None):
            return LangGraph兼容执行器(self, checkpointer)
    
    class ToolNode:
        def __init__(self, tools):
            self.tools = tools
    
    def tools_condition(state):
        return "tools" if state.get("tool_calls") else END
    
    END = "__end__"
    
    class HumanMessage:
        def __init__(self, content):
            self.content = content
            self.type = "human"
    
    class AIMessage:
        def __init__(self, content, tool_calls=None):
            self.content = content
            self.tool_calls = tool_calls or []
            self.type = "ai"
    
    class ToolMessage:
        def __init__(self, content, tool_call_id):
            self.content = content
            self.tool_call_id = tool_call_id
            self.type = "tool"
    
    def tool(func):
        return func
    
    class RunnableConfig:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

# 日志配置
智能体核心日志器 = logging.getLogger("LangGraph智能体核心")


class LangGraph兼容执行器:
    """LangGraph兼容执行器（用于非LangGraph环境）"""
    
    def __init__(self, graph, checkpointer=None):
        self.graph = graph
        self.checkpointer = checkpointer
        智能体核心日志器.info("LangGraph兼容执行器初始化")
    
    async def ainvoke(self, input_data, config=None):
        """异步调用执行器"""
        try:
            # 简化的执行逻辑
            if "messages" in input_data:
                return {"messages": input_data["messages"] + [AIMessage("兼容模式响应")]}
            return {"response": "兼容模式响应"}
        except Exception as e:
            智能体核心日志器.error(f"兼容执行器调用失败: {str(e)}")
            return {"error": str(e)}
    
    def invoke(self, input_data, config=None):
        """同步调用执行器"""
        return asyncio.run(self.ainvoke(input_data, config))
    
    async def astream(self, input_data, config=None):
        """异步流式调用"""
        result = await self.ainvoke(input_data, config)
        yield result


class LangGraph智能体核心:
    """现代化的LangGraph智能体核心 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于智能体核心逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 智能体配置, 模型管理器, RAG引擎, 工具管理器,
                 数据层: Optional[LangChain状态持久化数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            智能体配置: 智能体配置对象
            模型管理器: 模型管理器实例
            RAG引擎: RAG引擎实例
            工具管理器: 工具管理器实例
            数据层: 状态持久化数据层实例，如果为None则使用默认实例
        """
        self.配置 = 智能体配置
        self.模型管理器 = 模型管理器
        self.RAG引擎 = RAG引擎
        self.工具管理器 = 工具管理器

        # 依赖注入 - 确保数据层永远不为None
        self.数据层: LangChain状态持久化数据层 = 数据层 or LangChain状态持久化数据层实例
        self.检查点管理器 = MySQL检查点管理器实例
        self.已初始化 = True  # 简化初始化逻辑

        # 图和执行器
        self.图构建器 = None
        self.智能体图 = None

        智能体核心日志器.info(f"LangGraph智能体核心创建成功: {智能体配置.智能体名称}")

    @classmethod
    async def 创建实例(cls, 智能体配置, 模型管理器, RAG引擎, 工具管理器) -> "LangGraph智能体核心":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保数据层已初始化
        if not LangChain状态持久化数据层实例.已初始化:
            await LangChain状态持久化数据层实例.初始化()

        # 创建核心实例
        实例 = cls(智能体配置, 模型管理器, RAG引擎, 工具管理器, LangChain状态持久化数据层实例)

        # 执行异步初始化逻辑
        await 实例._异步初始化()

        return 实例

    async def _异步初始化(self):
        """内部异步初始化方法"""
        try:
            # 初始化检查点管理器
            await self.检查点管理器.初始化()

            # 构建智能体图
            await self._构建智能体图()

            智能体核心日志器.info(f"✅ LangGraph智能体核心异步初始化成功: {self.配置.智能体名称}")

        except Exception as e:
            智能体核心日志器.error(f"❌ LangGraph智能体核心异步初始化失败: {str(e)}")
            raise
    
    async def _构建智能体图(self):
        """构建LangGraph智能体图"""
        try:
            # 创建状态图
            self.图构建器 = StateGraph(智能体状态)
            
            # 添加节点
            self.图构建器.add_node("query_or_respond", self._查询或响应节点)
            self.图构建器.add_node("retrieve", self._检索节点)
            self.图构建器.add_node("tools", self._工具节点)
            self.图构建器.add_node("generate", self._生成节点)
            
            # 添加边
            self.图构建器.add_conditional_edges(
                "query_or_respond",
                self._路由条件,
                {
                    "retrieve": "retrieve",
                    "tools": "tools", 
                    "generate": "generate",
                    "end": END
                }
            )
            
            self.图构建器.add_edge("retrieve", "generate")
            self.图构建器.add_edge("tools", "generate")
            self.图构建器.add_edge("generate", END)
            
            # 设置入口点
            self.图构建器.set_entry_point("query_or_respond")
            
            # 编译图
            self.智能体图 = self.图构建器.compile(checkpointer=self.检查点管理器)
            
            智能体核心日志器.info("✅ LangGraph智能体图构建完成")
            
        except Exception as e:
            智能体核心日志器.error(f"❌ 构建智能体图失败: {str(e)}")
            raise
    
    async def _查询或响应节点(self, 状态: 智能体状态) -> Dict[str, Any]:
        """查询或响应节点 - 决定下一步行动"""
        try:
            更新执行步骤(状态, "query_or_respond")
            
            # 获取最新消息
            if not 状态["messages"]:
                return {"current_step": "generate"}
            
            最新消息 = 状态["messages"][-1]
            
            # 分析是否需要工具调用或检索
            if hasattr(最新消息, "content"):
                消息内容 = 最新消息.content
            else:
                消息内容 = str(最新消息)
            
            # 简单的意图识别
            if any(关键词 in 消息内容.lower() for 关键词 in ["搜索", "查找", "检索", "search", "find"]):
                状态["current_step"] = "retrieve"
            elif any(关键词 in 消息内容.lower() for 关键词 in ["工具", "调用", "执行", "tool", "call"]):
                状态["current_step"] = "tools"
            else:
                状态["current_step"] = "generate"
            
            智能体核心日志器.debug(f"查询或响应节点: 下一步 -> {状态['current_step']}")
            return 状态
            
        except Exception as e:
            智能体核心日志器.error(f"查询或响应节点失败: {str(e)}")
            状态["current_step"] = "generate"
            return 状态
    
    async def _检索节点(self, 状态: 智能体状态) -> Dict[str, Any]:
        """检索节点 - 执行RAG检索"""
        try:
            更新执行步骤(状态, "retrieve")
            
            if not 状态["messages"]:
                return 状态
            
            # 获取查询内容
            最新消息 = 状态["messages"][-1]
            if hasattr(最新消息, "content"):
                查询内容 = 最新消息.content
            else:
                查询内容 = str(最新消息)
            
            # 执行RAG检索
            if self.RAG引擎 and hasattr(self.RAG引擎, "检索"):
                检索结果 = await self.RAG引擎.检索(查询内容, k=5)
                添加RAG上下文(状态, 检索结果)
                智能体核心日志器.debug(f"RAG检索完成: {len(检索结果)} 个文档")
            
            状态["current_step"] = "generate"
            return 状态
            
        except Exception as e:
            智能体核心日志器.error(f"检索节点失败: {str(e)}")
            状态["current_step"] = "generate"
            return 状态
    
    async def _工具节点(self, 状态: 智能体状态) -> Dict[str, Any]:
        """工具节点 - 执行工具调用"""
        try:
            更新执行步骤(状态, "tools")
            
            # 这里可以集成实际的工具调用逻辑
            # 暂时返回模拟结果
            工具调用记录 = {
                "tool_name": "示例工具",
                "tool_args": {},
                "tool_result": "工具执行结果",
                "status": "success",
                "start_time": datetime.now(),
                "end_time": datetime.now()
            }
            
            添加工具调用记录(状态, 工具调用记录)
            智能体核心日志器.debug("工具调用完成")
            
            状态["current_step"] = "generate"
            return 状态
            
        except Exception as e:
            智能体核心日志器.error(f"工具节点失败: {str(e)}")
            状态["current_step"] = "generate"
            return 状态
    
    async def _生成节点(self, 状态: 智能体状态) -> Dict[str, Any]:
        """生成节点 - 生成最终响应"""
        try:
            更新执行步骤(状态, "generate")
            
            # 构建提示词
            提示词内容 = self._构建提示词(状态)
            
            # 调用模型生成响应
            if self.模型管理器 and hasattr(self.模型管理器, "调用模型"):
                模型响应 = await self.模型管理器.调用模型(
                    self.配置.模型名称,
                    [{"role": "user", "content": 提示词内容}],
                    temperature=self.配置.温度参数
                )
                
                if 模型响应.get("success"):
                    响应内容 = 模型响应.get("response", "抱歉，生成响应失败")
                else:
                    响应内容 = "抱歉，模型调用失败"
            else:
                响应内容 = "LangGraph智能体响应（模型管理器不可用）"
            
            # 添加AI消息到状态
            ai_消息 = AIMessage(content=响应内容)
            更新状态消息(状态, ai_消息)
            
            智能体核心日志器.debug("生成节点完成")
            return 状态
            
        except Exception as e:
            智能体核心日志器.error(f"生成节点失败: {str(e)}")
            # 添加错误响应
            错误消息 = AIMessage(content=f"抱歉，生成响应时发生错误: {str(e)}")
            更新状态消息(状态, 错误消息)
            return 状态
    
    def _路由条件(self, 状态: 智能体状态) -> str:
        """路由条件 - 决定下一个节点"""
        当前步骤 = 状态.get("current_step", "generate")
        
        if 当前步骤 == "retrieve":
            return "retrieve"
        elif 当前步骤 == "tools":
            return "tools"
        elif 当前步骤 == "generate":
            return "generate"
        else:
            return "end"
    
    def _构建提示词(self, 状态: 智能体状态) -> str:
        """构建提示词"""
        try:
            提示词部分 = []
            
            # 系统提示词
            if hasattr(self.配置, "系统提示词") and self.配置.系统提示词:
                提示词部分.append(f"系统指令: {self.配置.系统提示词}")
            
            # RAG上下文
            if 状态["context"]:
                上下文内容 = "\n".join([
                    f"文档{i+1}: {doc.page_content if hasattr(doc, 'page_content') else str(doc)}"
                    for i, doc in enumerate(状态["context"][:3])  # 限制上下文数量
                ])
                提示词部分.append(f"相关上下文:\n{上下文内容}")
            
            # 对话历史
            if 状态["messages"]:
                最新消息 = 状态["messages"][-1]
                if hasattr(最新消息, "content"):
                    提示词部分.append(f"用户问题: {最新消息.content}")
                else:
                    提示词部分.append(f"用户问题: {str(最新消息)}")
            
            return "\n\n".join(提示词部分)

        except Exception as e:
            智能体核心日志器.error(f"构建提示词失败: {str(e)}")
            return "请回答用户的问题。"

    async def 对话(self, 用户输入: str, 用户ID: int, 会话ID: str = None, 线程ID: str = None) -> Dict[str, Any]:
        """处理用户对话"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 生成会话和线程ID
            if not 会话ID:
                会话ID = f"session_{uuid4().hex[:16]}"
            if not 线程ID:
                线程ID = f"thread_{uuid4().hex[:16]}"

            # 创建或获取对话线程
            线程信息 = await self.数据层.获取对话线程(线程ID)
            if not 线程信息:
                await self.数据层.创建对话线程(用户ID, self.配置.智能体id, f"对话_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

            # 创建初始状态
            状态 = 创建初始状态(用户ID, self.配置.智能体id, 会话ID, 线程ID)

            # 添加用户消息
            用户消息 = HumanMessage(content=用户输入)
            更新状态消息(状态, 用户消息)

            # 配置
            配置 = RunnableConfig(
                configurable={
                    'thread_id': 线程ID,
                    'user_id': 用户ID,
                    'agent_id': self.配置.智能体id
                }
            )

            # 执行智能体图
            开始时间 = time.time()

            if LANGGRAPH_AVAILABLE and self.智能体图:
                # 使用真实的LangGraph
                结果 = await self.智能体图.ainvoke(
                    {"messages": [用户消息]},
                    config=配置
                )
            else:
                # 使用兼容模式
                结果 = await self._兼容模式执行(状态)

            处理时长 = time.time() - 开始时间

            # 提取响应
            if "messages" in 结果 and 结果["messages"]:
                最后消息 = 结果["messages"][-1]
                if hasattr(最后消息, "content"):
                    响应内容 = 最后消息.content
                else:
                    响应内容 = str(最后消息)
            else:
                响应内容 = "抱歉，处理请求时发生错误"

            # 更新统计
            await self.数据层.更新对话统计(
                线程ID=线程ID,
                智能体ID=self.配置.智能体id,
                消息数增量=2,  # 用户消息 + AI响应
                响应时间=处理时长
            )

            智能体核心日志器.info(f"✅ LangGraph对话完成: {处理时长:.2f}s")

            return {
                "success": True,
                "response": 响应内容,
                "session_id": 会话ID,
                "thread_id": 线程ID,
                "processing_time": 处理时长,
                "model_name": self.配置.模型名称,
                "agent_type": "langgraph",
                "langgraph_available": LANGGRAPH_AVAILABLE
            }

        except Exception as e:
            智能体核心日志器.error(f"❌ LangGraph对话失败: {str(e)}")
            return {
                "success": False,
                "response": f"抱歉，处理请求时发生错误: {str(e)}",
                "error": str(e),
                "agent_type": "langgraph",
                "langgraph_available": LANGGRAPH_AVAILABLE
            }

    async def _兼容模式执行(self, 状态: 智能体状态) -> Dict[str, Any]:
        """兼容模式执行（当LangGraph不可用时）"""
        try:
            智能体核心日志器.info("使用兼容模式执行")

            # 依次执行各个节点
            状态 = await self._查询或响应节点(状态)

            if 状态["current_step"] == "retrieve":
                状态 = await self._检索节点(状态)
            elif 状态["current_step"] == "tools":
                状态 = await self._工具节点(状态)

            状态 = await self._生成节点(状态)

            return {"messages": 状态["messages"]}

        except Exception as e:
            智能体核心日志器.error(f"兼容模式执行失败: {str(e)}")
            return {"messages": [AIMessage(content=f"兼容模式执行失败: {str(e)}")]}


# 创建全局实例工厂
def 创建LangGraph智能体核心(智能体配置, 模型管理器, RAG引擎, 工具管理器):
    """创建LangGraph智能体核心实例"""
    return LangGraph智能体核心(智能体配置, 模型管理器, RAG引擎, 工具管理器)
