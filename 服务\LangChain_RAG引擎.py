"""
LangChain RAG引擎

功能：
1. 文档加载和处理
2. 向量存储管理
3. 检索增强生成
4. 知识库集成
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# LangChain核心组件
try:
    from langchain.text_splitter import RecursiveCharacterTextSplitter  # type: ignore
    from langchain_chroma import Chroma as ChromaDB  # type: ignore
    from langchain_community.document_loaders import (  # type: ignore
        CSVLoader,  # type: ignore
        TextLoader,  # type: ignore
        UnstructuredExcelLoader,  # type: ignore
        UnstructuredWordDocumentLoader,  # type: ignore
    )
    from langchain_community.vectorstores import FAISS, Chroma  # type: ignore

    try:
        from langchain_community.document_loaders import (
            PyPDFLoader as PDFLoader,  # type: ignore
        )
    except ImportError:
        from langchain_community.document_loaders import (
            UnstructuredPDFLoader as PDFLoader,  # type: ignore
        )

    import chromadb  # type: ignore
    from langchain_core.documents import Document  # type: ignore
    from langchain_core.vectorstores import InMemoryVectorStore  # type: ignore

    LANGCHAIN_AVAILABLE = True
    CHROMA_AVAILABLE = True
except ImportError as e:
    print(f"LangChain导入失败: {e}")
    LANGCHAIN_AVAILABLE = False
    CHROMA_AVAILABLE = False

    # 创建占位符类
    class Document:
        pass

    class FAISS:
        pass

    class Chroma:
        pass

    class InMemoryVectorStore:
        pass

    class OpenAIEmbeddings:
        pass

    class HuggingFaceEmbeddings:
        pass

    class RecursiveCharacterTextSplitter:
        pass

    class TextLoader:
        pass

    class PDFLoader:
        pass

    class UnstructuredWordDocumentLoader:
        pass

    class UnstructuredExcelLoader:
        pass

    class CSVLoader:
        pass


# 配置日志
RAG日志器 = logging.getLogger("LangChain.RAG引擎")


class LangChainRAG引擎:
    """LangChain RAG引擎 - 支持Chroma向量数据库和云托管服务"""

    def __init__(self):
        self.向量存储 = None
        self.嵌入模型 = None
        self.文档分割器 = None
        self.已初始化 = False
        self.知识库路径 = "知识库"
        self.当前智能体id = None  # 记录当前初始化的智能体ID

        # Chroma配置 - 支持环境隔离
        self.chroma_客户端 = None
        self.chroma_集合名称 = None
        self.chroma_持久化路径 = self._获取Chroma持久化路径()

        # 向量存储配置 - 专注于ChromaDB
        self.向量存储类型 = "chroma"  # 统一使用ChromaDB

    def _获取Chroma持久化路径(self) -> str:
        """根据环境变量获取Chroma持久化路径"""
        import os

        # 获取环境标识
        环境标识 = os.getenv("ENVIRONMENT", "development")

        if 环境标识 == "production":
            # 生产环境使用固定路径
            return os.getenv("CHROMA_DB_PATH", "/data/chroma_db")
        else:
            # 开发环境使用用户特定路径，避免冲突
            用户标识 = os.getenv("USER", os.getenv("USERNAME", "local"))
            return os.getenv("CHROMA_DB_PATH", f"./chroma_db_dev_{用户标识}")

    async def 初始化(
        self,
        智能体id: Optional[int] = None,
        知识库列表: Optional[List[str]] = None,
        嵌入模型类型: str = "auto",
    ):
        """初始化RAG引擎 - 使用ChromaDB向量存储"""
        try:
            # 记录当前智能体ID
            self.当前智能体id = 智能体id

            if not LANGCHAIN_AVAILABLE:
                RAG日志器.warning("LangChain不可用，使用简化模式")
                self.已初始化 = True
                return True

            # 初始化嵌入模型 - 优先使用智能体关联知识库的嵌入模型
            await self._初始化智能嵌入模型(嵌入模型类型, 智能体id)

            # 初始化文档分割器
            await self._初始化文档分割器()

            # 初始化ChromaDB向量存储
            await self._初始化Chroma向量存储()

            # 优先从数据库加载智能体关联的知识库
            if 智能体id:
                await self._加载智能体关联知识库(智能体id)
            # 兼容原有的文件系统方式
            elif 知识库列表:
                await self._加载知识库(知识库列表)

            self.已初始化 = True
            RAG日志器.info(f"RAG引擎初始化成功 - 向量存储类型: {self.向量存储类型}")
            return True

        except Exception as e:
            RAG日志器.error(f"RAG引擎初始化失败: {str(e)}")
            return False

    async def _初始化智能嵌入模型(
        self, 嵌入模型类型: str = "auto", 智能体id: Optional[int] = None
    ):
        """智能初始化嵌入模型 - 优先使用智能体关联知识库的嵌入模型"""
        try:
            RAG日志器.info(f"🔧 开始初始化嵌入模型 - 智能体ID: {智能体id}")

            # 如果有智能体ID，优先获取其关联知识库的嵌入模型（复用缓存）
            if 智能体id:
                try:
                    # 🔧 关键修复：每次都重新获取最新的知识库信息，确保嵌入模型一致性
                    self._知识库信息_缓存 = await self._获取智能体关联知识库信息(
                        智能体id
                    )

                    智能体嵌入模型 = self._知识库信息_缓存.get("嵌入模型")
                    if 智能体嵌入模型:
                        嵌入模型实例 = await self._创建数据库嵌入模型(智能体嵌入模型)
                        if 嵌入模型实例:
                            self.嵌入模型 = 嵌入模型实例
                            RAG日志器.info(
                                f"✅ 使用智能体关联知识库嵌入模型: {智能体嵌入模型['显示名称']} (ID: {智能体嵌入模型['id']})"
                            )
                            return
                        else:
                            RAG日志器.warning(
                                f"⚠️ 嵌入模型实例创建失败: {智能体嵌入模型['显示名称']}"
                            )
                    else:
                        RAG日志器.info(f"ℹ️ 智能体 {智能体id} 关联知识库未指定嵌入模型")
                except Exception as e:
                    RAG日志器.warning(f"⚠️ 获取智能体关联知识库嵌入模型失败: {str(e)}")

            # 备用方案：从数据库获取启用的嵌入模型配置
            RAG日志器.info("📝 使用备用方案：从数据库获取嵌入模型配置...")

            try:
                # 获取启用的嵌入模型配置
                嵌入模型配置 = await self._获取数据库嵌入模型配置()

                if 嵌入模型配置:
                    # 使用数据库配置创建嵌入模型
                    嵌入模型实例 = await self._创建数据库嵌入模型(嵌入模型配置)
                    if 嵌入模型实例:
                        self.嵌入模型 = 嵌入模型实例
                        RAG日志器.info(
                            f"✅ 使用数据库嵌入模型成功: {嵌入模型配置['模型名称']}"
                        )
                        return
                    else:
                        RAG日志器.warning("数据库嵌入模型创建失败")
                else:
                    RAG日志器.info("数据库中没有启用的嵌入模型")

            except Exception as db_error:
                RAG日志器.warning(f"数据库嵌入模型初始化失败: {str(db_error)}")

            # 备用方案：使用模型管理器
            try:
                from 服务.LangChain_模型管理器 import LangChain模型管理器实例

                RAG日志器.info("尝试使用模型管理器中的向量模型...")
                向量模型 = await LangChain模型管理器实例.获取向量模型()

                if 向量模型:
                    self.嵌入模型 = 向量模型
                    RAG日志器.info("✅ 使用模型管理器中的向量模型成功")
                    return
                else:
                    RAG日志器.info("模型管理器中没有可用向量模型")

            except Exception as vm_error:
                RAG日志器.warning(f"模型管理器向量模型初始化失败: {str(vm_error)}")

            # 仅尝试OpenAI嵌入模型（如果有API Key）
            try:
                import os

                if os.getenv("OPENAI_API_KEY"):
                    RAG日志器.info("检测到OpenAI API Key，尝试初始化OpenAI嵌入模型...")
                    self.嵌入模型 = OpenAIEmbeddings()
                    # 测试模型是否可用
                    test_text = "测试文本"
                    _ = self.嵌入模型.embed_query(test_text)
                    RAG日志器.info("✅ OpenAI嵌入模型初始化成功")
                    return
                else:
                    RAG日志器.info("未检测到OPENAI_API_KEY环境变量，跳过OpenAI嵌入模型")
            except Exception as openai_error:
                RAG日志器.warning(f"OpenAI嵌入模型初始化失败: {str(openai_error)}")

            # 所有嵌入模型初始化都失败
            self.嵌入模型 = None
            RAG日志器.error("所有嵌入模型初始化都失败，向量化功能不可用")

        except Exception as e:
            RAG日志器.error(f"嵌入模型初始化失败: {str(e)}")
            self.嵌入模型 = None

    def 获取嵌入模型批处理大小(self) -> int:
        """获取当前嵌入模型的批处理大小限制"""
        if not self.嵌入模型:
            return 1

        # 根据嵌入模型类型返回相应的批处理大小限制
        模型类名 = self.嵌入模型.__class__.__name__

        if "DashScope" in 模型类名:
            # 阿里云DashScope嵌入模型批处理大小限制为10
            return 10
        elif "OpenAI" in 模型类名:
            # OpenAI嵌入模型批处理大小限制较大
            return 100
        elif "HuggingFace" in 模型类名:
            # HuggingFace本地模型通常没有严格限制
            return 50
        else:
            # 默认保守的批处理大小
            return 10

    async def _获取数据库嵌入模型配置(
        self, 知识库ID: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """从数据库获取嵌入模型配置，优先使用知识库指定的模型"""
        try:
            # 如果指定了知识库ID，先尝试获取知识库配置的嵌入模型
            if 知识库ID:
                # 直接从知识库表的嵌入模型字段获取模型ID
                知识库查询SQL = """
                SELECT 嵌入模型 FROM langchain_知识库表
                WHERE id = %s AND 知识库状态 = '活跃'
                """
                知识库结果 = await 异步连接池实例.执行查询(
                    知识库查询SQL, (知识库ID,)
                )

                if 知识库结果:
                    知识库数据 = 知识库结果[0]
                    嵌入模型ID = 知识库数据.get("嵌入模型")

                    # 使用知识库表的嵌入模型字段
                    if 嵌入模型ID:
                        # 获取指定的嵌入模型配置
                        模型查询SQL = """
                        SELECT id, 模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL, 模型参数, 启用状态
                        FROM langchain_模型配置表
                        WHERE id = %s AND 模型类型 LIKE '%embedding%' AND 启用状态 = 1
                        """
                        模型结果 = await 异步连接池实例.执行查询(
                            模型查询SQL, (嵌入模型ID,)
                        )

                        if 模型结果:
                            配置 = 模型结果[0]
                            RAG日志器.info(
                                f"使用知识库指定的嵌入模型: {配置['显示名称']} (ID: {嵌入模型ID})"
                            )
                            return 配置
                        else:
                            RAG日志器.warning(
                                f"知识库指定的嵌入模型不存在或已禁用 (ID: {嵌入模型ID})"
                            )
                    else:
                        RAG日志器.info("知识库未指定嵌入模型，将使用默认模型")

            # 如果没有指定模型或指定的模型不可用，使用默认模型
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商, API密钥, API基础URL, 模型参数, 启用状态
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%embedding%' AND 启用状态 = 1
            ORDER BY 创建时间 DESC
            LIMIT 1
            """

            结果 = await 异步连接池实例.执行查询(查询SQL)

            if 结果:
                配置 = 结果[0]
                RAG日志器.info(f"使用默认嵌入模型: {配置['模型名称']}")
                return 配置
            else:
                RAG日志器.warning("数据库中没有启用的嵌入模型")
                return None

        except Exception as e:
            RAG日志器.error(f"获取数据库嵌入模型配置失败: {str(e)}")
            return None

    async def _创建数据库嵌入模型(self, 配置: Dict[str, Any]) -> Optional[Any]:
        """根据数据库配置创建嵌入模型实例"""
        try:
            模型名称 = 配置["模型名称"]
            # 模型类型 = 配置["模型类型"]  # 暂未使用
            提供商 = 配置["提供商"].lower()
            API密钥 = 配置["API密钥"]
            API基础URL = 配置["API基础URL"]
            模型参数 = 配置.get("模型参数", {})

            if isinstance(模型参数, str):
                import json

                try:
                    模型参数 = json.loads(模型参数) if 模型参数.strip() else {}
                except json.JSONDecodeError as e:
                    RAG日志器.warning(f"模型参数JSON解析失败: {e}, 使用默认参数")
                    模型参数 = {}

            RAG日志器.info(f"创建嵌入模型实例: {模型名称}, 提供商: {提供商}")

            # 根据提供商创建对应的嵌入模型
            if "openai" in 提供商:
                if not API密钥:
                    RAG日志器.error("OpenAI嵌入模型需要API密钥")
                    return None

                openai_params = {"openai_api_key": API密钥, "model": 模型名称}
                if API基础URL:
                    openai_params["openai_api_base"] = API基础URL

                return OpenAIEmbeddings(**openai_params)

            elif "alibaba" in 提供商 or "阿里" in 提供商:
                if not API密钥:
                    RAG日志器.error("阿里云嵌入模型需要API密钥")
                    return None

                try:
                    from langchain_community.embeddings import DashScopeEmbeddings

                    return DashScopeEmbeddings(
                        dashscope_api_key=API密钥, model=模型名称
                    )
                except ImportError:
                    RAG日志器.error("DashScope库未安装")
                    return None

            elif "google" in 提供商:
                if not API密钥:
                    RAG日志器.error("Google嵌入模型需要API密钥")
                    return None

                try:
                    from langchain_google_genai import GoogleGenerativeAIEmbeddings

                    return GoogleGenerativeAIEmbeddings(
                        google_api_key=API密钥, model=模型名称
                    )
                except ImportError:
                    RAG日志器.error("Google AI库未安装")
                    return None

            elif "huggingface" in 提供商 or "local" in 提供商:
                try:
                    return HuggingFaceEmbeddings(model_name=模型名称, **模型参数)
                except Exception as e:
                    RAG日志器.error(f"HuggingFace嵌入模型创建失败: {str(e)}")
                    return None
            else:
                RAG日志器.error(f"不支持的嵌入模型提供商: {提供商}")
                return None

        except Exception as e:
            RAG日志器.error(f"创建数据库嵌入模型失败: {str(e)}")
            return None

    async def _初始化文档分割器(self):
        """初始化文档分割器"""
        try:
            self.文档分割器 = RecursiveCharacterTextSplitter(
                chunk_size=1000,  # type: ignore
                chunk_overlap=200,  # type: ignore
                length_function=len,  # type: ignore
                separators=["\n\n", "\n", " ", ""],  # type: ignore
            )
            RAG日志器.info("✅ 文档分割器初始化成功")
        except Exception:
            # 如果参数不支持，使用简化版本
            self.文档分割器 = RecursiveCharacterTextSplitter()
            RAG日志器.info("✅ 简化文档分割器初始化成功")

    async def _初始化Chroma向量存储(self):
        """初始化Chroma向量存储 - 优化版本"""
        try:
            if not CHROMA_AVAILABLE:
                RAG日志器.warning("Chroma不可用，回退到内存向量存储")
                await self._初始化内存向量存储()
                return

            if not self.嵌入模型:
                RAG日志器.warning("嵌入模型不可用，无法初始化Chroma")
                return

            RAG日志器.info("🔧 初始化ChromaDB向量存储...")

            # 🔧 关键修复：根据智能体ID获取关联知识库的集合名称
            if self.当前智能体id:
                # 确保使用最新的知识库信息
                if hasattr(self, "_知识库信息_缓存") and self._知识库信息_缓存:
                    集合名称 = self._知识库信息_缓存.get("集合名称")
                else:
                    # 重新获取知识库信息
                    self._知识库信息_缓存 = await self._获取智能体关联知识库信息(
                        self.当前智能体id
                    )
                    集合名称 = self._知识库信息_缓存.get("集合名称")

                if 集合名称:
                    self.chroma_集合名称 = 集合名称
                    RAG日志器.info(f"✅ 使用智能体关联知识库集合: {集合名称}")
                else:
                    # 备用方案：使用智能体ID生成集合名称
                    self.chroma_集合名称 = f"agent_{self.当前智能体id}_kb"
                    RAG日志器.warning(
                        f"⚠️ 未找到关联知识库集合，使用默认命名: {self.chroma_集合名称}"
                    )
            else:
                self.chroma_集合名称 = "default_knowledge_base"

            # 确保持久化路径存在
            import os

            os.makedirs(self.chroma_持久化路径, exist_ok=True)

            # 初始化Chroma客户端 - 使用Context7最佳实践配置
            try:
                # 根据Context7文档，使用推荐的配置
                chroma_settings = chromadb.config.Settings(
                    anonymized_telemetry=False,  # 禁用遥测以提高性能
                    allow_reset=True,  # 开发环境允许重置
                    # 注意：chroma_server_nofile在新版本中已移除
                )

                self.chroma_客户端 = chromadb.PersistentClient(
                    path=self.chroma_持久化路径, settings=chroma_settings
                )
                RAG日志器.debug("使用高级ChromaDB配置初始化成功")
            except Exception as e:
                # 如果高级配置失败，使用基本配置
                RAG日志器.warning(f"高级ChromaDB配置失败，使用基本配置: {str(e)}")
                self.chroma_客户端 = chromadb.PersistentClient(
                    path=self.chroma_持久化路径
                )

            # 创建或获取集合 - 使用Context7推荐的get_or_create_collection
            collection_metadata = {
                "description": f"Knowledge base for agent {self.当前智能体id or 'default'}",
                "hnsw:space": "cosine",  # 使用余弦相似度
            }

            # 使用Context7推荐的get_or_create_collection方法
            collection = self.chroma_客户端.get_or_create_collection(
                name=self.chroma_集合名称, metadata=collection_metadata
            )
            RAG日志器.info(
                f"✅ 获取或创建Chroma集合: {self.chroma_集合名称} (文档数: {collection.count()})"
            )

            # 初始化LangChain Chroma向量存储 - 使用最新API
            self.向量存储 = ChromaDB(
                client=self.chroma_客户端,
                collection_name=self.chroma_集合名称,
                embedding_function=self.嵌入模型,
            )

            # 验证向量存储是否正常工作
            try:
                heartbeat = self.chroma_客户端.heartbeat()
                RAG日志器.debug(f"ChromaDB心跳检测: {heartbeat}")
            except Exception as e:
                RAG日志器.warning(f"ChromaDB心跳检测失败: {str(e)}")

            RAG日志器.info(
                f"✅ ChromaDB向量存储初始化成功 - 集合: {self.chroma_集合名称}, 路径: {self.chroma_持久化路径}"
            )

        except Exception as e:
            RAG日志器.error(f"❌ ChromaDB初始化失败: {str(e)}")
            RAG日志器.warning("回退到内存向量存储")
            await self._初始化内存向量存储()

    async def _初始化内存向量存储(self):
        """初始化内存向量存储"""
        try:
            if self.嵌入模型:
                self.向量存储 = InMemoryVectorStore(self.嵌入模型)  # type: ignore
                RAG日志器.info("✅ 内存向量存储初始化成功")
            else:
                RAG日志器.warning("嵌入模型不可用，跳过向量存储初始化")
                self.向量存储 = None
        except Exception as e:
            RAG日志器.warning(f"内存向量存储初始化失败: {str(e)}")
            self.向量存储 = None

    async def _加载知识库(self, 知识库列表: List[str]):
        """加载知识库文档"""
        try:
            所有文档 = []

            for 知识库名称 in 知识库列表:
                知识库路径 = Path(self.知识库路径) / 知识库名称
                if 知识库路径.exists():
                    文档列表 = await self._加载目录文档(知识库路径)
                    所有文档.extend(文档列表)

            if 所有文档:
                # 分割文档
                try:
                    文档片段 = self.文档分割器.split_documents(所有文档)  # type: ignore
                except Exception:
                    # 如果方法不存在，直接使用原文档
                    文档片段 = 所有文档

                # 添加到向量存储
                await self._添加文档到向量存储(文档片段)

                RAG日志器.info(
                    f"成功加载 {len(所有文档)} 个文档，分割为 {len(文档片段)} 个片段"
                )

        except Exception as e:
            RAG日志器.error(f"加载知识库失败: {str(e)}")

    async def _加载目录文档(self, 目录路径: Path) -> List[Document]:
        """加载目录中的所有文档"""
        文档列表 = []

        try:
            for 文件路径 in 目录路径.rglob("*"):
                if 文件路径.is_file():
                    文档 = await self._加载单个文档(文件路径)
                    if 文档:
                        文档列表.extend(文档)
        except Exception as e:
            RAG日志器.error(f"加载目录文档失败: {str(e)}")

        return 文档列表

    async def _加载单个文档(self, 文件路径: Path) -> Optional[List[Document]]:
        """加载单个文档"""
        try:
            文件扩展名 = 文件路径.suffix.lower()

            if 文件扩展名 == ".txt":
                try:
                    加载器 = TextLoader(str(文件路径), encoding="utf-8")  # type: ignore
                except Exception:
                    加载器 = TextLoader()  # type: ignore
            elif 文件扩展名 == ".pdf":
                try:
                    加载器 = PDFLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = PDFLoader()  # type: ignore
            elif 文件扩展名 in [".doc", ".docx"]:
                try:
                    加载器 = UnstructuredWordDocumentLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = UnstructuredWordDocumentLoader()  # type: ignore
            elif 文件扩展名 in [".xls", ".xlsx"]:
                try:
                    加载器 = UnstructuredExcelLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = UnstructuredExcelLoader()  # type: ignore
            elif 文件扩展名 == ".csv":
                try:
                    加载器 = CSVLoader(str(文件路径))  # type: ignore
                except Exception:
                    加载器 = CSVLoader()  # type: ignore
            else:
                RAG日志器.warning(f"不支持的文件类型: {文件扩展名}")
                return None

            try:
                文档列表 = 加载器.load()  # type: ignore
            except Exception:
                # 如果load方法不存在，返回空列表
                文档列表 = []

            # 添加元数据
            for 文档 in 文档列表:
                文档.metadata.update(
                    {
                        "source": str(文件路径),
                        "filename": 文件路径.name,
                        "file_type": 文件扩展名,
                    }
                )

            return 文档列表

        except Exception as e:
            RAG日志器.error(f"加载文档失败 {文件路径}: {str(e)}")
            return None

    async def _添加文档到向量存储(self, 文档列表: List[Document]):
        """添加文档到向量存储"""
        try:
            if self.向量存储:
                try:
                    self.向量存储.add_documents(文档列表)  # type: ignore
                    RAG日志器.info(f"成功添加 {len(文档列表)} 个文档片段到向量存储")
                except Exception:
                    RAG日志器.warning("向量存储不支持add_documents方法")
        except Exception as e:
            RAG日志器.error(f"添加文档到向量存储失败: {str(e)}")

    async def 检索(self, 查询: str, k: int = 5) -> List[Document]:
        """检索相关文档"""
        try:
            if not self.已初始化:
                RAG日志器.warning("RAG引擎未初始化")
                return []

            # 使用Chroma向量检索
            检索结果 = await self.向量检索(查询, k)
            return 检索结果

        except Exception as e:
            RAG日志器.error(f"检索失败: {str(e)}")
            return []

    async def 检索带分数(self, 查询: str, k: int = 5) -> List[tuple]:
        """检索相关文档并返回相似度分数"""
        try:
            RAG日志器.info(
                f"🔍 检索查询: '{查询[:50]}{'...' if len(查询) > 50 else ''}', k={k}"
            )
            RAG日志器.debug(
                f"智能体ID: {getattr(self, '当前智能体id', 'None')}, 集合: {getattr(self, 'chroma_集合名称', 'None')}"
            )

            if not self.已初始化:
                RAG日志器.error("❌ RAG引擎未初始化")
                return []

            if not self.向量存储:
                RAG日志器.error("❌ 向量存储未初始化")
                return []

            if not LANGCHAIN_AVAILABLE:
                RAG日志器.error("❌ LangChain不可用，无法执行检索")
                return []

            # 检查Chroma集合状态（仅在必要时输出详细信息）
            if (
                hasattr(self, "chroma_客户端")
                and self.chroma_客户端
                and hasattr(self, "chroma_集合名称")
            ):
                try:
                    collection = self.chroma_客户端.get_collection(self.chroma_集合名称)
                    文档数量 = collection.count()
                    RAG日志器.debug(
                        f"📊 集合 '{self.chroma_集合名称}' 包含 {文档数量} 个文档"
                    )

                    if 文档数量 == 0:
                        RAG日志器.warning("⚠️ Chroma集合为空，无法执行检索")
                        return []

                except Exception as e:
                    RAG日志器.error(f"❌ 检查Chroma集合状态失败: {str(e)}")
                    return []

            # 执行相似性搜索并返回分数
            try:
                检索结果 = self.向量存储.similarity_search_with_score(查询, k=k)  # type: ignore
                RAG日志器.info(f"✅ 检索完成，返回 {len(检索结果)} 个结果")

                # 简化的结果日志
                if 检索结果:
                    分数列表 = [分数 for _, 分数 in 检索结果]
                    RAG日志器.debug(
                        f"📊 分数范围: {max(分数列表):.3f} - {min(分数列表):.3f}"
                    )
                else:
                    RAG日志器.warning("⚠️ 检索返回空结果")

            except Exception as e:
                RAG日志器.error(f"❌ 检索执行失败: {str(e)}")
                检索结果 = []

            return 检索结果

        except Exception as e:
            RAG日志器.error(f"❌ 检索带分数失败: {str(e)}")
            return []

    async def 添加文档(
        self, 文档内容: str, 元数据: Optional[Dict[str, Any]] = None
    ) -> bool:
        """添加单个文档"""
        try:
            if not self.已初始化:
                return False

            # 创建文档对象
            try:
                文档 = Document(
                    page_content=文档内容,  # type: ignore
                    metadata=元数据 or {},  # type: ignore
                )
            except Exception:
                文档 = Document(文档内容)  # type: ignore

            # 分割文档
            try:
                文档片段 = self.文档分割器.split_documents([文档])  # type: ignore
            except Exception:
                # 如果方法不存在，直接使用原文档
                文档片段 = [文档]

            # 添加到向量存储
            await self._添加文档到向量存储(文档片段)

            RAG日志器.info(f"成功添加文档，分割为 {len(文档片段)} 个片段")
            return True

        except Exception as e:
            RAG日志器.error(f"添加文档失败: {str(e)}")
            return False

    async def 删除文档(self, 文档id: str) -> bool:
        """删除文档"""
        try:
            if self.向量存储类型 == "chroma" and self.chroma_客户端:
                # Chroma支持删除操作
                collection = self.chroma_客户端.get_collection(
                    name=self.chroma_集合名称
                )
                collection.delete(ids=[文档id])
                RAG日志器.info(f"✅ 成功从Chroma删除文档: {文档id}")
                return True
            else:
                # 其他向量存储类型可能不支持删除操作
                RAG日志器.warning("当前向量存储不支持删除操作")
                return False

        except Exception as e:
            RAG日志器.error(f"删除文档失败: {str(e)}")
            return False

    # ==================== 混合存储向量化方法 ====================

    async def 向量化知识库(self, 知识库ID: int) -> Dict[str, Any]:
        """批量向量化知识库中的所有文档 - MySQL+ChromaDB混合存储架构"""
        try:
            if not self.已初始化:
                return {"success": False, "error": "RAG引擎未初始化"}

            if not self.向量存储:
                return {"success": False, "error": "向量存储未初始化"}

            RAG日志器.info(
                f"🚀 开始批量向量化知识库 {知识库ID} 中的所有文档 - ChromaDB混合存储模式"
            )

            # 获取知识库文档
            文档列表 = await self._获取知识库文档(知识库ID)
            if not 文档列表:
                return {"success": False, "error": "知识库中没有文档"}

            总分块数 = 0
            成功文档数 = 0
            失败文档数 = 0

            for 文档 in 文档列表:
                try:
                    # 安全获取文档记录ID
                    文档记录ID = None
                    if hasattr(文档, "metadata") and 文档.metadata:
                        文档记录ID = 文档.metadata.get("document_record_id")

                    if not 文档记录ID:
                        filename = (
                            getattr(文档, "metadata", {}).get("filename", "unknown")
                            if hasattr(文档, "metadata")
                            else "unknown"
                        )
                        RAG日志器.warning(f"文档缺少记录ID，跳过: {filename}")
                        continue

                    # 向量化单个文档
                    向量化结果 = await self._向量化单个文档(文档, 知识库ID, 文档记录ID)

                    if 向量化结果.get("success"):
                        总分块数 += 向量化结果.get("分块数量", 0)
                        成功文档数 += 1
                        RAG日志器.debug(
                            f"✅ 文档 {文档记录ID} 向量化成功，分块数: {向量化结果.get('分块数量', 0)}"
                        )
                    else:
                        失败文档数 += 1
                        RAG日志器.error(
                            f"❌ 文档 {文档记录ID} 向量化失败: {向量化结果.get('error')}"
                        )

                except Exception as e:
                    失败文档数 += 1
                    RAG日志器.error(f"❌ 处理文档时出错: {str(e)}")

            RAG日志器.info(
                f"✅ 知识库 {知识库ID} 向量化完成: 成功 {成功文档数} 个文档，失败 {失败文档数} 个，总分块 {总分块数}"
            )

            return {
                "success": True,
                "知识库ID": 知识库ID,
                "总文档数": len(文档列表),
                "成功文档数": 成功文档数,
                "失败文档数": 失败文档数,
                "总分块数": 总分块数,
                "存储模式": "MySQL+ChromaDB混合存储",
            }

        except Exception as e:
            RAG日志器.error(f"❌ 知识库向量化失败: {str(e)}")
            return {"success": False, "error": f"知识库向量化失败: {str(e)}"}

    async def _向量化单个文档(
        self, 文档, 知识库ID: int, 文档记录ID: int
    ) -> Dict[str, Any]:
        """向量化单个文档并存储到ChromaDB - 优化版本"""
        try:
            # 安全获取文档内容
            文档内容 = ""
            if hasattr(文档, "page_content"):
                文档内容 = 文档.page_content
            elif isinstance(文档, str):
                文档内容 = 文档
            else:
                return {"success": False, "error": "无法获取文档内容"}

            if not 文档内容.strip():
                return {"success": False, "error": "文档内容为空"}

            # 分割文档 - 安全检查
            if not self.文档分割器:
                return {"success": False, "error": "文档分割器未初始化"}

            try:
                # 使用Context7推荐的文档分割方法
                if hasattr(self.文档分割器, "split_text"):
                    文档分块列表 = self.文档分割器.split_text(文档内容)
                elif hasattr(self.文档分割器, "split_documents"):
                    # 如果是Document对象，先创建临时Document
                    from langchain_core.documents import Document as TempDocument

                    temp_doc = TempDocument(page_content=文档内容)
                    split_docs = self.文档分割器.split_documents([temp_doc])
                    文档分块列表 = [doc.page_content for doc in split_docs]
                else:
                    # 简单分割作为后备方案
                    文档分块列表 = [
                        文档内容[i : i + 1000] for i in range(0, len(文档内容), 1000)
                    ]
                    RAG日志器.warning("使用简单分割作为后备方案")
            except Exception as e:
                RAG日志器.error(f"文档分割失败: {str(e)}")
                return {"success": False, "error": f"文档分割失败: {str(e)}"}

            if not 文档分块列表:
                return {"success": False, "error": "文档分割后无有效内容"}

            # 准备ChromaDB文档对象
            文档对象列表 = []
            for i, 分块内容 in enumerate(文档分块列表):
                if 分块内容.strip():
                    # 安全获取原始文档元数据
                    原始元数据 = {}
                    if hasattr(文档, "metadata") and 文档.metadata:
                        原始元数据 = 文档.metadata

                    元数据 = {
                        "chunk_index": i,
                        "chunk_size": len(分块内容),
                        "document_record_id": 文档记录ID,
                        "knowledge_base_id": 知识库ID,
                        "source": 原始元数据.get("source", ""),
                        "filename": 原始元数据.get("filename", ""),
                        "file_type": 原始元数据.get("file_type", ""),
                    }

                    from langchain_core.documents import Document as LangChainDocument

                    文档对象列表.append(
                        LangChainDocument(page_content=分块内容, metadata=元数据)
                    )

            # 添加到ChromaDB - 安全检查
            if not self.向量存储:
                return {"success": False, "error": "向量存储未初始化"}

            try:
                if hasattr(self.向量存储, "add_documents"):
                    chroma_文档IDs = await asyncio.get_event_loop().run_in_executor(
                        None, self.向量存储.add_documents, 文档对象列表
                    )
                else:
                    return {"success": False, "error": "向量存储不支持添加文档"}
            except Exception as e:
                RAG日志器.error(f"添加文档到ChromaDB失败: {str(e)}")
                return {"success": False, "error": f"添加文档到ChromaDB失败: {str(e)}"}

            # 同步更新PostgreSQL分块元数据
            try:
                await self._同步分块元数据到PostgreSQL(文档记录ID, 文档对象列表)
            except Exception as e:
                RAG日志器.warning(f"同步PostgreSQL元数据失败: {str(e)}")

            return {
                "success": True,
                "分块数量": len(文档对象列表),
                "chroma_文档IDs": chroma_文档IDs if chroma_文档IDs else [],
            }

        except Exception as e:
            RAG日志器.error(f"文档向量化失败: {str(e)}")
            return {"success": False, "error": f"文档向量化失败: {str(e)}"}

    async def _同步分块元数据到PostgreSQL(self, 文档记录ID: int, 文档对象列表):
        """同步分块元数据到PostgreSQL - 精简版本"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 更新文档的向量化状态和分块数量
            更新SQL = """
            UPDATE langchain_知识库文档表
            SET 向量状态 = '已完成',
                向量分块数量 = $1,
                最后向量化时间 = CURRENT_TIMESTAMP,
                更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """
            async with 异步连接池实例.获取连接() as 连接:
                await 连接.execute(更新SQL, len(文档对象列表), 文档记录ID)

            RAG日志器.debug(
                f"✅ PostgreSQL元数据同步完成: 文档 {文档记录ID}, 分块数 {len(文档对象列表)}"
            )

        except Exception as e:
            RAG日志器.error(f"❌ 同步分块元数据到PostgreSQL失败: {str(e)}")
            raise  # 重新抛出异常以便上层处理

    async def _获取知识库文档(self, 知识库ID: int) -> List:
        """从数据库获取知识库文档"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 查询知识库文档 - 使用中文状态字段
            查询SQL = """
            SELECT id, 文档名称, 文档内容, 元数据, 文档类型
            FROM langchain_知识库文档表
            WHERE langchain_知识库表id = $1 AND 文档状态 = '已处理'
            ORDER BY 创建时间 DESC
            """

            async with 异步连接池实例.获取连接() as 连接:
                文档记录列表 = await 连接.fetch(查询SQL, 知识库ID)

            if not 文档记录列表:
                RAG日志器.warning(f"知识库 {知识库ID} 中没有找到文档")
                return []

            # 转换为Document对象
            from langchain_core.documents import Document

            文档列表 = []

            for 记录 in 文档记录列表:
                文档ID, 文档名称, 文档内容, 元数据字符串, 文档类型 = 记录

                # 解析元数据
                try:
                    元数据 = json.loads(元数据字符串) if 元数据字符串 else {}
                except (json.JSONDecodeError, TypeError):
                    元数据 = {}

                # 添加必要的元数据
                元数据.update(
                    {
                        "document_record_id": 文档ID,
                        "filename": 文档名称,
                        "file_type": 文档类型,
                        "knowledge_base_id": 知识库ID,
                    }
                )

                文档对象 = Document(page_content=文档内容 or "", metadata=元数据)
                文档列表.append(文档对象)

            RAG日志器.info(f"✅ 从知识库 {知识库ID} 获取到 {len(文档列表)} 个文档")
            return 文档列表

        except Exception as e:
            RAG日志器.error(f"❌ 获取知识库文档失败: {str(e)}")
            return []

    async def 清理(self):
        """清理资源"""
        try:
            self.向量存储 = None
            self.嵌入模型 = None
            self.文档分割器 = None
            self.已初始化 = False
            RAG日志器.info("RAG引擎资源清理完成")
        except Exception as e:
            RAG日志器.error(f"RAG引擎清理失败: {str(e)}")

    def 获取状态(self) -> Dict[str, Any]:
        """获取RAG引擎状态"""
        状态信息 = {
            "已初始化": self.已初始化,
            "LangChain可用": LANGCHAIN_AVAILABLE,
            "Chroma可用": CHROMA_AVAILABLE,
            "嵌入模型": str(type(self.嵌入模型).__name__) if self.嵌入模型 else None,
            "向量存储": str(type(self.向量存储).__name__) if self.向量存储 else None,
            "向量存储类型": self.向量存储类型,
            "知识库路径": self.知识库路径,
            "Chroma集合名称": self.chroma_集合名称,
            "Chroma持久化路径": self.chroma_持久化路径,
        }

        # 添加详细信息
        if self.向量存储:
            状态信息["向量存储类型"] = type(self.向量存储).__name__
            # 尝试获取文档数量
            try:
                if hasattr(self.向量存储, "_collection") and hasattr(
                    self.向量存储._collection, "count"
                ):
                    状态信息["向量总数"] = self.向量存储._collection.count()
                elif hasattr(self.向量存储, "index") and hasattr(
                    self.向量存储.index, "ntotal"
                ):
                    状态信息["向量总数"] = self.向量存储.index.ntotal
                else:
                    状态信息["向量总数"] = 0
            except Exception:
                状态信息["向量总数"] = 0

        # 检测OpenAI是否可用
        try:
            import os

            状态信息["OpenAI_可用"] = bool(os.getenv("OPENAI_API_KEY"))
        except Exception:
            状态信息["OpenAI_可用"] = False

        return 状态信息

    async def _获取智能体关联知识库信息(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体关联知识库的配置信息（嵌入模型和集合名称）- 使用关联表"""
        try:
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            # 分步查询以避免复杂JOIN导致的参数问题
            # 第一步：获取智能体基本信息和关联的知识库ID
            智能体查询SQL = """
            SELECT
                ac.启用RAG,
                akr.langchain_知识库表id as 知识库ID
            FROM langchain_智能体配置表 ac
            LEFT JOIN langchain_智能体知识库关联表 akr ON ac.id = akr.langchain_智能体配置表id
                AND akr.状态 = %s
            WHERE ac.id = %s AND ac.启用RAG = 1
            LIMIT 1
            """

            RAG日志器.info(f"🔍 执行智能体查询SQL: {智能体查询SQL}")
            RAG日志器.info(
                f"🔍 智能体查询参数: ('active', {智能体id}), 智能体ID类型: {type(智能体id)}"
            )

            智能体结果 = await 异步连接池实例.执行查询(
                智能体查询SQL, ("active", 智能体id)
            )

            RAG日志器.info(f"🔍 智能体查询结果: {智能体结果}")

            if not 智能体结果:
                RAG日志器.info(f"ℹ️ 智能体 {智能体id} 未启用RAG或不存在")
                return {"集合名称": None, "嵌入模型": None}

            数据 = 智能体结果[0]

            # 检查是否有关联的知识库
            知识库ID = 数据.get("知识库ID")
            if not 知识库ID:
                RAG日志器.info(f"ℹ️ 智能体 {智能体id} 未关联任何知识库")
                return {"集合名称": None, "嵌入模型": None}

            # 第二步：查询知识库详情和嵌入模型信息
            知识库查询SQL = """
            SELECT
                kb.chroma_集合名称,
                m.id as 模型id, m.模型名称, m.模型类型, m.显示名称, m.提供商,
                m.API密钥, m.API基础URL, m.模型参数, m.启用状态
            FROM langchain_知识库表 kb
            LEFT JOIN langchain_模型配置表 m ON kb.嵌入模型 = m.id
                AND m.启用状态 = 1
            WHERE kb.id = $1 AND kb.知识库状态 = $2
            """

            RAG日志器.info(f"🔍 执行知识库查询SQL: {知识库查询SQL}")
            RAG日志器.info(f"🔍 知识库查询参数: ({知识库ID}, '活跃')")

            知识库结果 = await 异步连接池实例.执行查询(
                知识库查询SQL, (知识库ID, "活跃")
            )

            RAG日志器.info(f"🔍 知识库查询结果: {知识库结果}")

            if not 知识库结果:
                RAG日志器.warning(f"⚠️ 知识库 {知识库ID} 不存在或未激活")
                return {"集合名称": None, "嵌入模型": None}

            知识库数据 = 知识库结果[0]

            返回信息 = {
                "集合名称": 知识库数据.get("chroma_集合名称"),
                "嵌入模型": None,
            }

            # 如果有嵌入模型配置
            if 知识库数据.get("模型id"):
                返回信息["嵌入模型"] = {
                    "id": 知识库数据["模型id"],
                    "模型名称": 知识库数据["模型名称"],
                    "模型类型": 知识库数据["模型类型"],
                    "显示名称": 知识库数据["显示名称"],
                    "提供商": 知识库数据["提供商"],
                    "API密钥": 知识库数据["API密钥"],
                    "API基础URL": 知识库数据["API基础URL"],
                    "模型参数": 知识库数据["模型参数"],
                    "启用状态": 知识库数据["启用状态"],
                }
                RAG日志器.info(
                    f"✅ 找到智能体 {智能体id} 嵌入模型: {知识库数据['显示名称']}"
                )

            if 返回信息["集合名称"]:
                RAG日志器.info(f"✅ 找到智能体 {智能体id} 集合: {返回信息['集合名称']}")

            return 返回信息

        except Exception as e:
            RAG日志器.error(f"❌ 获取智能体关联知识库信息失败: {str(e)}")
            RAG日志器.error(f"❌ 错误详情: 智能体ID={智能体id}, 类型={type(智能体id)}")
            import traceback

            RAG日志器.error(f"❌ 完整错误堆栈: {traceback.format_exc()}")
            return {"集合名称": None, "嵌入模型": None}

    async def 根据知识库ID初始化(self, 知识库ID: int) -> bool:
        """根据知识库ID直接初始化RAG引擎，确保使用正确的嵌入模型和集合"""
        try:
            RAG日志器.info(f"🔧 根据知识库ID初始化RAG引擎: {知识库ID}")

            # 获取知识库详情
            from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

            知识库查询SQL = """
            SELECT
                kb.id, kb.知识库名称, kb.chroma_集合名称, kb.嵌入模型,
                m.id as 模型id, m.模型名称, m.模型类型, m.显示名称, m.提供商,
                m.API密钥, m.API基础URL, m.模型参数, m.启用状态
            FROM langchain_知识库表 kb
            LEFT JOIN langchain_模型配置表 m ON kb.嵌入模型 = m.id
                AND m.启用状态 = 1
            WHERE kb.id = %s AND kb.知识库状态 = '活跃'
            """

            知识库结果 = await 异步连接池实例.执行查询(知识库查询SQL, (知识库ID,))

            if not 知识库结果:
                RAG日志器.error(f"❌ 知识库 {知识库ID} 不存在或未激活")
                return False

            知识库数据 = 知识库结果[0]

            # 设置集合名称
            集合名称 = 知识库数据.get("chroma_集合名称")
            if 集合名称:
                self.chroma_集合名称 = 集合名称
                RAG日志器.info(f"✅ 设置Chroma集合名称: {集合名称}")
            else:
                RAG日志器.warning(f"⚠️ 知识库 {知识库ID} 未设置集合名称")
                return False

            # 初始化嵌入模型
            if 知识库数据.get("模型id"):
                嵌入模型配置 = {
                    "id": 知识库数据["模型id"],
                    "模型名称": 知识库数据["模型名称"],
                    "模型类型": 知识库数据["模型类型"],
                    "显示名称": 知识库数据["显示名称"],
                    "提供商": 知识库数据["提供商"],
                    "API密钥": 知识库数据["API密钥"],
                    "API基础URL": 知识库数据["API基础URL"],
                    "模型参数": 知识库数据["模型参数"],
                    "启用状态": 知识库数据["启用状态"],
                }

                嵌入模型实例 = await self._创建数据库嵌入模型(嵌入模型配置)
                if 嵌入模型实例:
                    self.嵌入模型 = 嵌入模型实例
                    RAG日志器.info(f"✅ 设置嵌入模型: {嵌入模型配置['显示名称']}")
                else:
                    RAG日志器.error(f"❌ 嵌入模型创建失败: {嵌入模型配置['显示名称']}")
                    return False
            else:
                RAG日志器.warning(f"⚠️ 知识库 {知识库ID} 未配置嵌入模型")
                return False

            # 初始化文档分割器
            await self._初始化文档分割器()

            # 初始化Chroma向量存储
            await self._初始化Chroma向量存储()

            if self.向量存储:
                RAG日志器.info(f"✅ 知识库 {知识库ID} RAG引擎初始化成功")
                self.已初始化 = True
                # 🔧 重要：不要加载其他知识库的数据，只使用现有的向量存储
                return True
            else:
                RAG日志器.error(f"❌ 知识库 {知识库ID} 向量存储初始化失败")
                return False

        except Exception as e:
            RAG日志器.error(f"❌ 根据知识库ID初始化失败: {str(e)}")
            return False

    async def _加载智能体关联知识库(self, 智能体id: int):
        """从数据库加载智能体关联的知识库 - 优化版本，避免重复加载"""
        try:
            RAG日志器.info(f"开始加载智能体 {智能体id} 关联的知识库")

            # 检查Chroma集合中是否已有数据
            if self.chroma_客户端 and self.chroma_集合名称:
                try:
                    collection = self.chroma_客户端.get_collection(self.chroma_集合名称)
                    现有文档数量 = collection.count()

                    if 现有文档数量 > 0:
                        RAG日志器.info(
                            f"✅ Chroma集合 {self.chroma_集合名称} 已包含 {现有文档数量} 个向量，跳过重复加载"
                        )
                        return
                    else:
                        RAG日志器.info(
                            f"📝 Chroma集合 {self.chroma_集合名称} 为空，开始加载文档"
                        )
                except Exception as e:
                    RAG日志器.warning(f"检查Chroma集合状态失败: {str(e)}，继续加载文档")

            # 导入智能体数据层
            from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例

            # 获取智能体关联的知识库列表
            关联知识库列表 = await LangChain智能体数据层实例.获取智能体关联知识库(
                智能体id
            )

            if not 关联知识库列表:
                RAG日志器.info(f"智能体 {智能体id} 未关联任何知识库")
                return

            RAG日志器.info(f"智能体 {智能体id} 关联了 {len(关联知识库列表)} 个知识库")

            # 加载每个关联的知识库
            所有文档 = []
            for 知识库信息 in 关联知识库列表:
                知识库id = 知识库信息["知识库id"]
                知识库名称 = 知识库信息["知识库名称"]

                RAG日志器.info(f"加载知识库: {知识库名称} (ID: {知识库id})")

                # 获取知识库的文档列表
                文档列表 = await self._获取知识库文档(知识库id)
                所有文档.extend(文档列表)

            if 所有文档:
                # 分割文档
                try:
                    文档片段 = self.文档分割器.split_documents(所有文档)  # type: ignore
                except Exception:
                    # 如果方法不存在，直接使用原文档
                    文档片段 = 所有文档

                # 添加到向量存储
                await self._添加文档到向量存储(文档片段)

                RAG日志器.info(
                    f"成功加载智能体 {智能体id} 的 {len(所有文档)} 个文档，分割为 {len(文档片段)} 个片段"
                )
            else:
                RAG日志器.warning(f"智能体 {智能体id} 关联的知识库中没有找到文档")

        except Exception as e:
            RAG日志器.error(f"加载智能体关联知识库失败: {str(e)}")

    # 重复的方法已删除 - 使用上面优化的_获取知识库文档方法

    async def 向量检索(self, 查询: str, k: int = 5) -> List[Document]:
        """Chroma向量检索"""
        try:
            RAG日志器.info(f"Chroma向量检索: {查询}")

            if not self.向量存储:
                RAG日志器.error("Chroma向量存储未初始化")
                return []

            # 直接使用Chroma进行相似度搜索
            检索结果 = self.向量存储.similarity_search(查询, k=k)

            RAG日志器.info(f"检索完成，返回 {len(检索结果)} 个结果")
            return 检索结果

        except Exception as e:
            RAG日志器.error(f"Chroma向量检索失败: {str(e)}")
            return []

    # ==================== Chroma特有方法 ====================

    async def 获取Chroma集合信息(self) -> Dict[str, Any]:
        """获取Chroma集合信息"""
        try:
            if not self.chroma_客户端 or not self.chroma_集合名称:
                return {"success": False, "error": "Chroma未初始化"}

            collection = self.chroma_客户端.get_collection(name=self.chroma_集合名称)

            集合信息 = {
                "success": True,
                "集合名称": self.chroma_集合名称,
                "文档数量": collection.count(),
                "元数据": collection.metadata,
                "持久化路径": self.chroma_持久化路径,
            }

            return 集合信息

        except Exception as e:
            RAG日志器.error(f"获取Chroma集合信息失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def 清空Chroma集合(self) -> Dict[str, Any]:
        """清空Chroma集合中的所有文档"""
        try:
            if not self.chroma_客户端 or not self.chroma_集合名称:
                return {"success": False, "error": "Chroma未初始化"}

            # 删除集合
            self.chroma_客户端.delete_collection(name=self.chroma_集合名称)

            # 重新创建集合
            self.chroma_客户端.create_collection(
                name=self.chroma_集合名称,
                metadata={
                    "description": f"Knowledge base for agent {self.当前智能体id}"
                },
            )

            # 重新初始化向量存储
            self.向量存储 = ChromaDB(
                client=self.chroma_客户端,
                collection_name=self.chroma_集合名称,
                embedding_function=self.嵌入模型,
            )

            RAG日志器.info(f"✅ 成功清空Chroma集合: {self.chroma_集合名称}")
            return {"success": True, "message": "集合已清空"}

        except Exception as e:
            RAG日志器.error(f"清空Chroma集合失败: {str(e)}")
            return {"success": False, "error": str(e)}


# 创建全局RAG引擎实例
RAG引擎实例 = LangChainRAG引擎()
