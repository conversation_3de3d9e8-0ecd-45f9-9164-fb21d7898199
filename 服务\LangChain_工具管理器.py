"""
LangChain工具管理器

功能：
1. 工具注册和管理
2. 工具调用和执行
3. MCP工具集成
4. 自定义工具支持
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

# 静态导入所有依赖 - 避免动态导入
from 数据.LangChain_工具数据层 import LangChain工具数据层实例, LangChain工具数据层

# LangChain工具组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain_core.tools import BaseTool, tool  # type: ignore
    from langchain_community.tools import DuckDuckGoSearchRun  # type: ignore
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符类
    class BaseTool:  # type: ignore
        def run(self, *_args, **_kwargs):
            return "工具不可用"

    def tool(func):  # type: ignore
        return func

    class CallbackManagerForToolRun:  # type: ignore
        pass

    class DuckDuckGoSearchRun:  # type: ignore
        def run(self, _query: str) -> str:
            return "搜索功能不可用"

# 配置日志
工具日志器 = logging.getLogger("LangChain.工具管理器")

class 简单工具:
    """简单工具实现"""
    
    def __init__(self, name: str, description: str, func: Callable):
        self.name = name
        self.description = description
        self.func = func
    
    async def run(self, *args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(self.func):
                return await self.func(*args, **kwargs)
            else:
                return self.func(*args, **kwargs)
        except Exception as e:
            return f"工具执行错误: {str(e)}"

class LangChain工具管理器:
    """LangChain工具管理器 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于工具管理业务逻辑
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 工具数据层: Optional[LangChain工具数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            工具数据层: LangChain工具数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保工具数据层永远不为None
        self.工具数据层: LangChain工具数据层 = 工具数据层 or LangChain工具数据层实例
        self.工具注册表 = {}  # 工具名称 -> 工具实例 (运行时缓存)
        self.工具配置 = {}   # 工具名称 -> 配置信息 (运行时缓存)
        self.已初始化 = True  # 简化初始化逻辑

        工具日志器.info("LangChain工具管理器创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain工具管理器":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保工具数据层已初始化
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 创建管理器实例
        实例 = cls(LangChain工具数据层实例)

        # 执行异步初始化逻辑
        await 实例._异步初始化()

        return 实例

    async def _异步初始化(self):
        """内部异步初始化方法"""
        try:
            # 从数据库加载工具配置
            await self._从数据库加载工具配置()

            # 注册默认工具
            await self._注册默认工具()

            # 初始化内部函数工具
            await self._初始化内部函数工具()

            工具日志器.info("✅ LangChain工具管理器异步初始化成功")

        except Exception as e:
            工具日志器.error(f"❌ LangChain工具管理器异步初始化失败: {str(e)}")
            raise

    async def _从数据库加载工具配置(self):
        """从数据库加载工具配置 - 统一的工具配置加载逻辑"""
        try:
            if not self.工具数据层:
                工具日志器.warning("工具数据层未初始化，跳过数据库加载")
                return

            # 获取所有启用的工具配置
            工具配置列表 = await self.工具数据层.获取工具配置列表(启用状态=True)

            加载成功数量 = 0
            for 工具配置 in 工具配置列表:
                if await self._加载单个工具配置(工具配置):
                    加载成功数量 += 1

            工具日志器.info(f"✅ 从数据库加载了 {加载成功数量}/{len(工具配置列表)} 个工具配置")

        except Exception as e:
            工具日志器.error(f"从数据库加载工具配置失败: {str(e)}")

    async def _加载单个工具配置(self, 工具配置: Dict[str, Any]) -> bool:
        """加载单个工具配置的统一逻辑"""
        try:
            工具名称 = 工具配置.get('工具名称')
            工具类型 = 工具配置.get('工具类型')

            # 类型检查
            if not isinstance(工具名称, str) or not isinstance(工具类型, str):
                工具日志器.warning(f"跳过无效工具配置: 工具名称={工具名称}, 工具类型={工具类型}")
                return False

            # 将数据库配置加载到内存缓存
            self.工具配置[工具名称] = self._构建工具配置信息(工具配置)

            # 根据工具类型创建工具实例
            return await self._创建数据库工具实例(工具名称, 工具配置)

        except Exception as e:
            工具日志器.error(f"加载工具配置失败 ({工具配置.get('工具名称', 'unknown')}): {str(e)}")
            return False

    def _构建工具配置信息(self, 工具配置: Dict[str, Any]) -> Dict[str, Any]:
        """构建标准化的工具配置信息"""
        工具类型 = 工具配置.get('工具类型') or 'custom'
        return {
            "描述": 工具配置.get('工具描述', ''),
            "类型": 工具类型,
            "分类": self._获取工具分类(工具类型),
            "权限要求": 工具配置.get('权限要求', []),
            "安全级别": 工具配置.get('安全级别', 1),
            "超时时间": 工具配置.get('超时时间', 30),
            "重试次数": 工具配置.get('重试次数', 3),
            "数据库配置": True,  # 标记为数据库配置
            "注册时间": 工具配置.get('创建时间'),
            "启用状态": 工具配置.get('启用状态', True)
        }

    def _获取工具分类(self, 工具类型: str) -> str:
        """根据工具类型获取分类"""
        分类映射 = {
            'calculation': '数学计算',
            'search': '搜索工具',
            'database': '数据库工具',
            'api': 'API工具',
            'file': '文件工具',
            'builtin': '系统工具',
            'custom': '自定义工具'
        }
        return 分类映射.get(工具类型, '其他工具')

    async def _创建数据库工具实例(self, 工具名称: str, 工具配置: Dict[str, Any]) -> bool:
        """根据数据库配置创建工具实例 - 统一的工具实例创建逻辑"""
        try:
            工具类型 = 工具配置.get('工具类型') or 'custom'

            # 使用工具工厂模式创建实例
            工具实例 = await self._工具工厂(工具名称, 工具类型, 工具配置)

            if 工具实例:
                self.工具注册表[工具名称] = 工具实例
                工具日志器.debug(f"✅ 创建工具实例成功: {工具名称} ({工具类型})")
                return True
            else:
                工具日志器.debug(f"⚠️ 工具类型 {工具类型} 暂不支持自动创建: {工具名称}")
                return False

        except Exception as e:
            工具日志器.error(f"创建数据库工具实例失败 ({工具名称}): {str(e)}")
            return False

    async def _工具工厂(self, 工具名称: str, 工具类型: str, 工具配置: Dict[str, Any]):
        """工具工厂 - 根据类型创建对应的工具实例"""
        try:
            if 工具类型 == 'search':
                return await self._创建搜索工具(工具名称, 工具配置)
            elif 工具类型 == 'calculation':
                return await self._创建计算工具(工具名称, 工具配置)
            elif 工具类型 == 'text':
                return await self._创建文本工具(工具名称, 工具配置)
            elif 工具类型 in ['api', 'database', 'file']:
                # 这些工具需要特殊配置，暂时返回None
                return None
            else:
                工具日志器.warning(f"未知工具类型: {工具类型}")
                return None

        except Exception as e:
            工具日志器.error(f"工具工厂创建失败 ({工具名称}): {str(e)}")
            return None

    async def _创建搜索工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建搜索工具实例"""
        if not LANGCHAIN_AVAILABLE:
            return None

        try:
            if 工具名称 == 'duckduckgo_search':
                return DuckDuckGoSearchRun()
            # 可以扩展其他搜索工具
            return None
        except Exception as e:
            工具日志器.warning(f"创建搜索工具失败: {e}")
            return None

    async def _创建计算工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建计算工具实例"""
        # 计算工具通常在_注册标准LangChain工具中创建
        # 这里可以扩展其他计算工具
        return None

    async def _创建文本工具(self, 工具名称: str, 工具配置: Dict[str, Any]):
        """创建文本处理工具实例"""
        # 可以扩展文本处理工具
        return None

    async def _注册默认工具(self):
        """注册默认工具 - 简化版本，避免与内部函数工具重复"""
        try:
            # 只注册标准LangChain工具，其他工具由内部函数包装器管理
            await self._注册标准LangChain工具()

            工具日志器.info("✅ 默认工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册默认工具失败: {str(e)}")

    async def _注册标准LangChain工具(self):
        """注册标准的LangChain工具"""
        try:
            if not LANGCHAIN_AVAILABLE:
                工具日志器.warning("LangChain不可用，跳过标准工具注册")
                return

            # 数学计算工具 - 遵循LangChain最佳实践
            @tool
            def multiply(first_int: int, second_int: int) -> int:
                """Multiply two integers together.

                Args:
                    first_int: The first integer to multiply
                    second_int: The second integer to multiply

                Returns:
                    The product of the two integers
                """
                return first_int * second_int

            @tool
            def add(first_int: int, second_int: int) -> int:
                """Add two integers together.

                Args:
                    first_int: The first integer to add
                    second_int: The second integer to add

                Returns:
                    The sum of the two integers
                """
                return first_int + second_int

            @tool
            def subtract(first_int: int, second_int: int) -> int:
                """Subtract the second integer from the first integer.

                Args:
                    first_int: The integer to subtract from
                    second_int: The integer to subtract

                Returns:
                    The difference between the two integers
                """
                return first_int - second_int

            @tool
            def divide(first_int: int, second_int: int) -> float:
                """Divide the first integer by the second integer.

                Args:
                    first_int: The dividend (number to be divided)
                    second_int: The divisor (number to divide by)

                Returns:
                    The quotient as a float

                Raises:
                    ValueError: If the divisor is zero
                """
                if second_int == 0:
                    raise ValueError("Division by zero is not allowed")
                return first_int / second_int

            # 注册LangChain工具
            await self.注册LangChain工具("multiply", multiply, {"分类": "数学计算"})
            await self.注册LangChain工具("add", add, {"分类": "数学计算"})
            await self.注册LangChain工具("subtract", subtract, {"分类": "数学计算"})
            await self.注册LangChain工具("divide", divide, {"分类": "数学计算"})

            工具日志器.info("✅ 标准LangChain工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册标准LangChain工具失败: {str(e)}")
    

    
    async def 注册工具(self, 工具名称: str, 工具实例_或_函数, 配置: Optional[Dict[str, Any]] = None, 覆盖已存在: bool = False) -> bool:
        """统一的工具注册方法 - 支持多种工具类型"""
        try:
            # 检查工具是否已存在
            if 工具名称 in self.工具注册表:
                if not 覆盖已存在:
                    工具日志器.info(f"工具已存在，跳过注册: {工具名称}")
                    return True  # 返回True表示工具已存在，不需要重复注册
                else:
                    工具日志器.info(f"覆盖已存在的工具: {工具名称}")

            # 统一的工具实例处理
            工具实例, 工具类型 = await self._处理工具实例(工具名称, 工具实例_或_函数)

            if not 工具实例:
                工具日志器.error(f"无法创建工具实例: {工具名称}")
                return False

            # 原子性注册：同时更新注册表和配置
            try:
                self.工具注册表[工具名称] = 工具实例
                self.工具配置[工具名称] = self._构建注册工具配置(工具实例, 工具类型, 配置)

                工具日志器.info(f"注册工具成功: {工具名称} ({工具类型})")
                return True

            except Exception as reg_error:
                # 回滚注册
                self.工具注册表.pop(工具名称, None)
                self.工具配置.pop(工具名称, None)
                raise reg_error

        except Exception as e:
            工具日志器.error(f"注册工具失败 {工具名称}: {str(e)}")
            return False

    async def _处理工具实例(self, 工具名称: str, 工具实例_或_函数):
        """处理不同类型的工具实例"""
        try:
            # LangChain BaseTool实例
            if LANGCHAIN_AVAILABLE and isinstance(工具实例_或_函数, BaseTool):
                return 工具实例_或_函数, "LangChain工具"

            # 普通函数 - 转换为LangChain工具
            elif callable(工具实例_或_函数):
                if LANGCHAIN_AVAILABLE:
                    # 使用已导入的tool装饰器
                    工具实例 = tool(工具实例_或_函数)
                    工具实例.name = 工具名称
                    工具实例.description = 工具实例_或_函数.__doc__ or f"{工具名称}工具"
                    return 工具实例, "LangChain工具"
                else:
                    # 回退到简单工具
                    描述 = getattr(工具实例_或_函数, '__doc__', '') or f"{工具名称}工具"
                    工具实例 = 简单工具(工具名称, 描述, 工具实例_或_函数)
                    return 工具实例, "自定义工具"

            # 简单工具实例
            elif isinstance(工具实例_或_函数, 简单工具):
                return 工具实例_或_函数, "自定义工具"

            else:
                工具日志器.error(f"不支持的工具类型: {type(工具实例_或_函数)}")
                return None, None

        except Exception as e:
            工具日志器.error(f"处理工具实例失败: {str(e)}")
            return None, None

    def _构建注册工具配置(self, 工具实例, 工具类型: str, 额外配置: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """构建注册工具的配置信息"""
        基础配置 = {
            "描述": getattr(工具实例, 'description', '') or getattr(工具实例, '描述', ''),
            "类型": 工具类型,
            "注册时间": datetime.now(),
            "数据库配置": False,  # 标记为运行时注册
            "启用状态": True
        }

        if 额外配置:
            基础配置.update(额外配置)

        return 基础配置

    # 保留向后兼容的方法
    async def 注册LangChain工具(self, 工具名称: str, 工具函数_或_实例, 配置: Optional[Dict[str, Any]] = None) -> bool:
        """注册LangChain工具 - 向后兼容方法"""
        return await self.注册工具(工具名称, 工具函数_或_实例, 配置)
    
    async def 调用工具(self, 工具名称: str, *参数列表, **关键字参数) -> Dict[str, Any]:
        """调用工具"""
        开始时间 = datetime.now()

        try:
            if 工具名称 not in self.工具注册表:
                return {
                    "success": False,
                    "error": f"工具不存在: {工具名称}",
                    "result": None,
                    "execution_time": 0
                }

            工具实例 = self.工具注册表[工具名称]

            # 调用工具
            if isinstance(工具实例, 简单工具):
                执行结果 = await 工具实例.run(*参数列表, **关键字参数)
            elif LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
                if 参数列表:
                    执行结果 = 工具实例.run(参数列表[0])  # LangChain工具通常接受单个字符串参数
                else:
                    执行结果 = 工具实例.run("")
            else:
                执行结果 = "不支持的工具类型"

            执行时间 = (datetime.now() - 开始时间).total_seconds()

            工具日志器.debug(f"工具调用成功: {工具名称}, 执行时间: {执行时间:.2f}s")

            # 记录成功调用统计到数据库
            if self.工具数据层:
                try:
                    await self.工具数据层.记录工具调用(工具名称, 成功=True)
                except Exception as 统计异常:
                    工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

            return {
                "success": True,
                "result": 执行结果,
                "execution_time": 执行时间,
                "tool_name": 工具名称
            }

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            工具日志器.error(f"工具调用失败 {工具名称}: {str(e)}")

            # 记录失败调用统计到数据库
            if self.工具数据层:
                try:
                    await self.工具数据层.记录工具调用(工具名称, 成功=False)
                except Exception as 统计异常:
                    工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "execution_time": 执行时间,
                "tool_name": 工具名称
            }
    
    async def 获取工具列表(self, 分类: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取工具列表"""
        try:
            工具列表 = []
            
            for 工具名称, 配置 in self.工具配置.items():
                if 分类 and 配置.get("分类") != 分类:
                    continue
                
                工具列表.append({
                    "工具名称": 工具名称,
                    "描述": 配置.get("描述", ""),
                    "类型": 配置.get("类型", "未知"),
                    "分类": 配置.get("分类", "其他"),
                    "注册时间": 配置.get("注册时间", "").isoformat() if isinstance(配置.get("注册时间"), datetime) else str(配置.get("注册时间", ""))
                })
            
            return 工具列表
            
        except Exception as e:
            工具日志器.error(f"获取工具列表失败: {str(e)}")
            return []
    
    async def 删除工具(self, 工具名称: str) -> bool:
        """删除工具"""
        try:
            if 工具名称 in self.工具注册表:
                del self.工具注册表[工具名称]
            
            if 工具名称 in self.工具配置:
                del self.工具配置[工具名称]
            
            工具日志器.info(f"删除工具成功: {工具名称}")
            return True
            
        except Exception as e:
            工具日志器.error(f"删除工具失败 {工具名称}: {str(e)}")
            return False
    
    # ==================== 内部函数工具集成 ====================
    
    async def _初始化内部函数工具(self):
        """初始化内部函数工具 - 简化版本"""
        try:
            工具日志器.info("开始初始化内部函数工具...")

            # 导入简化的内部函数包装器
            from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

            await 内部函数包装器实例.初始化()

            # 注册内部函数工具
            for 工具名称, 工具实例 in 内部函数包装器实例.已注册工具.items():
                await self.注册LangChain工具(
                    工具名称,
                    工具实例,
                    {"类型": "内部函数工具", "分类": "业务功能"}
                )

            工具日志器.info(f"✅ 内部函数工具初始化成功，注册了 {len(内部函数包装器实例.已注册工具)} 个工具")

        except Exception as e:
            工具日志器.error(f"初始化内部函数工具失败: {str(e)}")



    def 获取状态(self) -> Dict[str, Any]:
        """获取工具管理器状态"""
        内部工具数量 = sum(1 for 配置 in self.工具配置.values() if 配置.get("类型") == "内部函数工具")

        return {
            "已初始化": self.已初始化,
            "注册工具数量": len(self.工具注册表),
            "内部函数工具数量": 内部工具数量,
            "LangChain可用": LANGCHAIN_AVAILABLE
        }

# 创建全局工具管理器实例
LangChain工具管理器实例 = LangChain工具管理器()
