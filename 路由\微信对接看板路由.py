import datetime
from typing import Any, List, Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel

import 状态
from 依赖项.认证 import 获取当前用户

# 导入数据层进行数据库操作
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据模型.响应模型 import 统一响应模型
from 日志 import 错误日志器

# 创建一个新的路由实例，专门用于看板功能
微信对接看板路由 = APIRouter()


class 看板数据请求(BaseModel):
    微信ID: int = Body(..., description="我方微信账号ID")
    产品名称: Optional[str] = Body(None, description="按产品名称筛选")
    对方微信号: Optional[str] = Body(None, description="按对方微信号筛选")


class 看板统计请求(BaseModel):
    微信ID: int = Body(..., description="我方微信账号ID")
    统计天数: int = Body(30, description="统计最近多少天的数据趋势")


class 列表数据请求(BaseModel):
    微信ID: int = Body(..., description="我方微信账号ID")
    页码: int = Body(1, description="页码，从1开始")
    每页条数: int = Body(10, description="每页显示的数据条数")
    产品名称: Optional[str] = Body(None, description="按产品名称筛选")
    对方微信号: Optional[str] = Body(None, description="按对方微信号筛选")
    意向状态: Optional[int] = Body(None, description="按意向状态筛选")
    样品状态: Optional[int] = Body(None, description="按样品状态筛选")
    排期状态: Optional[int] = Body(None, description="按排期状态筛选")
    排序字段: str = Body("更新时间", description="排序字段名")
    排序方式: str = Body("desc", description="排序方式：asc升序，desc降序")


def 分配泳道(进度):
    """根据进度状态，将其分配到对应的泳道ID"""
    意向状态 = 进度.get("意向状态", 0)
    样品状态 = 进度.get("样品状态", 0)
    排期状态 = 进度.get("排期状态", 0)

    if 意向状态 == -1:
        return "lane-5"  # 合作失败
    if 排期状态 in [1, 2]:
        return "lane-4"  # 排期合作
    if 样品状态 in [1, -2, 2, 3, -3, 4, -4, 5, -5]:
        return "lane-3"  # 样品流程
    if 意向状态 == 1:
        return "lane-2"  # 意向确认
    return "lane-1"  # 初步接触


@微信对接看板路由.post(
    "/board-data",
    summary="获取对接看板数据",
    description="根据我方微信ID，获取按状态分组的产品对接看板数据",
)
async def 获取看板数据(请求: 看板数据请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接看板所需的数据。
    数据将按对接流程的各个阶段（泳道）进行分组。
    """
    try:
        # 记录请求参数，方便调试
        错误日志器.info(
            f"获取看板数据 - 请求参数: 微信ID={请求.微信ID}, 用户ID={用户['id']}"
        )

        # 修正SQL查询，确保JOIN条件正确，并添加更详细的字段别名以防止混淆
        查询语句 = """
            SELECT 
                p.id AS 进度ID,
                p.合作产品ID,
                prod.产品名称,
                p.对方微信号ID,
                p.我方微信号ID,
                我方.微信号 AS 我方微信号,
                对方.微信号 AS 对方微信号,
                p.回复状态,
                p.意向状态,
                p.样品状态,
                p.排期状态,
                p.开播状态,
                p.销售额,
                p.排期开始时间,
                p.排期结束时间,
                p.创建时间,
                p.更新时间
            FROM 微信产品对接进度表 p
            LEFT JOIN 用户产品表 prod ON p.合作产品ID = prod.id
            LEFT JOIN 微信信息表 我方 ON p.我方微信号ID = 我方.id
            LEFT JOIN 微信信息表 对方 ON p.对方微信号ID = 对方.id
            WHERE p.我方微信号ID = %s
        """
        参数: List[Any] = [请求.微信ID]  # 支持混合类型的参数列表

        # 添加可选的用户ID筛选，避免数据混淆
        if 用户 and 用户.get("id"):
            查询语句 += " AND p.用户ID = %s"
            参数.append(用户["id"])

        if 请求.产品名称:
            查询语句 += " AND prod.产品名称 LIKE %s"
            参数.append(f"%{请求.产品名称}%")

        if 请求.对方微信号:
            查询语句 += " AND 对方.微信号 LIKE %s"
            参数.append(f"%{请求.对方微信号}%")

        # 添加排序以保证一致的结果顺序
        查询语句 += " ORDER BY p.更新时间 DESC"

        # 记录执行的SQL，方便调试
        错误日志器.info(f"获取看板数据 - SQL: {查询语句}, 参数: {参数}")

        # 增加错误处理并捕获SQL执行错误
        try:
            所有进度 = await 异步连接池实例.执行查询(查询语句, tuple(参数))
        except Exception as sql_err:
            错误日志器.error(f"SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.通用.服务器错误, f"数据库查询失败: {str(sql_err)}"
            )

        # 记录查询结果数量
        错误日志器.info(f"获取看板数据 - 查询结果数量: {len(所有进度)}")

        # 初始化泳道数据
        lanes = {
            "lane-1": {"id": "lane-1", "title": "初步接触", "cards": []},
            "lane-2": {"id": "lane-2", "title": "意向确认", "cards": []},
            "lane-3": {"id": "lane-3", "title": "样品流程", "cards": []},
            "lane-4": {"id": "lane-4", "title": "排期合作", "cards": []},
            "lane-5": {"id": "lane-5", "title": "合作失败", "cards": []},
        }

        # 遍历进度数据并添加到对应泳道
        for 进度 in 所有进度:
            try:
                # 使用微信号作为好友标识
                好友标识 = (
                    进度.get("对方微信号") or f"未知好友(ID:{进度.get('对方微信号ID')})"
                )

                # 确保进度ID存在
                进度ID = 进度.get("进度ID")
                if not 进度ID:
                    错误日志器.warning(f"发现无ID的进度数据: {进度}")
                    continue

                # 分配泳道
                lane_id = 分配泳道(进度)

                # 构建卡片数据
                card = {
                    "id": f"card-{进度ID}",
                    "title": 进度.get("产品名称", "未知产品"),
                    "owner": 好友标识,
                    "lastUpdate": 进度["更新时间"].strftime("%Y-%m-%d")
                    if 进度.get("更新时间")
                    else "N/A",
                    "details": 进度,  # 保留所有细节以便弹窗显示
                }

                # 添加到对应泳道
                if lane_id in lanes:
                    lanes[lane_id]["cards"].append(card)
                else:
                    错误日志器.warning(f"未知泳道ID: {lane_id}, 进度数据: {进度}")
            except Exception as card_err:
                错误日志器.error(
                    f"处理进度卡片数据时发生错误: {card_err}, 进度数据: {进度}",
                    exc_info=True,
                )
                # 继续处理其他卡片，不中断整个流程

        # 返回成功响应
        return 统一响应模型.成功(list(lanes.values()))

    except Exception as e:
        错误日志器.error(f"获取看板数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(状态.通用.服务器错误, f"服务器内部错误: {str(e)}")


@微信对接看板路由.post(
    "/board-list",
    summary="获取产品对接进度列表",
    description="分页获取产品对接进度列表，支持筛选和排序",
)
async def 获取列表数据(请求: 列表数据请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接进度列表，支持分页、筛选和排序功能。
    返回带有分页信息的列表数据。
    """
    try:
        错误日志器.info(f"获取列表数据 - 请求参数: {请求}")

        # 构建基础查询语句
        查询语句 = """
            SELECT 
                p.id AS 进度ID,
                p.合作产品ID,
                prod.产品名称,
                p.对方微信号ID,
                p.我方微信号ID,
                我方.微信号 AS 我方微信号,
                对方.微信号 AS 对方微信号,
                p.回复状态,
                p.意向状态,
                p.样品状态,
                p.排期状态,
                p.开播状态,
                p.销售额,
                p.排期开始时间,
                p.排期结束时间,
                p.创建时间,
                p.更新时间
            FROM 微信产品对接进度表 p
            LEFT JOIN 用户产品表 prod ON p.合作产品ID = prod.id
            LEFT JOIN 微信信息表 我方 ON p.我方微信号ID = 我方.id
            LEFT JOIN 微信信息表 对方 ON p.对方微信号ID = 对方.id
            WHERE p.我方微信号ID = %s
        """
        参数 = [请求.微信ID]  # 支持混合类型的参数列表

        # 添加用户ID筛选
        if 用户 and 用户.get("id"):
            查询语句 += " AND p.用户ID = %s"
            参数.append(用户["id"])

        # 添加可选筛选条件
        if 请求.产品名称:
            查询语句 += " AND prod.产品名称 LIKE %s"
            参数.append(f"%{请求.产品名称}%")

        if 请求.对方微信号:
            查询语句 += " AND 对方.微信号 LIKE %s"
            参数.append(f"%{请求.对方微信号}%")

        if 请求.意向状态 is not None:
            查询语句 += " AND p.意向状态 = %s"
            参数.append(请求.意向状态)

        if 请求.样品状态 is not None:
            查询语句 += " AND p.样品状态 = %s"
            参数.append(请求.样品状态)

        if 请求.排期状态 is not None:
            查询语句 += " AND p.排期状态 = %s"
            参数.append(请求.排期状态)

        # 获取总记录数
        计数查询 = f"SELECT COUNT(*) as 总数 FROM ({查询语句}) AS 子查询"

        # 添加排序和分页
        查询语句 += f" ORDER BY p.{请求.排序字段} {请求.排序方式.upper()}"
        查询语句 += " LIMIT $1 OFFSET $2"
        偏移量 = (请求.页码 - 1) * 请求.每页条数
        参数.append(请求.每页条数)
        参数.append(偏移量)

        try:
            # 执行计数查询
            计数结果 = await 异步连接池实例.执行查询(
                计数查询, tuple(参数[:-2])
            )  # 不包括LIMIT和OFFSET参数
            总数 = 计数结果[0]["总数"] if 计数结果 else 0

            # 执行分页查询
            列表结果 = await 异步连接池实例.执行查询(查询语句, tuple(参数))

            错误日志器.info(
                f"获取列表数据 - 查询结果: 总数={总数}, 当前页记录数={len(列表结果)}"
            )

            # 格式化分页数据
            分页数据 = {
                "列表": 列表结果,
                "总数": 总数,
                "页码": 请求.页码,
                "每页条数": 请求.每页条数,
                "总页数": (总数 + 请求.每页条数 - 1) // 请求.每页条数,  # 向上取整
            }

            return 统一响应模型.成功(分页数据)

        except Exception as sql_err:
            错误日志器.error(f"SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.通用.服务器错误, f"数据库查询失败: {str(sql_err)}"
            )

    except Exception as e:
        错误日志器.error(f"获取列表数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(状态.通用.服务器错误, f"服务器内部错误: {str(e)}")


@微信对接看板路由.post(
    "/board-stats",
    summary="获取看板统计数据",
    description="获取产品对接各状态的统计数据和趋势",
)
async def 获取看板统计数据(请求: 看板统计请求, 用户: dict = Depends(获取当前用户)):
    """
    获取产品对接看板的统计数据，包括：
    1. 各阶段状态的数量统计
    2. 各产品分类的分布
    3. 近期对接数据的趋势
    """
    try:
        错误日志器.info(
            f"获取看板统计数据 - 请求参数: 微信ID={请求.微信ID}, 用户ID={用户['id']}"
        )

        # 1. 查询各状态数量统计
        状态统计查询 = """
            SELECT 
                '意向状态' AS 状态类型,
                意向状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
            GROUP BY 
                意向状态
            UNION ALL
            SELECT 
                '样品状态' AS 状态类型,
                样品状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
                AND 样品状态 IS NOT NULL
            GROUP BY 
                样品状态
            UNION ALL
            SELECT 
                '排期状态' AS 状态类型,
                排期状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
                AND 排期状态 IS NOT NULL
            GROUP BY 
                排期状态
            UNION ALL
            SELECT 
                '开播状态' AS 状态类型,
                开播状态 AS 状态值,
                COUNT(*) AS 数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
                AND 开播状态 IS NOT NULL
            GROUP BY 
                开播状态
        """
        状态统计参数 = [请求.微信ID, 用户["id"]] * 4  # 每个子查询都需要这两个参数

        # 2. 查询各阶段完成情况
        阶段统计查询 = """
            SELECT
                COUNT(*) AS 总进度数量,
                SUM(CASE WHEN 意向状态 = 1 THEN 1 ELSE 0 END) AS 意向确认数量,
                SUM(CASE WHEN 样品状态 = 1 THEN 1 ELSE 0 END) AS 样品确认数量,
                SUM(CASE WHEN 排期状态 = 1 THEN 1 ELSE 0 END) AS 排期确认数量,
                SUM(CASE WHEN 开播状态 = 1 THEN 1 ELSE 0 END) AS 开播完成数量,
                SUM(CASE WHEN 更新时间 >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) AS 近7天更新数量,
                SUM(CASE WHEN 创建时间 >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) AS 近30天新增数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
        """
        阶段统计参数 = [请求.微信ID, 用户["id"]]

        # 3. 查询产品分类分布
        分类统计查询 = """
            SELECT 
                JSON_UNQUOTE(JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]')) AS 产品分类,
                COUNT(*) AS 进度数量,
                SUM(CASE WHEN p.意向状态 = 1 THEN 1 ELSE 0 END) AS 意向确认数量,
                SUM(CASE WHEN p.样品状态 = 1 THEN 1 ELSE 0 END) AS 样品确认数量
            FROM 
                微信产品对接进度表 p
            JOIN 
                用户产品表 prod ON p.合作产品ID = prod.id
            WHERE 
                p.我方微信号ID = %s
                AND p.用户ID = %s
                AND JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]') IS NOT NULL
            GROUP BY 
                JSON_UNQUOTE(JSON_EXTRACT(prod.产品信息, '$."产品分类"[0]'))
            ORDER BY 
                进度数量 DESC
        """
        分类统计参数 = [请求.微信ID, 用户["id"]]

        # 4. 查询近期趋势数据
        今天 = datetime.datetime.now().date()
        开始日期 = 今天 - datetime.timedelta(days=请求.统计天数)

        趋势查询 = """
            SELECT 
                DATE(创建时间) AS 日期,
                COUNT(*) AS 新增对接数量
            FROM 
                微信产品对接进度表
            WHERE 
                我方微信号ID = %s
                AND 用户ID = %s
                AND 创建时间 >= %s
            GROUP BY 
                DATE(创建时间)
            ORDER BY 
                日期
        """
        趋势参数 = [请求.微信ID, 用户["id"], 开始日期]

        # 执行所有查询
        try:
            状态统计结果 = await 异步连接池实例.执行查询(
                状态统计查询, tuple(状态统计参数)
            )
            阶段统计结果 = await 异步连接池实例.执行查询(
                阶段统计查询, tuple(阶段统计参数)
            )
            分类统计结果 = await 异步连接池实例.执行查询(
                分类统计查询, tuple(分类统计参数)
            )
            趋势结果 = await 异步连接池实例.执行查询(趋势查询, tuple(趋势参数))
        except Exception as sql_err:
            错误日志器.error(f"统计数据SQL执行错误: {sql_err}", exc_info=True)
            return 统一响应模型.失败(
                状态.通用.服务器错误, f"数据库查询失败: {str(sql_err)}"
            )

        # 处理状态统计结果
        状态统计 = {"意向状态": {}, "样品状态": {}, "排期状态": {}, "开播状态": {}}

        for 统计 in 状态统计结果:
            状态类型 = 统计["状态类型"]
            状态值 = 统计["状态值"]
            数量 = 统计["数量"]

            if 状态类型 not in 状态统计:
                状态统计[状态类型] = {}

            状态统计[状态类型][str(状态值)] = 数量

        # 处理阶段统计结果
        阶段统计 = (
            阶段统计结果[0]
            if 阶段统计结果
            else {
                "总进度数量": 0,
                "意向确认数量": 0,
                "样品确认数量": 0,
                "排期确认数量": 0,
                "开播完成数量": 0,
                "近7天更新数量": 0,
                "近30天新增数量": 0,
            }
        )

        # 处理分类统计结果
        分类统计 = []
        for 分类 in 分类统计结果:
            分类统计.append(
                {
                    "产品分类": 分类["产品分类"],
                    "进度数量": 分类["进度数量"],
                    "意向确认数量": 分类["意向确认数量"],
                    "样品确认数量": 分类["样品确认数量"],
                }
            )

        # 处理趋势数据，填充没有数据的日期
        趋势数据 = {}
        for 数据 in 趋势结果:
            日期字符串 = 数据["日期"].strftime("%Y-%m-%d")
            趋势数据[日期字符串] = 数据["新增对接数量"]

        # 填充所有日期
        完整趋势 = []
        当前日期 = 开始日期
        while 当前日期 <= 今天:
            日期字符串 = 当前日期.strftime("%Y-%m-%d")
            完整趋势.append(
                {"日期": 日期字符串, "新增对接数量": 趋势数据.get(日期字符串, 0)}
            )
            当前日期 += datetime.timedelta(days=1)

        # 组装结果
        统计结果 = {
            "状态统计": 状态统计,
            "阶段统计": 阶段统计,
            "分类统计": 分类统计,
            "趋势数据": 完整趋势,
        }

        return 统一响应模型.成功(统计结果)

    except Exception as e:
        错误日志器.error(f"获取看板统计数据时发生错误: {e}", exc_info=True)
        return 统一响应模型.失败(状态.通用.服务器错误, f"服务器内部错误: {str(e)}")
